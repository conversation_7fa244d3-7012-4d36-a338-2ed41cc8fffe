"use strict";exports.id=100,exports.ids=[100],exports.modules={16386:(e,r,t)=>{t.d(r,{A:()=>h});var a=t(60154);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),l=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},d=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),n=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:i="",children:l,iconNode:o,...h},x)=>(0,a.createElement)("svg",{ref:x,...c,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:d("lucide",i),...!l&&!n(h)&&{"aria-hidden":"true"},...h},[...o.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(l)?l:[l]])),h=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...i},n)=>(0,a.createElement)(o,{ref:n,iconNode:r,className:d(`lucide-${s(l(e))}`,`lucide-${e}`,t),...i}));return t.displayName=l(e),t}},35545:(e,r,t)=>{t.d(r,{AutoOrderPrefillList:()=>f});var a=t(43197),s=t(14824),i=t(416),l=t(39832),d=t(64357),n=t(55751),c=t(45806);let o=(0,t(44736).A)("list-filter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);var h=t(64020),x=t(49040),m=t(16044),p=t(24017);p.callServer,p.findSourceMapURL;var u=t(51948),j=t(95681);function f({userId:e}){let[r,t]=(0,s.useState)([]),[p,f]=(0,s.useState)(!0),y=(0,u.U)("(min-width: 768px)");return p?(0,a.jsx)(i.Zp,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin text-primary"})})}):r.length?y?(0,a.jsx)(i.Zp,{children:(0,a.jsx)(n.F,{className:"h-[calc(100vh-400px)]",children:(0,a.jsxs)(l.XI,{children:[(0,a.jsx)(l.A0,{children:(0,a.jsxs)(l.Hj,{children:[(0,a.jsx)(l.nd,{children:"Symbol"}),(0,a.jsx)(l.nd,{children:"Action"}),(0,a.jsx)(l.nd,{children:"Quantity"}),(0,a.jsx)(l.nd,{children:"Price"}),(0,a.jsx)(l.nd,{children:"Status"}),(0,a.jsx)(l.nd,{children:"Created"})]})}),(0,a.jsx)(l.BF,{children:r.map((e,r)=>(0,a.jsxs)(l.Hj,{children:[(0,a.jsx)(l.nA,{className:"font-medium",children:e.ticker}),(0,a.jsx)(l.nA,{children:(0,a.jsx)(d.E,{variant:"BUY"===e.action?"default":"destructive",children:e.action})}),(0,a.jsx)(l.nA,{children:e.quantity}),(0,a.jsxs)(l.nA,{children:["$",e.price.toFixed(2)]}),(0,a.jsx)(l.nA,{children:(0,a.jsx)(d.E,{variant:e.deleted_at?"secondary":"success",children:e.deleted_at?"Deleted":"Pending"})}),(0,a.jsx)(l.nA,{children:new Date(e.created_at).toLocaleDateString()})]},r))})]})})}):(0,a.jsx)(n.F,{className:"h-[calc(100vh-400px)]",children:(0,a.jsx)("div",{className:"space-y-4 p-4",children:r.map((e,r)=>(0,a.jsxs)(i.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-lg font-semibold",children:e.ticker}),(0,a.jsx)(d.E,{variant:"BUY"===e.action?"default":"destructive",children:e.action})]}),(0,a.jsx)(d.E,{variant:e.deleted_at?"secondary":"success",children:e.deleted_at?"Deleted":"Pending"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"})," Quantity"]}),(0,a.jsx)("div",{className:"font-medium",children:e.quantity})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"})," Price"]}),(0,a.jsxs)("div",{className:"font-medium",children:["$",e.price.toFixed(2)]})]})]}),(0,a.jsx)(j.w,{className:"my-4"}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]})]},r))})}):(0,a.jsx)(i.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center py-6 text-muted-foreground",children:[(0,a.jsx)(o,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h3",{className:"font-medium mb-1",children:"No Auto Order Prefills"}),(0,a.jsx)("p",{className:"text-sm",children:"Enable auto order prefill in your account settings to use this feature"})]})})}},49040:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(44736).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},53471:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(16386).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},57657:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(16386).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},62806:(e,r,t)=>{t.d(r,{i:()=>l});var a=t(20349);t(71241);var s=t(31595),i=t(80336);async function l(e){if(!e)throw Error("User ID is required");try{let r=await (0,s.V)(e),t=r?.settings?.enableAutoOrderPrefill??!1;if(!t)return[];return(await i.A.autoOrderPrefill.findMany({where:{user_id:e,deleted_at:null},orderBy:{created_at:"desc"}})).map(e=>({...e,isEnabled:t}))}catch(e){throw console.error("[FETCH_AUTO_ORDER_PREFILL_ERROR]",e),Error(e instanceof Error?e.message:"Failed to fetch auto order prefill records")}}(0,t(68785).D)([l]),(0,a.A)(l,"409a303cc9c9a1946c183a09b48b42bcabb7822fd3",null)},95681:(e,r,t)=>{t.d(r,{w:()=>o});var a=t(43197),s=t(14824),i=t(2185),l="horizontal",d=["horizontal","vertical"],n=s.forwardRef((e,r)=>{var t;let{decorative:s,orientation:n=l,...c}=e,o=(t=n,d.includes(t))?n:l;return(0,a.jsx)(i.sG.div,{"data-orientation":o,...s?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:r})});n.displayName="Separator";var c=t(51001);let o=s.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...s},i)=>(0,a.jsx)(n,{ref:i,decorative:t,orientation:r,className:(0,c.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...s}));o.displayName=n.displayName}};