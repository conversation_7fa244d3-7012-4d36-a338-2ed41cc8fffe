exports.id=7566,exports.ids=[7566],exports.modules={63667:(e,t,r)=>{"use strict";var i=Object.create,n=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,u=(e,t)=>()=>(e&&(t=e(e=0)),t),d=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),c=(e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})},f=(e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of a(t))l.call(e,o)||o===r||n(e,o,{get:()=>t[o],enumerable:!(i=s(t,o))||i.enumerable});return e},p=(e,t,r)=>(r=null!=e?i(o(e)):{},f(!t&&e&&e.__esModule?r:n(r,"default",{value:e,enumerable:!0}),e)),h=d((e,t)=>{t.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":1===e.length?"-":"--",i=t.indexOf(r+e),n=t.indexOf("--");return -1!==i&&(-1===n||i<n)}}),m=d((e,t)=>{var i,n=r(48161),s=r(7066),a=h(),{env:o}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function u(e,t){if(0===i)return 0;if(a("color=16m")||a("color=full")||a("color=truecolor"))return 3;if(a("color=256"))return 2;if(e&&!t&&void 0===i)return 0;let r=i||0;if("dumb"===o.TERM)return r;if("win32"===process.platform){let e=n.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in o)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in o)||"codeship"===o.CI_NAME?1:r;if("TEAMCITY_VERSION"in o)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(o.TEAMCITY_VERSION);if("truecolor"===o.COLORTERM)return 3;if("TERM_PROGRAM"in o){let e=parseInt((o.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(o.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(o.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(o.TERM)||"COLORTERM"in o?1:r}a("no-color")||a("no-colors")||a("color=false")||a("color=never")?i=0:(a("color")||a("colors")||a("color=true")||a("color=always"))&&(i=1),"FORCE_COLOR"in o&&(i="true"===o.FORCE_COLOR?1:"false"===o.FORCE_COLOR?0:0===o.FORCE_COLOR.length?1:Math.min(parseInt(o.FORCE_COLOR,10),3)),t.exports={supportsColor:function(e){return l(u(e,e&&e.isTTY))},stdout:l(u(!0,s.isatty(1))),stderr:l(u(!0,s.isatty(2)))}}),g=d((e,t)=>{var r=m(),i=h();function n(e){if(/^\d{3,4}$/.test(e)){let t=/(\d{1,2})(\d{2})/.exec(e)||[];return{major:0,minor:parseInt(t[1],10),patch:parseInt(t[2],10)}}let t=(e||"").split(".").map(e=>parseInt(e,10));return{major:t[0],minor:t[1],patch:t[2]}}function s(e){let{CI:t,FORCE_HYPERLINK:s,NETLIFY:a,TEAMCITY_VERSION:o,TERM_PROGRAM:l,TERM_PROGRAM_VERSION:u,VTE_VERSION:d,TERM:c}=process.env;if(s)return!(s.length>0&&0===parseInt(s,10));if(i("no-hyperlink")||i("no-hyperlinks")||i("hyperlink=false")||i("hyperlink=never"))return!1;if(i("hyperlink=true")||i("hyperlink=always")||a)return!0;if(!r.supportsColor(e)||e&&!e.isTTY)return!1;if("WT_SESSION"in process.env)return!0;if("win32"===process.platform||t||o)return!1;if(l){let e=n(u||"");switch(l){case"iTerm.app":return 3===e.major?e.minor>=1:e.major>3;case"WezTerm":return e.major>=0x1343cac;case"vscode":return e.major>1||1===e.major&&e.minor>=72;case"ghostty":return!0}}if(d){if("0.50.0"===d)return!1;let e=n(d);return e.major>0||e.minor>=50}return"alacritty"===c}t.exports={supportsHyperlink:s,stdout:s(process.stdout),stderr:s(process.stderr)}}),y=d((e,t)=>{t.exports={name:"@prisma/internals",version:"6.9.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/schema-engine-wasm":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}}),b=d((e,t)=>{t.exports={name:"@prisma/engines-version",version:"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"81e4af48011447c3cc503a190e86995b66d2a28e"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}}),v=d(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.enginesVersion=void 0,e.enginesVersion=b().prisma.enginesVersion}),w=d((e,t)=>{t.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((e,t)=>Math.min(e,t.length),1/0):0}}),_=d((e,t)=>{t.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},"string"!=typeof e)throw TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if("number"!=typeof t)throw TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if("string"!=typeof r.indent)throw TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(0===t)return e;let i=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(i,r.indent.repeat(t))}}),E=d((e,t)=>{t.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")}),x=d((e,t)=>{var r=E();t.exports=e=>"string"==typeof e?e.replace(r(),""):e}),P=d((e,t)=>{t.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}}),T=d((e,t)=>{var i=r(73024),n=r(76760),s=r(48161),a=r(77598),o=P().version,l=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function u(e){console.log(`[dotenv@${o}][DEBUG] ${e}`)}function d(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function c(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)i.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=n.resolve(process.cwd(),".env.vault");return i.existsSync(t)?t:null}function f(e){return"~"===e[0]?n.join(s.homedir(),e.slice(1)):e}var p={configDotenv:function(e){let t=n.resolve(process.cwd(),".env"),r="utf8",s=!!(e&&e.debug);e&&e.encoding?r=e.encoding:s&&u("No encoding is specified. UTF-8 is used by default");let a=[t];if(e&&e.path)if(Array.isArray(e.path))for(let t of(a=[],e.path))a.push(f(t));else a=[f(e.path)];let o,l={};for(let t of a)try{let n=p.parse(i.readFileSync(t,{encoding:r}));p.populate(l,n,e)}catch(e){s&&u(`Failed to load ${t} ${e.message}`),o=e}let d=process.env;return e&&null!=e.processEnv&&(d=e.processEnv),p.populate(d,l,e),o?{parsed:l,error:o}:{parsed:l}},_configVault:function(e){e&&e.debug&&u("Loading env from encrypted .env.vault");let t=p._parseVault(e),r=process.env;return e&&null!=e.processEnv&&(r=e.processEnv),p.populate(r,t,e),{parsed:t}},_parseVault:function(e){let t=c(e),r=p.configDotenv({path:t});if(!r.parsed){let e=Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw e.code="MISSING_DATA",e}let i=d(e).split(","),n=i.length,s;for(let e=0;e<n;e++)try{let t=i[e].trim(),n=function(e,t){let r;try{r=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let i=r.password;if(!i){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let n=r.searchParams.get("environment");if(!n){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let s=`DOTENV_VAULT_${n.toUpperCase()}`,a=e.parsed[s];if(!a){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:a,key:i}}(r,t);s=p.decrypt(n.ciphertext,n.key);break}catch(t){if(e+1>=n)throw t}return p.parse(s)},config:function(e){var t;if(0===d(e).length)return p.configDotenv(e);let r=c(e);return r?p._configVault(e):(t=`You set DOTENV_KEY but you are missing a .env.vault file at ${r}. Did you forget to build it?`,console.log(`[dotenv@${o}][WARN] ${t}`),p.configDotenv(e))},decrypt:function(e,t){let r=Buffer.from(t.slice(-64),"hex"),i=Buffer.from(e,"base64"),n=i.subarray(0,12),s=i.subarray(-16);i=i.subarray(12,-16);try{let e=a.createDecipheriv("aes-256-gcm",r,n);return e.setAuthTag(s),`${e.update(i)}${e.final()}`}catch(i){let e=i instanceof RangeError,t="Invalid key length"===i.message,r="Unsupported state or unable to authenticate data"===i.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(r){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw i}},parse:function(e){let t,r={},i=e.toString();for(i=i.replace(/\r\n?/mg,`
`);null!=(t=l.exec(i));){let e=t[1],i=t[2]||"",n=(i=i.trim())[0];i=i.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===n&&(i=(i=i.replace(/\\n/g,`
`)).replace(/\\r/g,"\r")),r[e]=i}return r},populate:function(e,t,r={}){let i=!!(r&&r.debug),n=!!(r&&r.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let r of Object.keys(t))Object.prototype.hasOwnProperty.call(e,r)?(!0===n&&(e[r]=t[r]),i&&u(!0===n?`"${r}" is already defined and WAS overwritten`:`"${r}" is already defined and was NOT overwritten`)):e[r]=t[r]}};t.exports.configDotenv=p.configDotenv,t.exports._configVault=p._configVault,t.exports._parseVault=p._parseVault,t.exports.config=p.config,t.exports.decrypt=p.decrypt,t.exports.parse=p.parse,t.exports.populate=p.populate,t.exports=p}),S=d((e,t)=>{t.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`);for(let t of["body","title","labels","template","milestone","assignee","projects"]){let i=e[t];if(void 0!==i){if("labels"===t||"projects"===t){if(!Array.isArray(i))throw TypeError(`The \`${t}\` option should be an array`);i=i.join(",")}r.searchParams.set(t,i)}}return r.toString()},t.exports.default=t.exports}),R=d((e,t)=>{t.exports=function(){function e(e,t,r,i,n){return e<t||r<t?e>r?r+1:e+1:i===n?t:t+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var i=t;t=r,r=i}for(var n=t.length,s=r.length;n>0&&t.charCodeAt(n-1)===r.charCodeAt(s-1);)n--,s--;for(var a=0;a<n&&t.charCodeAt(a)===r.charCodeAt(a);)a++;if(n-=a,s-=a,0===n||s<3)return s;var o,l,u,d,c,f,p,h,m,g,y,b,v=0,w=[];for(o=0;o<n;o++)w.push(o+1),w.push(t.charCodeAt(a+o));for(var _=w.length-1;v<s-3;)for(m=r.charCodeAt(a+(l=v)),g=r.charCodeAt(a+(u=v+1)),y=r.charCodeAt(a+(d=v+2)),b=r.charCodeAt(a+(c=v+3)),f=v+=4,o=0;o<_;o+=2)l=e(p=w[o],l,u,m,h=w[o+1]),u=e(l,u,d,g,h),d=e(u,d,c,y,h),f=e(d,c,f,b,h),w[o]=f,c=d,d=u,u=l,l=p;for(;v<s;)for(m=r.charCodeAt(a+(l=v)),f=++v,o=0;o<_;o+=2)p=w[o],w[o]=f=e(p,l,f,m,w[o+1]),l=p;return f}}()}),k=u(()=>{}),O=u(()=>{}),A={};c(A,{DMMF:()=>i0,Debug:()=>eE,Decimal:()=>iB,Extensions:()=>N,MetricsClient:()=>n6,PrismaClientInitializationError:()=>rm,PrismaClientKnownRequestError:()=>rg,PrismaClientRustPanicError:()=>ry,PrismaClientUnknownRequestError:()=>rb,PrismaClientValidationError:()=>rv,Public:()=>D,Sql:()=>sl,createParam:()=>nJ,defineDmmfProperty:()=>n9,deserializeJsonResponse:()=>iH,deserializeRawResult:()=>oI,dmmfToRuntimeDataModel:()=>iQ,empty:()=>sc,getPrismaClient:()=>oY,getRuntime:()=>ae,join:()=>su,makeStrictEnum:()=>o0,makeTypedQueryFactory:()=>st,objectEnumValues:()=>nD,raw:()=>sd,serializeJsonQuery:()=>n0,skip:()=>nz,sqltag:()=>sf,warnEnvConflicts:()=>o1,warnOnce:()=>rh}),e.exports=f(n({},"__esModule",{value:!0}),A);var N={};function $(e){return"function"==typeof e?e:t=>t.$extends(e)}function q(e){return e}c(N,{defineExtension:()=>$,getExtensionContext:()=>q});var D={};function I(...e){return e=>e}c(D,{validator:()=>I});var V={};c(V,{$:()=>M,bgBlack:()=>el,bgBlue:()=>ef,bgCyan:()=>eh,bgGreen:()=>ed,bgMagenta:()=>ep,bgRed:()=>eu,bgWhite:()=>em,bgYellow:()=>ec,black:()=>Z,blue:()=>er,bold:()=>H,cyan:()=>en,dim:()=>W,gray:()=>ea,green:()=>ee,grey:()=>eo,hidden:()=>z,inverse:()=>Q,italic:()=>J,magenta:()=>ei,red:()=>X,reset:()=>B,strikethrough:()=>Y,underline:()=>K,white:()=>es,yellow:()=>et});var U,F,C,j,L=!0;"u">typeof process&&({FORCE_COLOR:U,NODE_DISABLE_COLORS:F,NO_COLOR:C,TERM:j}=process.env||{},L=process.stdout&&process.stdout.isTTY);var M={enabled:!F&&null==C&&"dumb"!==j&&(null!=U&&"0"!==U||L)};function G(e,t){let r=RegExp(`\\x1b\\[${t}m`,"g"),i=`\x1b[${e}m`,n=`\x1b[${t}m`;return function(e){return M.enabled&&null!=e?i+(~(""+e).indexOf(n)?e.replace(r,n+i):e)+n:e}}var B=G(0,0),H=G(1,22),W=G(2,22),J=G(3,23),K=G(4,24),Q=G(7,27),z=G(8,28),Y=G(9,29),Z=G(30,39),X=G(31,39),ee=G(32,39),et=G(33,39),er=G(34,39),ei=G(35,39),en=G(36,39),es=G(37,39),ea=G(90,39),eo=G(90,39),el=G(40,49),eu=G(41,49),ed=G(42,49),ec=G(43,49),ef=G(44,49),ep=G(45,49),eh=G(46,49),em=G(47,49),eg=["green","yellow","blue","magenta","cyan","red"],ey=[],eb=Date.now(),ev=0,ew="u">typeof process?process.env:{};globalThis.DEBUG??=ew.DEBUG??"",globalThis.DEBUG_COLORS??=!ew.DEBUG_COLORS||"true"===ew.DEBUG_COLORS;var e_={enable(e){"string"==typeof e&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(e=>e.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(t=>""!==t&&"-"!==t[0]&&e.match(RegExp(t.split("*").join(".*")+"$"))),i=t.some(t=>""!==t&&"-"===t[0]&&e.match(RegExp(t.slice(1).split("*").join(".*")+"$")));return r&&!i},log:(...e)=>{let[t,r,...i]=e;(console.warn??console.log)(`${t} ${r}`,...i)},formatters:{}},eE=new Proxy(function(e){let t={color:eg[ev++%eg.length],enabled:e_.enabled(e),namespace:e,log:e_.log,extend:()=>{}};return new Proxy((...e)=>{let{enabled:r,namespace:i,color:n,log:s}=t;if(0!==e.length&&ey.push([i,...e]),ey.length>100&&ey.shift(),e_.enabled(i)||r){let t=e.map(e=>"string"==typeof e?e:function(e,t=2){let r=new Set;return JSON.stringify(e,(e,t)=>{if("object"==typeof t&&null!==t){if(r.has(t))return"[Circular *]";r.add(t)}else if("bigint"==typeof t)return t.toString();return t},t)}(e)),r=`+${Date.now()-eb}ms`;eb=Date.now(),globalThis.DEBUG_COLORS?s(V[n](H(i)),...t,V[n](r)):s(i,...t,r)}},{get:(e,r)=>t[r],set:(e,r,i)=>t[r]=i})},{get:(e,t)=>e_[t],set:(e,t,r)=>e_[t]=r}),ex=p(r(73024)),eP="libquery_engine",eT=p(r(31421)),eS=p(r(51455)),eR=p(r(48161)),ek=Symbol.for("@ts-pattern/matcher"),eO=Symbol.for("@ts-pattern/isVariadic"),eA="@ts-pattern/anonymous-select-key",eN=e=>!!(e&&"object"==typeof e),e$=e=>e&&!!e[ek],eq=(e,t,r)=>{if(e$(e)){let{matched:i,selections:n}=e[ek]().match(t);return i&&n&&Object.keys(n).forEach(e=>r(e,n[e])),i}if(eN(e)){if(!eN(t))return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let i=[],n=[],s=[];for(let t of e.keys()){let r=e[t];e$(r)&&r[eO]?s.push(r):s.length?n.push(r):i.push(r)}if(s.length){if(s.length>1)throw Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<i.length+n.length)return!1;let e=t.slice(0,i.length),a=0===n.length?[]:t.slice(-n.length),o=t.slice(i.length,0===n.length?1/0:-n.length);return i.every((t,i)=>eq(t,e[i],r))&&n.every((e,t)=>eq(e,a[t],r))&&(0===s.length||eq(s[0],o,r))}return e.length===t.length&&e.every((e,i)=>eq(e,t[i],r))}return Reflect.ownKeys(e).every(i=>{let n=e[i];return(i in t||e$(n)&&"optional"===n[ek]().matcherType)&&eq(n,t[i],r)})}return Object.is(t,e)},eD=e=>{var t,r,i;return eN(e)?e$(e)?null!=(t=null==(r=(i=e[ek]()).getSelectionKeys)?void 0:r.call(i))?t:[]:Array.isArray(e)?eI(e,eD):eI(Object.values(e),eD):[]},eI=(e,t)=>e.reduce((e,r)=>e.concat(t(r)),[]);function eV(e){return Object.assign(e,{optional:()=>{var t;return t=e,eV({[ek]:()=>({match:e=>{let r={},i=(e,t)=>{r[e]=t};return void 0===e?(eD(t).forEach(e=>i(e,void 0)),{matched:!0,selections:r}):{matched:eq(t,e,i),selections:r}},getSelectionKeys:()=>eD(t),matcherType:"optional"})})},and:t=>eU(e,t),or:t=>(function(...e){return eV({[ek]:()=>({match:t=>{let r={},i=(e,t)=>{r[e]=t};return eI(e,eD).forEach(e=>i(e,void 0)),{matched:e.some(e=>eq(e,t,i)),selections:r}},getSelectionKeys:()=>eI(e,eD),matcherType:"or"})})})(e,t),select:t=>void 0===t?eC(e):eC(t,e)})}function eU(...e){return eV({[ek]:()=>({match:t=>{let r={},i=(e,t)=>{r[e]=t};return{matched:e.every(e=>eq(e,t,i)),selections:r}},getSelectionKeys:()=>eI(e,eD),matcherType:"and"})})}function eF(e){return{[ek]:()=>({match:t=>({matched:!!e(t)})})}}function eC(...e){let t="string"==typeof e[0]?e[0]:void 0,r=2===e.length?e[1]:"string"==typeof e[0]?void 0:e[0];return eV({[ek]:()=>({match:e=>{let i={[t??eA]:e};return{matched:void 0===r||eq(r,e,(e,t)=>{i[e]=t}),selections:i}},getSelectionKeys:()=>[t??eA].concat(void 0===r?[]:eD(r))})})}function ej(e){return"number"==typeof e}function eL(e){return"string"==typeof e}function eM(e){return"bigint"==typeof e}eV(eF(function(e){return!0}));var eG=e=>Object.assign(eV(e),{startsWith:t=>eG(eU(e,eF(e=>eL(e)&&e.startsWith(t)))),endsWith:t=>eG(eU(e,eF(e=>eL(e)&&e.endsWith(t)))),minLength:t=>eG(eU(e,eF(e=>eL(e)&&e.length>=t))),length:t=>eG(eU(e,eF(e=>eL(e)&&e.length===t))),maxLength:t=>eG(eU(e,eF(e=>eL(e)&&e.length<=t))),includes:t=>eG(eU(e,eF(e=>eL(e)&&e.includes(t)))),regex:t=>eG(eU(e,eF(e=>eL(e)&&!!e.match(t))))}),eB=(eG(eF(eL)),e=>Object.assign(eV(e),{between:(t,r)=>eB(eU(e,eF(e=>ej(e)&&t<=e&&r>=e))),lt:t=>eB(eU(e,eF(e=>ej(e)&&e<t))),gt:t=>eB(eU(e,eF(e=>ej(e)&&e>t))),lte:t=>eB(eU(e,eF(e=>ej(e)&&e<=t))),gte:t=>eB(eU(e,eF(e=>ej(e)&&e>=t))),int:()=>eB(eU(e,eF(e=>ej(e)&&Number.isInteger(e)))),finite:()=>eB(eU(e,eF(e=>ej(e)&&Number.isFinite(e)))),positive:()=>eB(eU(e,eF(e=>ej(e)&&e>0))),negative:()=>eB(eU(e,eF(e=>ej(e)&&e<0)))})),eH=(eB(eF(ej)),e=>Object.assign(eV(e),{between:(t,r)=>eH(eU(e,eF(e=>eM(e)&&t<=e&&r>=e))),lt:t=>eH(eU(e,eF(e=>eM(e)&&e<t))),gt:t=>eH(eU(e,eF(e=>eM(e)&&e>t))),lte:t=>eH(eU(e,eF(e=>eM(e)&&e<=t))),gte:t=>eH(eU(e,eF(e=>eM(e)&&e>=t))),positive:()=>eH(eU(e,eF(e=>eM(e)&&e>0))),negative:()=>eH(eU(e,eF(e=>eM(e)&&e<0)))}));eH(eF(eM)),eV(eF(function(e){return"boolean"==typeof e})),eV(eF(function(e){return"symbol"==typeof e})),eV(eF(function(e){return null==e})),eV(eF(function(e){return null!=e}));var eW=class extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch{t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}},eJ={matched:!1,value:void 0};function eK(e){return new eQ(e,eJ)}var eQ=class e{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...t){if(this.state.matched)return this;let r=t[t.length-1],i=[t[0]],n;3===t.length&&"function"==typeof t[1]?n=t[1]:t.length>2&&i.push(...t.slice(1,t.length-1));let s=!1,a={},o=(e,t)=>{s=!0,a[e]=t},l=i.some(e=>eq(e,this.input,o))&&(!n||n(this.input))?{matched:!0,value:r(s?eA in a?a[eA]:a:this.input,this.input)}:eJ;return new e(this.input,l)}when(t,r){if(this.state.matched)return this;let i=!!t(this.input);return new e(this.input,i?{matched:!0,value:r(this.input,this.input)}:eJ)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new eW(this.input)}run(){return this.exhaustive()}returnType(){return this}},ez=r(57975),eY={warn:et("prisma:warn")},eZ={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function eX(e,...t){eZ.warn()&&console.warn(`${eY.warn} ${e}`,...t)}var e0=(0,ez.promisify)(eT.default.exec),e1=eE("prisma:get-platform"),e2=["1.0.x","1.1.x","3.0.x"];async function e3(){let e=eR.default.platform(),t=process.arch;if("freebsd"===e){let e=await ts("freebsd-version");if(e&&e.trim().length>0){let r=/^(\d+)\.?/.exec(e);if(r)return{platform:"freebsd",targetDistro:`freebsd${r[1]}`,arch:t}}}if("linux"!==e)return{platform:e,arch:t};let r=await e4(),i=await ta(),n=eK({arch:t,archFromUname:i,familyDistro:r.familyDistro}).with({familyDistro:"musl"},()=>(e1('Trying platform-specific paths for "alpine"'),["/lib","/usr/lib"])).with({familyDistro:"debian"},({archFromUname:e})=>(e1('Trying platform-specific paths for "debian" (and "ubuntu")'),[`/usr/lib/${e}-linux-gnu`,`/lib/${e}-linux-gnu`])).with({familyDistro:"rhel"},()=>(e1('Trying platform-specific paths for "rhel"'),["/lib64","/usr/lib64"])).otherwise(({familyDistro:e,arch:t,archFromUname:r})=>(e1(`Don't know any platform-specific paths for "${e}" on ${t} (${r})`),[])),{libssl:s}=await e7(n);return{platform:"linux",libssl:s,arch:t,archFromUname:i,...r}}async function e4(){try{var e;let t,r,i,n;return e=await eS.default.readFile("/etc/os-release",{encoding:"utf-8"}),r=(t=/^ID="?([^"\n]*)"?$/im.exec(e))&&t[1]&&t[1].toLowerCase()||"",i=/^ID_LIKE="?([^"\n]*)"?$/im.exec(e),n=eK({id:r,idLike:i&&i[1]&&i[1].toLowerCase()||""}).with({id:"alpine"},({id:e})=>({targetDistro:"musl",familyDistro:e,originalDistro:e})).with({id:"raspbian"},({id:e})=>({targetDistro:"arm",familyDistro:"debian",originalDistro:e})).with({id:"nixos"},({id:e})=>({targetDistro:"nixos",originalDistro:e,familyDistro:"nixos"})).with({id:"debian"},{id:"ubuntu"},({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).with({id:"rhel"},{id:"centos"},{id:"fedora"},({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).when(({idLike:e})=>e.includes("debian")||e.includes("ubuntu"),({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).when(({idLike:e})=>"arch"===r||e.includes("arch"),({id:e})=>({targetDistro:"debian",familyDistro:"arch",originalDistro:e})).when(({idLike:e})=>e.includes("centos")||e.includes("fedora")||e.includes("rhel")||e.includes("suse"),({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).otherwise(({id:e})=>({targetDistro:void 0,familyDistro:void 0,originalDistro:e})),e1(`Found distro info:
${JSON.stringify(n,null,2)}`),n}catch{return{targetDistro:void 0,familyDistro:void 0,originalDistro:void 0}}}function e6(e){let t=/libssl\.so\.(\d)(\.\d)?/.exec(e);if(t)return e9(`${t[1]}${t[2]??".0"}.x`)}function e9(e){let t=(()=>{if(to(e))return e;let t=e.split(".");return t[1]="0",t.join(".")})();if(e2.includes(t))return t}async function e7(e){let t=await e5(e);if(t){e1(`Found libssl.so file using platform-specific paths: ${t}`);let e=e6(t);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"libssl-specific-path"}}e1('Falling back to "ldconfig" and other generic paths');let r=await ts('ldconfig -p | sed "s/.*=>s*//" | sed "s|.*/||" | grep libssl | sort | grep -v "libssl.so.0"');if(r||(r=await e5(["/lib64","/usr/lib64","/lib","/usr/lib"])),r){e1(`Found libssl.so file using "ldconfig" or other generic paths: ${r}`);let e=e6(r);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"ldconfig"}}let i=await ts("openssl version -v");if(i){e1(`Found openssl binary with version: ${i}`);let e=function(e){let t=/^OpenSSL\s(\d+\.\d+)\.\d+/.exec(e);if(t)return e9(`${t[1]}.x`)}(i);if(e1(`The parsed openssl version is: ${e}`),e)return{libssl:e,strategy:"openssl-binary"}}return e1("Couldn't find any version of libssl or OpenSSL in the system"),{}}async function e5(e){for(let t of e){let e=await e8(t);if(e)return e}}async function e8(e){try{return(await eS.default.readdir(e)).find(e=>e.startsWith("libssl.so.")&&!e.startsWith("libssl.so.0"))}catch(e){if("ENOENT"===e.code)return;throw e}}async function te(){let{binaryTarget:e}=await ti();return e}async function tt(){let{memoized:e,...t}=await ti();return t}var tr={};async function ti(){if(void 0!==tr.binaryTarget)return Promise.resolve({...tr,memoized:!0});let e=await e3(),t=function(e){let{platform:t,arch:r,archFromUname:i,libssl:n,targetDistro:s,familyDistro:a,originalDistro:o}=e;"linux"!==t||["x64","arm64"].includes(r)||eX(`Prisma only officially supports Linux on amd64 (x86_64) and arm64 (aarch64) system architectures (detected "${r}" instead). If you are using your own custom Prisma engines, you can ignore this warning, as long as you've compiled the engines for your system architecture "${i}".`);let l="1.1.x";if("linux"===t&&void 0===n){let e=eK({familyDistro:a}).with({familyDistro:"debian"},()=>"Please manually install OpenSSL via `apt-get update -y && apt-get install -y openssl` and try installing Prisma again. If you're running Prisma on Docker, add this command to your Dockerfile, or switch to an image that already has OpenSSL installed.").otherwise(()=>"Please manually install OpenSSL and try installing Prisma again.");eX(`Prisma failed to detect the libssl/openssl version to use, and may not work as expected. Defaulting to "openssl-${l}".
${e}`)}let u="debian";if("linux"===t&&void 0===s&&e1(`Distro is "${o}". Falling back to Prisma engines built for "${u}".`),"darwin"===t&&"arm64"===r)return"darwin-arm64";if("darwin"===t)return"darwin";if("win32"===t)return"windows";if("freebsd"===t)return s;if("openbsd"===t)return"openbsd";if("netbsd"===t)return"netbsd";if("linux"===t&&"nixos"===s)return"linux-nixos";if("linux"===t&&"arm64"===r)return`${"musl"===s?"linux-musl-arm64":"linux-arm64"}-openssl-${n||l}`;if("linux"===t&&"arm"===r)return`linux-arm-openssl-${n||l}`;if("linux"===t&&"musl"===s){let e="linux-musl";return!n||to(n)?e:`${e}-openssl-${n}`}return"linux"===t&&s&&n?`${s}-openssl-${n}`:("linux"!==t&&eX(`Prisma detected unknown OS "${t}" and may not work as expected. Defaulting to "linux".`),n?`${u}-openssl-${n}`:s?`${s}-openssl-${l}`:`${u}-openssl-${l}`)}(e);return{...tr={...e,binaryTarget:t},memoized:!1}}async function tn(e){try{return await e()}catch{return}}function ts(e){return tn(async()=>{let t=await e0(e);return e1(`Command "${e}" successfully returned "${t.stdout}"`),t.stdout})}async function ta(){return"function"==typeof eR.default.machine?eR.default.machine():(await ts("uname -m"))?.trim()}function to(e){return e.startsWith("1.")}var tl={};c(tl,{beep:()=>tM,clearScreen:()=>tF,clearTerminal:()=>tC,cursorBackward:()=>tw,cursorDown:()=>tb,cursorForward:()=>tv,cursorGetPosition:()=>tP,cursorHide:()=>tR,cursorLeft:()=>t_,cursorMove:()=>tg,cursorNextLine:()=>tT,cursorPrevLine:()=>tS,cursorRestorePosition:()=>tx,cursorSavePosition:()=>tE,cursorShow:()=>tk,cursorTo:()=>tm,cursorUp:()=>ty,enterAlternativeScreen:()=>tj,eraseDown:()=>tq,eraseEndLine:()=>tA,eraseLine:()=>t$,eraseLines:()=>tO,eraseScreen:()=>tI,eraseStartLine:()=>tN,eraseUp:()=>tD,exitAlternativeScreen:()=>tL,iTerm:()=>tH,image:()=>tB,link:()=>tG,scrollDown:()=>tU,scrollUp:()=>tV});var tu=p(r(1708),1),td=globalThis.window?.document!==void 0,tc=(globalThis.process?.versions?.node,globalThis.process?.versions?.bun,globalThis.Deno?.version?.deno,globalThis.process?.versions?.electron,globalThis.navigator?.userAgent?.includes("jsdom"),"u">typeof WorkerGlobalScope&&WorkerGlobalScope,"u">typeof DedicatedWorkerGlobalScope&&DedicatedWorkerGlobalScope,"u">typeof SharedWorkerGlobalScope&&SharedWorkerGlobalScope,"u">typeof ServiceWorkerGlobalScope&&ServiceWorkerGlobalScope,globalThis.navigator?.userAgentData?.platform);"macOS"===tc||globalThis.navigator?.platform==="MacIntel"||globalThis.navigator?.userAgent?.includes(" Mac ")===!0||globalThis.process?.platform,"Windows"===tc||globalThis.navigator?.platform==="Win32"||globalThis.process?.platform,"Linux"===tc||globalThis.navigator?.platform?.startsWith("Linux")===!0||globalThis.navigator?.userAgent?.includes(" Linux ")===!0||globalThis.process?.platform,"iOS"===tc||globalThis.navigator?.platform==="MacIntel"&&globalThis.navigator?.maxTouchPoints>1||/iPad|iPhone|iPod/.test(globalThis.navigator?.platform),"Android"===tc||globalThis.navigator?.platform==="Android"||globalThis.navigator?.userAgent?.includes(" Android ")===!0||globalThis.process?.platform;var tf=!td&&"Apple_Terminal"===tu.default.env.TERM_PROGRAM,tp=!td&&"win32"===tu.default.platform,th=td?()=>{throw Error("`process.cwd()` only works in Node.js, not the browser.")}:tu.default.cwd,tm=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");return"number"!=typeof t?"\x1b["+(e+1)+"G":"\x1b["+(t+1)+";"+(e+1)+"H"},tg=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");let r="";return e<0?r+="\x1b["+-e+"D":e>0&&(r+="\x1b["+e+"C"),t<0?r+="\x1b["+-t+"A":t>0&&(r+="\x1b["+t+"B"),r},ty=(e=1)=>"\x1b["+e+"A",tb=(e=1)=>"\x1b["+e+"B",tv=(e=1)=>"\x1b["+e+"C",tw=(e=1)=>"\x1b["+e+"D",t_="\x1b[G",tE=tf?"\x1b7":"\x1b[s",tx=tf?"\x1b8":"\x1b[u",tP="\x1b[6n",tT="\x1b[E",tS="\x1b[F",tR="\x1b[?25l",tk="\x1b[?25h",tO=e=>{let t="";for(let r=0;r<e;r++)t+=t$+(r<e-1?ty():"");return e&&(t+=t_),t},tA="\x1b[K",tN="\x1b[1K",t$="\x1b[2K",tq="\x1b[J",tD="\x1b[1J",tI="\x1b[2J",tV="\x1b[S",tU="\x1b[T",tF="\x1bc",tC=tp?`${tI}\x1b[0f`:`${tI}\x1b[3J\x1b[H`,tj="\x1b[?1049h",tL="\x1b[?1049l",tM="\x07",tG=(e,t)=>["\x1b]","8",";",";",t,"\x07",e,"\x1b]","8",";",";","\x07"].join(""),tB=(e,t={})=>{let r=`\x1b]1337;File=inline=1`;return t.width&&(r+=`;width=${t.width}`),t.height&&(r+=`;height=${t.height}`),!1===t.preserveAspectRatio&&(r+=";preserveAspectRatio=0"),r+":"+Buffer.from(e).toString("base64")+"\x07"},tH={setCwd:(e=th())=>`\x1b]50;CurrentDir=${e}\x07`,annotation(e,t={}){let r=`\x1b]1337;`,i=void 0!==t.x,n=void 0!==t.y;if((i||n)&&!(i&&n&&void 0!==t.length))throw Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");return e=e.replaceAll("|",""),r+=t.isHidden?"AddHiddenAnnotation=":"AddAnnotation=",t.length>0?r+=(i?[e,t.length,t.x,t.y]:[t.length,e]).join("|"):r+=e,r+"\x07"}},tW=p(g(),1);function tJ(e,t,{target:r="stdout",...i}={}){return tW.default[r]?tl.link(e,t):!1===i.fallback?e:"function"==typeof i.fallback?i.fallback(e,t):`${e} (\u200B${t}\u200B)`}tJ.isSupported=tW.default.stdout,tJ.stderr=(e,t,r={})=>tJ(e,t,{target:"stderr",...r}),tJ.stderr.isSupported=tW.default.stderr;var tK=y().version;function tQ(e){var t;let r;return("library"===(r=process.env.PRISMA_CLIENT_ENGINE_TYPE)?"library":"binary"===r?"binary":"client"===r?"client":void 0)||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":(t=e,t?.previewFeatures.includes("queryCompiler")?"client":"library"))}p(v());var tz=p(r(76760));p(v()),eE("prisma:engines"),tz.default.join(__dirname,"../query-engine-darwin"),tz.default.join(__dirname,"../query-engine-darwin-arm64"),tz.default.join(__dirname,"../query-engine-debian-openssl-1.0.x"),tz.default.join(__dirname,"../query-engine-debian-openssl-1.1.x"),tz.default.join(__dirname,"../query-engine-debian-openssl-3.0.x"),tz.default.join(__dirname,"../query-engine-linux-static-x64"),tz.default.join(__dirname,"../query-engine-linux-static-arm64"),tz.default.join(__dirname,"../query-engine-rhel-openssl-1.0.x"),tz.default.join(__dirname,"../query-engine-rhel-openssl-1.1.x"),tz.default.join(__dirname,"../query-engine-rhel-openssl-3.0.x"),tz.default.join(__dirname,"../libquery_engine-darwin.dylib.node"),tz.default.join(__dirname,"../libquery_engine-darwin-arm64.dylib.node"),tz.default.join(__dirname,"../libquery_engine-debian-openssl-1.0.x.so.node"),tz.default.join(__dirname,"../libquery_engine-debian-openssl-1.1.x.so.node"),tz.default.join(__dirname,"../libquery_engine-debian-openssl-3.0.x.so.node"),tz.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.0.x.so.node"),tz.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.1.x.so.node"),tz.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-3.0.x.so.node"),tz.default.join(__dirname,"../libquery_engine-linux-musl.so.node"),tz.default.join(__dirname,"../libquery_engine-linux-musl-openssl-3.0.x.so.node"),tz.default.join(__dirname,"../libquery_engine-rhel-openssl-1.0.x.so.node"),tz.default.join(__dirname,"../libquery_engine-rhel-openssl-1.1.x.so.node"),tz.default.join(__dirname,"../libquery_engine-rhel-openssl-3.0.x.so.node"),tz.default.join(__dirname,"../query_engine-windows.dll.node");var tY=p(r(73024)),tZ=eE("chmodPlusX"),tX=(p(w(),1),"prisma+postgres:");function t0(e){return e?.toString().startsWith(`${tX}//`)??!1}var t1=p(_()),t2=class{constructor(e){this.config=e}toString(){var e;let t,{config:r}=this,i=JSON.parse(JSON.stringify({provider:r.provider.fromEnvVar?`env("${r.provider.fromEnvVar}")`:r.provider.value,binaryTargets:function(e){let t;if(e.length>0){let r=e.find(e=>null!==e.fromEnvVar);t=r?`env("${r.fromEnvVar}")`:e.map(e=>e.native?"native":e.value)}else t=void 0;return t}(r.binaryTargets)}));return`generator ${r.name} {
${(0,t1.default)((t=Object.keys(e=i).reduce((e,t)=>Math.max(e,t.length),0),Object.entries(e).map(([e,r])=>`${e.padEnd(t)} = ${JSON.parse(JSON.stringify(r,(e,t)=>Array.isArray(t)?`[${t.map(e=>JSON.stringify(e)).join(", ")}]`:JSON.stringify(t)))}`).join(`
`)),2)}
}`}},t3={};c(t3,{error:()=>t8,info:()=>t5,log:()=>t9,query:()=>re,should:()=>t6,tags:()=>t4,warn:()=>t7});var t4={error:X("prisma:error"),warn:et("prisma:warn"),info:en("prisma:info"),query:er("prisma:query")},t6={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function t9(...e){console.log(...e)}function t7(e,...t){t6.warn()&&console.warn(`${t4.warn} ${e}`,...t)}function t5(e,...t){console.info(`${t4.info} ${e}`,...t)}function t8(e,...t){console.error(`${t4.error} ${e}`,...t)}function re(e,...t){console.log(`${t4.query} ${e}`,...t)}function rt(e,t){if(!e)throw Error(`${t}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`)}function rr(e,t){throw Error(t)}var ri=p(r(76760)),rn=p(T()),rs=p(r(73024)),ra=p(r(76760)),ro=eE("prisma:tryLoadEnv");function rl({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let i=ru(e);"none"!==r.conflictCheck&&function(e,t,r){let i=e?.dotenvResult.parsed,n=!rd(e?.path,t);if(i&&t&&n&&rs.default.existsSync(t)){let n=rn.default.parse(rs.default.readFileSync(t)),s=[];for(let e in n)i[e]===n[e]&&s.push(e);if(s.length>0){let i=ra.default.relative(process.cwd(),e.path),n=ra.default.relative(process.cwd(),t);if("error"===r)throw Error(`There is a conflict between env var${s.length>1?"s":""} in ${K(i)} and ${K(n)}
Conflicting env vars:
${s.map(e=>`  ${H(e)}`).join(`
`)}

We suggest to move the contents of ${K(n)} to ${K(i)} to consolidate your env vars.
`);if("warn"===r){let e=`Conflict for env var${s.length>1?"s":""} ${s.map(e=>H(e)).join(", ")} in ${K(i)} and ${K(n)}
Env vars from ${K(n)} overwrite the ones from ${K(i)}
      `;console.warn(`${et("warn(prisma)")} ${e}`)}}}}(i,t,r.conflictCheck);let n=null;return rd(i?.path,t)||(n=ru(t)),i||n||ro("No Environment variables loaded"),n?.dotenvResult.error?console.error(X(H("Schema Env Error: "))+n.dotenvResult.error):{message:[i?.message,n?.message].filter(Boolean).join(`
`),parsed:{...i?.dotenvResult?.parsed,...n?.dotenvResult?.parsed}}}function ru(e){var t;return(t=e)&&rs.default.existsSync(t)?(ro(`Environment variables loaded from ${e}`),{dotenvResult:function(e){let t=e.ignoreProcessEnv?{}:process.env,r=i=>i.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(i,n){let s=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(n);if(!s)return i;let a=s[1],o,l;if("\\"===a)o=(l=s[0]).replace("\\$","$");else{let i=s[2];l=s[0].substring(a.length),o=r(o=Object.hasOwnProperty.call(t,i)?t[i]:e.parsed[i]||"")}return i.replace(l,o)},i)??i;for(let i in e.parsed){let n=Object.hasOwnProperty.call(t,i)?t[i]:e.parsed[i];e.parsed[i]=r(n)}for(let r in e.parsed)t[r]=e.parsed[r];return e}(rn.default.config({path:e,debug:!!process.env.DOTENV_CONFIG_DEBUG||void 0})),message:W(`Environment variables loaded from ${ra.default.relative(process.cwd(),e)}`),path:e}):(ro(`Environment variables not found at ${e}`),null)}function rd(e,t){return e&&t&&ra.default.resolve(e)===ra.default.resolve(t)}function rc(e,t){let r={};for(let i of Object.keys(e))r[i]=t(e[i],i);return r}function rf(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var rp=new Set,rh=(e,t,...r)=>{rp.has(e)||(rp.add(e),t7(t,...r))},rm=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,i){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=i,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};rf(rm,"PrismaClientInitializationError");var rg=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(e,{code:t,clientVersion:r,meta:i,batchRequestIdx:n}){super(e),this.name="PrismaClientKnownRequestError",this.code=t,this.clientVersion=r,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:n,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};rf(rg,"PrismaClientKnownRequestError");var ry=class extends Error{clientVersion;constructor(e,t){super(e),this.name="PrismaClientRustPanicError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};rf(ry,"PrismaClientRustPanicError");var rb=class extends Error{clientVersion;batchRequestIdx;constructor(e,{clientVersion:t,batchRequestIdx:r}){super(e),this.name="PrismaClientUnknownRequestError",this.clientVersion=t,Object.defineProperty(this,"batchRequestIdx",{value:r,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};rf(rb,"PrismaClientUnknownRequestError");var rv=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(e,{clientVersion:t}){super(e),this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};rf(rv,"PrismaClientValidationError");var rw,r_,rE="0123456789abcdef",rx="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",rP="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",rT={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},rS=!0,rR="[DecimalError] ",rk=rR+"Invalid argument: ",rO=rR+"Precision limit exceeded",rA=rR+"crypto unavailable",rN="[object Decimal]",r$=Math.floor,rq=Math.pow,rD=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,rI=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,rV=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,rU=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,rF=rx.length-1,rC=rP.length-1,rj={toStringTag:rN};function rL(e){var t,r,i,n=e.length-1,s="",a=e[0];if(n>0){for(s+=a,t=1;t<n;t++)(r=7-(i=e[t]+"").length)&&(s+=rZ(r)),s+=i;(r=7-(i=(a=e[t])+"").length)&&(s+=rZ(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return s+a}function rM(e,t,r){if(e!==~~e||e<t||e>r)throw Error(rk+e)}function rG(e,t,r,i){var n,s,a,o;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=7,n=0):(n=Math.ceil((t+1)/7),t%=7),s=rq(10,7-t),o=e[n]%s|0,null==i?t<3?(0==t?o=o/100|0:1==t&&(o=o/10|0),a=r<4&&99999==o||r>3&&49999==o||5e4==o||0==o):a=(r<4&&o+1==s||r>3&&o+1==s/2)&&(e[n+1]/s/100|0)==rq(10,t-2)-1||(o==s/2||0==o)&&(e[n+1]/s/100|0)==0:t<4?(0==t?o=o/1e3|0:1==t?o=o/100|0:2==t&&(o=o/10|0),a=(i||r<4)&&9999==o||!i&&r>3&&4999==o):a=((i||r<4)&&o+1==s||!i&&r>3&&o+1==s/2)&&(e[n+1]/s/1e3|0)==rq(10,t-3)-1,a}function rB(e,t,r){for(var i,n,s=[0],a=0,o=e.length;a<o;){for(n=s.length;n--;)s[n]*=t;for(s[0]+=rE.indexOf(e.charAt(a++)),i=0;i<s.length;i++)s[i]>r-1&&(void 0===s[i+1]&&(s[i+1]=0),s[i+1]+=s[i]/r|0,s[i]%=r)}return s.reverse()}rj.absoluteValue=rj.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),rW(e)},rj.ceil=function(){return rW(new this.constructor(this),this.e+1,2)},rj.clampedTo=rj.clamp=function(e,t){var r=this.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(rk+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new r(this)},rj.comparedTo=rj.cmp=function(e){var t,r,i,n,s=this.d,a=(e=new this.constructor(e)).d,o=this.s,l=e.s;if(!s||!a)return o&&l?o!==l?o:s===a?0:!s^o<0?1:-1:NaN;if(!s[0]||!a[0])return s[0]?o:a[0]?-l:0;if(o!==l)return o;if(this.e!==e.e)return this.e>e.e^o<0?1:-1;for(i=s.length,n=a.length,t=0,r=i<n?i:n;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^o<0?1:-1;return i===n?0:i>n^o<0?1:-1},rj.cosine=rj.cos=function(){var e,t,r=this,i=r.constructor;return r.d?r.d[0]?(e=i.precision,t=i.rounding,i.precision=e+Math.max(r.e,r.sd())+7,i.rounding=1,r=function(e,t){var r,i,n;if(t.isZero())return t;(i=t.d.length)<32?n=(1/r7(4,r=Math.ceil(i/3))).toString():(r=16,n="2.3283064365386962890625e-10"),e.precision+=r,t=r9(e,1,t.times(n),new e(1));for(var s=r;s--;){var a=t.times(t);t=a.times(a).minus(a).times(8).plus(1)}return e.precision-=r,t}(i,r5(i,r)),i.precision=e,i.rounding=t,rW(2==r_||3==r_?r.neg():r,e,t,!0)):new i(1):new i(NaN)},rj.cubeRoot=rj.cbrt=function(){var e,t,r,i,n,s,a,o,l,u,d=this.constructor;if(!this.isFinite()||this.isZero())return new d(this);for(rS=!1,(s=this.s*rq(this.s*this,1/3))&&Math.abs(s)!=1/0?i=new d(s.toString()):(r=rL(this.d),(s=((e=this.e)-r.length+1)%3)&&(r+=1==s||-2==s?"0":"00"),s=rq(r,1/3),e=r$((e+1)/3)-(e%3==(e<0?-1:2)),(i=new d(r=s==1/0?"5e"+e:(r=s.toExponential()).slice(0,r.indexOf("e")+1)+e)).s=this.s),a=(e=d.precision)+3;;)if(i=rH((u=(l=(o=i).times(o).times(o)).plus(this)).plus(this).times(o),u.plus(l),a+2,1),rL(o.d).slice(0,a)===(r=rL(i.d)).slice(0,a))if("9999"!=(r=r.slice(a-3,a+1))&&(n||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(rW(i,e+1,1),t=!i.times(i).times(i).eq(this));break}else{if(!n&&(rW(o,e+1,0),o.times(o).times(o).eq(this))){i=o;break}a+=4,n=1}return rS=!0,rW(i,e,d.rounding,t)},rj.decimalPlaces=rj.dp=function(){var e,t=this.d,r=NaN;if(t){if(r=((e=t.length-1)-r$(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r},rj.dividedBy=rj.div=function(e){return rH(this,new this.constructor(e))},rj.dividedToIntegerBy=rj.divToInt=function(e){var t=this.constructor;return rW(rH(this,new t(e),0,1,1),t.precision,t.rounding)},rj.equals=rj.eq=function(e){return 0===this.cmp(e)},rj.floor=function(){return rW(new this.constructor(this),this.e+1,3)},rj.greaterThan=rj.gt=function(e){return this.cmp(e)>0},rj.greaterThanOrEqualTo=rj.gte=function(e){var t=this.cmp(e);return 1==t||0===t},rj.hyperbolicCosine=rj.cosh=function(){var e,t,r,i,n,s=this,a=s.constructor,o=new a(1);if(!s.isFinite())return new a(s.s?1/0:NaN);if(s.isZero())return o;r=a.precision,i=a.rounding,a.precision=r+Math.max(s.e,s.sd())+4,a.rounding=1,(n=s.d.length)<32?t=(1/r7(4,e=Math.ceil(n/3))).toString():(e=16,t="2.3283064365386962890625e-10"),s=r9(a,1,s.times(t),new a(1),!0);for(var l,u=e,d=new a(8);u--;)l=s.times(s),s=o.minus(l.times(d.minus(l.times(d))));return rW(s,a.precision=r,a.rounding=i,!0)},rj.hyperbolicSine=rj.sinh=function(){var e,t,r,i,n=this,s=n.constructor;if(!n.isFinite()||n.isZero())return new s(n);if(t=s.precision,r=s.rounding,s.precision=t+Math.max(n.e,n.sd())+4,s.rounding=1,(i=n.d.length)<3)n=r9(s,2,n,n,!0);else{e=(e=1.4*Math.sqrt(i))>16?16:0|e,n=r9(s,2,n=n.times(1/r7(5,e)),n,!0);for(var a,o=new s(5),l=new s(16),u=new s(20);e--;)a=n.times(n),n=n.times(o.plus(a.times(l.times(a).plus(u))))}return s.precision=t,s.rounding=r,rW(n,t,r,!0)},rj.hyperbolicTangent=rj.tanh=function(){var e,t,r=this.constructor;return this.isFinite()?this.isZero()?new r(this):(e=r.precision,t=r.rounding,r.precision=e+7,r.rounding=1,rH(this.sinh(),this.cosh(),r.precision=e,r.rounding=t)):new r(this.s)},rj.inverseCosine=rj.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),i=t.precision,n=t.rounding;return -1!==r?0===r?e.isNeg()?rz(t,i,n):new t(0):new t(NaN):e.isZero()?rz(t,i+4,n).times(.5):(t.precision=i+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=i,t.rounding=n,e.times(2))},rj.inverseHyperbolicCosine=rj.acosh=function(){var e,t,r=this,i=r.constructor;return r.lte(1)?new i(r.eq(1)?0:NaN):r.isFinite()?(e=i.precision,t=i.rounding,i.precision=e+Math.max(Math.abs(r.e),r.sd())+4,i.rounding=1,rS=!1,r=r.times(r).minus(1).sqrt().plus(r),rS=!0,i.precision=e,i.rounding=t,r.ln()):new i(r)},rj.inverseHyperbolicSine=rj.asinh=function(){var e,t,r=this,i=r.constructor;return!r.isFinite()||r.isZero()?new i(r):(e=i.precision,t=i.rounding,i.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,i.rounding=1,rS=!1,r=r.times(r).plus(1).sqrt().plus(r),rS=!0,i.precision=e,i.rounding=t,r.ln())},rj.inverseHyperbolicTangent=rj.atanh=function(){var e,t,r,i,n=this,s=n.constructor;return n.isFinite()?n.e>=0?new s(n.abs().eq(1)?n.s/0:n.isZero()?n:NaN):(e=s.precision,t=s.rounding,Math.max(i=n.sd(),e)<-(2*n.e)-1?rW(new s(n),e,t,!0):(s.precision=r=i-n.e,n=rH(n.plus(1),new s(1).minus(n),r+e,1),s.precision=e+4,s.rounding=1,n=n.ln(),s.precision=e,s.rounding=t,n.times(.5))):new s(NaN)},rj.inverseSine=rj.asin=function(){var e,t,r,i,n=this,s=n.constructor;return n.isZero()?new s(n):(t=n.abs().cmp(1),r=s.precision,i=s.rounding,-1!==t?0===t?((e=rz(s,r+4,i).times(.5)).s=n.s,e):new s(NaN):(s.precision=r+6,s.rounding=1,n=n.div(new s(1).minus(n.times(n)).sqrt().plus(1)).atan(),s.precision=r,s.rounding=i,n.times(2)))},rj.inverseTangent=rj.atan=function(){var e,t,r,i,n,s,a,o,l,u=this,d=u.constructor,c=d.precision,f=d.rounding;if(u.isFinite()){if(u.isZero())return new d(u);if(u.abs().eq(1)&&c+4<=rC)return(a=rz(d,c+4,f).times(.25)).s=u.s,a}else{if(!u.s)return new d(NaN);if(c+4<=rC)return(a=rz(d,c+4,f).times(.5)).s=u.s,a}for(d.precision=o=c+10,d.rounding=1,e=r=Math.min(28,o/7+2|0);e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(rS=!1,t=Math.ceil(o/7),i=1,l=u.times(u),a=new d(u),n=u;-1!==e;)if(n=n.times(l),s=a.minus(n.div(i+=2)),n=n.times(l),void 0!==(a=s.plus(n.div(i+=2))).d[t])for(e=t;a.d[e]===s.d[e]&&e--;);return r&&(a=a.times(2<<r-1)),rS=!0,rW(a,d.precision=c,d.rounding=f,!0)},rj.isFinite=function(){return!!this.d},rj.isInteger=rj.isInt=function(){return!!this.d&&r$(this.e/7)>this.d.length-2},rj.isNaN=function(){return!this.s},rj.isNegative=rj.isNeg=function(){return this.s<0},rj.isPositive=rj.isPos=function(){return this.s>0},rj.isZero=function(){return!!this.d&&0===this.d[0]},rj.lessThan=rj.lt=function(e){return 0>this.cmp(e)},rj.lessThanOrEqualTo=rj.lte=function(e){return 1>this.cmp(e)},rj.logarithm=rj.log=function(e){var t,r,i,n,s,a,o,l,u=this.constructor,d=u.precision,c=u.rounding;if(null==e)e=new u(10),t=!0;else{if(r=(e=new u(e)).d,e.s<0||!r||!r[0]||e.eq(1))return new u(NaN);t=e.eq(10)}if(r=this.d,this.s<0||!r||!r[0]||this.eq(1))return new u(r&&!r[0]?-1/0:1!=this.s?NaN:r?0:1/0);if(t)if(r.length>1)s=!0;else{for(n=r[0];n%10==0;)n/=10;s=1!==n}if(rS=!1,rG((l=rH(a=r3(this,o=d+5),t?rQ(u,o+10):r3(e,o),o,1)).d,n=d,c))do if(o+=10,l=rH(a=r3(this,o),t?rQ(u,o+10):r3(e,o),o,1),!s){+rL(l.d).slice(n+1,n+15)+1==1e14&&(l=rW(l,d+1,0));break}while(rG(l.d,n+=10,c));return rS=!0,rW(l,d,c)},rj.minus=rj.sub=function(e){var t,r,i,n,s,a,o,l,u,d,c,f,p=this.constructor;if(e=new p(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new p(e.d||this.s!==e.s?this:NaN):e=new p(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(u=this.d,f=e.d,o=p.precision,l=p.rounding,!u[0]||!f[0]){if(f[0])e.s=-e.s;else{if(!u[0])return new p(3===l?-0:0);e=new p(this)}return rS?rW(e,o,l):e}if(r=r$(e.e/7),d=r$(this.e/7),u=u.slice(),s=d-r){for((c=s<0)?(t=u,s=-s,a=f.length):(t=f,r=d,a=u.length),s>(i=Math.max(Math.ceil(o/7),a)+2)&&(s=i,t.length=1),t.reverse(),i=s;i--;)t.push(0);t.reverse()}else{for((c=(i=u.length)<(a=f.length))&&(a=i),i=0;i<a;i++)if(u[i]!=f[i]){c=u[i]<f[i];break}s=0}for(c&&(t=u,u=f,f=t,e.s=-e.s),a=u.length,i=f.length-a;i>0;--i)u[a++]=0;for(i=f.length;i>s;){if(u[--i]<f[i]){for(n=i;n&&0===u[--n];)u[n]=1e7-1;--u[n],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--a];)u.pop();for(;0===u[0];u.shift())--r;return u[0]?(e.d=u,e.e=rK(u,r),rS?rW(e,o,l):e):new p(3===l?-0:0)},rj.modulo=rj.mod=function(e){var t,r=this.constructor;return e=new r(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(rS=!1,9==r.modulo?(t=rH(this,e.abs(),0,3,1),t.s*=e.s):t=rH(this,e,0,r.modulo,1),t=t.times(e),rS=!0,this.minus(t)):rW(new r(this),r.precision,r.rounding):new r(NaN)},rj.naturalExponential=rj.exp=function(){return r2(this)},rj.naturalLogarithm=rj.ln=function(){return r3(this)},rj.negated=rj.neg=function(){var e=new this.constructor(this);return e.s=-e.s,rW(e)},rj.plus=rj.add=function(e){var t,r,i,n,s,a,o,l,u,d,c=this.constructor;if(e=new c(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new c(e.d||this.s===e.s?this:NaN)):e=new c(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(u=this.d,d=e.d,o=c.precision,l=c.rounding,!u[0]||!d[0])return d[0]||(e=new c(this)),rS?rW(e,o,l):e;if(s=r$(this.e/7),i=r$(e.e/7),u=u.slice(),n=s-i){for(n<0?(r=u,n=-n,a=d.length):(r=d,i=s,a=u.length),n>(a=(s=Math.ceil(o/7))>a?s+1:a+1)&&(n=a,r.length=1),r.reverse();n--;)r.push(0);r.reverse()}for((a=u.length)-(n=d.length)<0&&(n=a,r=d,d=u,u=r),t=0;n;)t=(u[--n]=u[n]+d[n]+t)/1e7|0,u[n]%=1e7;for(t&&(u.unshift(t),++i),a=u.length;0==u[--a];)u.pop();return e.d=u,e.e=rK(u,i),rS?rW(e,o,l):e},rj.precision=rj.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(rk+e);return this.d?(t=rY(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},rj.round=function(){var e=this.constructor;return rW(new e(this),this.e+1,e.rounding)},rj.sine=rj.sin=function(){var e,t,r=this,i=r.constructor;return r.isFinite()?r.isZero()?new i(r):(e=i.precision,t=i.rounding,i.precision=e+Math.max(r.e,r.sd())+7,i.rounding=1,r=function(e,t){var r,i=t.d.length;if(i<3)return t.isZero()?t:r9(e,2,t,t);r=(r=1.4*Math.sqrt(i))>16?16:0|r,t=r9(e,2,t=t.times(1/r7(5,r)),t);for(var n,s=new e(5),a=new e(16),o=new e(20);r--;)n=t.times(t),t=t.times(s.plus(n.times(a.times(n).minus(o))));return t}(i,r5(i,r)),i.precision=e,i.rounding=t,rW(r_>2?r.neg():r,e,t,!0)):new i(NaN)},rj.squareRoot=rj.sqrt=function(){var e,t,r,i,n,s,a=this.d,o=this.e,l=this.s,u=this.constructor;if(1!==l||!a||!a[0])return new u(!l||l<0&&(!a||a[0])?NaN:a?this:1/0);for(rS=!1,0==(l=Math.sqrt(+this))||l==1/0?(((t=rL(a)).length+o)%2==0&&(t+="0"),l=Math.sqrt(t),o=r$((o+1)/2)-(o<0||o%2),i=new u(t=l==1/0?"5e"+o:(t=l.toExponential()).slice(0,t.indexOf("e")+1)+o)):i=new u(l.toString()),r=(o=u.precision)+3;;)if(i=(s=i).plus(rH(this,s,r+2,1)).times(.5),rL(s.d).slice(0,r)===(t=rL(i.d)).slice(0,r))if("9999"!=(t=t.slice(r-3,r+1))&&(n||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(rW(i,o+1,1),e=!i.times(i).eq(this));break}else{if(!n&&(rW(s,o+1,0),s.times(s).eq(this))){i=s;break}r+=4,n=1}return rS=!0,rW(i,o,u.rounding,e)},rj.tangent=rj.tan=function(){var e,t,r=this,i=r.constructor;return r.isFinite()?r.isZero()?new i(r):(e=i.precision,t=i.rounding,i.precision=e+10,i.rounding=1,(r=r.sin()).s=1,r=rH(r,new i(1).minus(r.times(r)).sqrt(),e+10,0),i.precision=e,i.rounding=t,rW(2==r_||4==r_?r.neg():r,e,t,!0)):new i(NaN)},rj.times=rj.mul=function(e){var t,r,i,n,s,a,o,l,u,d=this.constructor,c=this.d,f=(e=new d(e)).d;if(e.s*=this.s,!c||!c[0]||!f||!f[0])return new d(!e.s||c&&!c[0]&&!f||f&&!f[0]&&!c?NaN:!c||!f?e.s/0:0*e.s);for(r=r$(this.e/7)+r$(e.e/7),(l=c.length)<(u=f.length)&&(s=c,c=f,f=s,a=l,l=u,u=a),s=[],i=a=l+u;i--;)s.push(0);for(i=u;--i>=0;){for(t=0,n=l+i;n>i;)o=s[n]+f[i]*c[n-i-1]+t,s[n--]=o%1e7|0,t=o/1e7|0;s[n]=(s[n]+t)%1e7|0}for(;!s[--a];)s.pop();return t?++r:s.shift(),e.d=s,e.e=rK(s,r),rS?rW(e,d.precision,d.rounding):e},rj.toBinary=function(e,t){return r8(this,2,e,t)},rj.toDecimalPlaces=rj.toDP=function(e,t){var r=this,i=r.constructor;return r=new i(r),void 0===e?r:(rM(e,0,1e9),void 0===t?t=i.rounding:rM(t,0,8),rW(r,e+r.e+1,t))},rj.toExponential=function(e,t){var r,i=this,n=i.constructor;return void 0===e?r=rJ(i,!0):(rM(e,0,1e9),void 0===t?t=n.rounding:rM(t,0,8),r=rJ(i=rW(new n(i),e+1,t),!0,e+1)),i.isNeg()&&!i.isZero()?"-"+r:r},rj.toFixed=function(e,t){var r,i,n=this.constructor;return void 0===e?r=rJ(this):(rM(e,0,1e9),void 0===t?t=n.rounding:rM(t,0,8),r=rJ(i=rW(new n(this),e+this.e+1,t),!1,e+i.e+1)),this.isNeg()&&!this.isZero()?"-"+r:r},rj.toFraction=function(e){var t,r,i,n,s,a,o,l,u,d,c,f,p=this.d,h=this.constructor;if(!p)return new h(this);if(u=r=new h(1),i=l=new h(0),a=(s=(t=new h(i)).e=rY(p)-this.e-1)%7,t.d[0]=rq(10,a<0?7+a:a),null==e)e=s>0?t:u;else{if(!(o=new h(e)).isInt()||o.lt(u))throw Error(rk+o);e=o.gt(t)?s>0?t:u:o}for(rS=!1,o=new h(rL(p)),d=h.precision,h.precision=s=7*p.length*2;c=rH(o,t,0,1,1),1!=(n=r.plus(c.times(i))).cmp(e);)r=i,i=n,n=u,u=l.plus(c.times(n)),l=n,n=t,t=o.minus(c.times(n)),o=n;return n=rH(e.minus(r),i,0,1,1),l=l.plus(n.times(u)),r=r.plus(n.times(i)),l.s=u.s=this.s,f=1>rH(u,i,s,1).minus(this).abs().cmp(rH(l,r,s,1).minus(this).abs())?[u,i]:[l,r],h.precision=d,rS=!0,f},rj.toHexadecimal=rj.toHex=function(e,t){return r8(this,16,e,t)},rj.toNearest=function(e,t){var r=this,i=r.constructor;if(r=new i(r),null==e){if(!r.d)return r;e=new i(1),t=i.rounding}else{if(e=new i(e),void 0===t?t=i.rounding:rM(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(rS=!1,r=rH(r,e,0,t,1).times(e),rS=!0,rW(r)):(e.s=r.s,r=e),r},rj.toNumber=function(){return+this},rj.toOctal=function(e,t){return r8(this,8,e,t)},rj.toPower=rj.pow=function(e){var t,r,i,n,s,a,o=this,l=o.constructor,u=+(e=new l(e));if(!o.d||!e.d||!o.d[0]||!e.d[0])return new l(rq(+o,u));if((o=new l(o)).eq(1))return o;if(i=l.precision,s=l.rounding,e.eq(1))return rW(o,i,s);if((t=r$(e.e/7))>=e.d.length-1&&(r=u<0?-u:u)<=0x1fffffffffffff)return n=rX(l,o,r,i),e.s<0?new l(1).div(n):rW(n,i,s);if((a=o.s)<0){if(t<e.d.length-1)return new l(NaN);if((1&e.d[t])==0&&(a=1),0==o.e&&1==o.d[0]&&1==o.d.length)return o.s=a,o}return(t=0!=(r=rq(+o,u))&&isFinite(r)?new l(r+"").e:r$(u*(Math.log("0."+rL(o.d))/Math.LN10+o.e+1)))>l.maxE+1||t<l.minE-1?new l(t>0?a/0:0):(rS=!1,l.rounding=o.s=1,r=Math.min(12,(t+"").length),(n=r2(e.times(r3(o,i+r)),i)).d&&rG((n=rW(n,i+5,1)).d,i,s)&&(t=i+10,+rL((n=rW(r2(e.times(r3(o,t+r)),t),t+5,1)).d).slice(i+1,i+15)+1==1e14&&(n=rW(n,i+1,0))),n.s=a,rS=!0,l.rounding=s,rW(n,i,s))},rj.toPrecision=function(e,t){var r,i=this,n=i.constructor;return void 0===e?r=rJ(i,i.e<=n.toExpNeg||i.e>=n.toExpPos):(rM(e,1,1e9),void 0===t?t=n.rounding:rM(t,0,8),r=rJ(i=rW(new n(i),e,t),e<=i.e||i.e<=n.toExpNeg,e)),i.isNeg()&&!i.isZero()?"-"+r:r},rj.toSignificantDigits=rj.toSD=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(rM(e,1,1e9),void 0===t?t=r.rounding:rM(t,0,8)),rW(new r(this),e,t)},rj.toString=function(){var e=this.constructor,t=rJ(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},rj.truncated=rj.trunc=function(){return rW(new this.constructor(this),this.e+1,1)},rj.valueOf=rj.toJSON=function(){var e=this.constructor,t=rJ(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var rH=function(){function e(e,t,r){var i,n=0,s=e.length;for(e=e.slice();s--;)i=e[s]*t+n,e[s]=i%r|0,n=i/r|0;return n&&e.unshift(n),e}function t(e,t,r,i){var n,s;if(r!=i)s=r>i?1:-1;else for(n=s=0;n<r;n++)if(e[n]!=t[n]){s=e[n]>t[n]?1:-1;break}return s}function r(e,t,r,i){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=n*i+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(i,n,s,a,o,l){var u,d,c,f,p,h,m,g,y,b,v,w,_,E,x,P,T,S,R,k,O=i.constructor,A=i.s==n.s?1:-1,N=i.d,$=n.d;if(!N||!N[0]||!$||!$[0])return new O(!i.s||!n.s||(N?$&&N[0]==$[0]:!$)?NaN:N&&0==N[0]||!$?0*A:A/0);for(l?(p=1,d=i.e-n.e):(l=1e7,p=7,d=r$(i.e/p)-r$(n.e/p)),R=$.length,T=N.length,b=(y=new O(A)).d=[],c=0;$[c]==(N[c]||0);c++);if($[c]>(N[c]||0)&&d--,null==s?(E=s=O.precision,a=O.rounding):E=o?s+(i.e-n.e)+1:s,E<0)b.push(1),h=!0;else{if(E=E/p+2|0,c=0,1==R){for(f=0,$=$[0],E++;(c<T||f)&&E--;c++)x=f*l+(N[c]||0),b[c]=x/$|0,f=x%$|0;h=f||c<T}else{for((f=l/($[0]+1)|0)>1&&($=e($,f,l),N=e(N,f,l),R=$.length,T=N.length),P=R,w=(v=N.slice(0,R)).length;w<R;)v[w++]=0;(k=$.slice()).unshift(0),S=$[0],$[1]>=l/2&&++S;do f=0,(u=t($,v,R,w))<0?(_=v[0],R!=w&&(_=_*l+(v[1]||0)),(f=_/S|0)>1?(f>=l&&(f=l-1),g=(m=e($,f,l)).length,w=v.length,1==(u=t(m,v,g,w))&&(f--,r(m,R<g?k:$,g,l))):(0==f&&(u=f=1),m=$.slice()),(g=m.length)<w&&m.unshift(0),r(v,m,w,l),-1==u&&(w=v.length,(u=t($,v,R,w))<1&&(f++,r(v,R<w?k:$,w,l))),w=v.length):0===u&&(f++,v=[0]),b[c++]=f,u&&v[0]?v[w++]=N[P]||0:(v=[N[P]],w=1);while((P++<T||void 0!==v[0])&&E--);h=void 0!==v[0]}b[0]||b.shift()}if(1==p)y.e=d,rw=h;else{for(c=1,f=b[0];f>=10;f/=10)c++;y.e=c+d*p-1,rW(y,o?s+y.e+1:s,a,h)}return y}}();function rW(e,t,r,i){var n,s,a,o,l,u,d,c,f,p=e.constructor;e:if(null!=t){if(!(c=e.d))return e;for(n=1,o=c[0];o>=10;o/=10)n++;if((s=t-n)<0)s+=7,a=t,l=(d=c[f=0])/rq(10,n-a-1)%10|0;else if((f=Math.ceil((s+1)/7))>=(o=c.length))if(i){for(;o++<=f;)c.push(0);d=l=0,n=1,s%=7,a=s-7+1}else break e;else{for(d=o=c[f],n=1;o>=10;o/=10)n++;s%=7,l=(a=s-7+n)<0?0:d/rq(10,n-a-1)%10|0}if(i=i||t<0||void 0!==c[f+1]||(a<0?d:d%rq(10,n-a-1)),u=r<4?(l||i)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||i||6==r&&(s>0?a>0?d/rq(10,n-a):0:c[f-1])%10&1||r==(e.s<0?8:7)),t<1||!c[0])return c.length=0,u?(t-=e.e+1,c[0]=rq(10,(7-t%7)%7),e.e=-t||0):c[0]=e.e=0,e;if(0==s?(c.length=f,o=1,f--):(c.length=f+1,o=rq(10,7-s),c[f]=a>0?(d/rq(10,n-a)%rq(10,a)|0)*o:0),u)for(;;)if(0==f){for(s=1,a=c[0];a>=10;a/=10)s++;for(a=c[0]+=o,o=1;a>=10;a/=10)o++;s!=o&&(e.e++,1e7==c[0]&&(c[0]=1));break}else{if(c[f]+=o,1e7!=c[f])break;c[f--]=0,o=1}for(s=c.length;0===c[--s];)c.pop()}return rS&&(e.e>p.maxE?(e.d=null,e.e=NaN):e.e<p.minE&&(e.e=0,e.d=[0])),e}function rJ(e,t,r){if(!e.isFinite())return r4(e);var i,n=e.e,s=rL(e.d),a=s.length;return t?(r&&(i=r-a)>0?s=s.charAt(0)+"."+s.slice(1)+rZ(i):a>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):n<0?(s="0."+rZ(-n-1)+s,r&&(i=r-a)>0&&(s+=rZ(i))):n>=a?(s+=rZ(n+1-a),r&&(i=r-n-1)>0&&(s=s+"."+rZ(i))):((i=n+1)<a&&(s=s.slice(0,i)+"."+s.slice(i)),r&&(i=r-a)>0&&(n+1===a&&(s+="."),s+=rZ(i))),s}function rK(e,t){var r=e[0];for(t*=7;r>=10;r/=10)t++;return t}function rQ(e,t,r){if(t>rF)throw rS=!0,r&&(e.precision=r),Error(rO);return rW(new e(rx),t,1,!0)}function rz(e,t,r){if(t>rC)throw Error(rO);return rW(new e(rP),t,r,!0)}function rY(e){var t=e.length-1,r=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function rZ(e){for(var t="";e--;)t+="0";return t}function rX(e,t,r,i){var n,s=new e(1),a=Math.ceil(i/7+4);for(rS=!1;;){if(r%2&&ie((s=s.times(t)).d,a)&&(n=!0),0===(r=r$(r/2))){r=s.d.length-1,n&&0===s.d[r]&&++s.d[r];break}ie((t=t.times(t)).d,a)}return rS=!0,s}function r0(e){return 1&e.d[e.d.length-1]}function r1(e,t,r){for(var i,n,s=new e(t[0]),a=0;++a<t.length;){if(!(n=new e(t[a])).s){s=n;break}((i=s.cmp(n))===r||0===i&&s.s===r)&&(s=n)}return s}function r2(e,t){var r,i,n,s,a,o,l,u=0,d=0,c=0,f=e.constructor,p=f.rounding,h=f.precision;if(!e.d||!e.d[0]||e.e>17)return new f(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(rS=!1,l=h):l=t,o=new f(.03125);e.e>-2;)e=e.times(o),c+=5;for(l+=i=Math.log(rq(2,c))/Math.LN10*2+5|0,r=s=a=new f(1),f.precision=l;;){if(s=rW(s.times(e),l,1),r=r.times(++d),rL((o=a.plus(rH(s,r,l,1))).d).slice(0,l)===rL(a.d).slice(0,l)){for(n=c;n--;)a=rW(a.times(a),l,1);if(null!=t)return f.precision=h,a;if(!(u<3&&rG(a.d,l-i,p,u)))return rW(a,f.precision=h,p,rS=!0);f.precision=l+=10,r=s=o=new f(1),d=0,u++}a=o}}function r3(e,t){var r,i,n,s,a,o,l,u,d,c,f,p=1,h=e,m=h.d,g=h.constructor,y=g.rounding,b=g.precision;if(h.s<0||!m||!m[0]||!h.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=h.s?NaN:m?0:h);if(null==t?(rS=!1,d=b):d=t,g.precision=d+=10,i=(r=rL(m)).charAt(0),!(15e14>Math.abs(s=h.e)))return u=rQ(g,d+2,b).times(s+""),h=r3(new g(i+"."+r.slice(1)),d-10).plus(u),g.precision=b,null==t?rW(h,b,y,rS=!0):h;for(;i<7&&1!=i||1==i&&r.charAt(1)>3;)i=(r=rL((h=h.times(e)).d)).charAt(0),p++;for(s=h.e,i>1?(h=new g("0."+r),s++):h=new g(i+"."+r.slice(1)),c=h,l=a=h=rH(h.minus(1),h.plus(1),d,1),f=rW(h.times(h),d,1),n=3;;){if(a=rW(a.times(f),d,1),rL((u=l.plus(rH(a,new g(n),d,1))).d).slice(0,d)===rL(l.d).slice(0,d))if(l=l.times(2),0!==s&&(l=l.plus(rQ(g,d+2,b).times(s+""))),l=rH(l,new g(p),d,1),null!=t)return g.precision=b,l;else{if(!rG(l.d,d-10,y,o))return rW(l,g.precision=b,y,rS=!0);g.precision=d+=10,u=a=h=rH(c.minus(1),c.plus(1),d,1),f=rW(h.times(h),d,1),n=o=1}l=u,n+=2}}function r4(e){return String(e.s*e.s/0)}function r6(e,t){var r,i,n;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(i=t.search(/e/i))>0?(r<0&&(r=i),r+=+t.slice(i+1),t=t.substring(0,i)):r<0&&(r=t.length),i=0;48===t.charCodeAt(i);i++);for(n=t.length;48===t.charCodeAt(n-1);--n);if(t=t.slice(i,n)){if(n-=i,e.e=r=r-i-1,e.d=[],i=(r+1)%7,r<0&&(i+=7),i<n){for(i&&e.d.push(+t.slice(0,i)),n-=7;i<n;)e.d.push(+t.slice(i,i+=7));i=7-(t=t.slice(i)).length}else i-=n;for(;i--;)t+="0";e.d.push(+t),rS&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function r9(e,t,r,i,n){var s,a,o,l,u=e.precision,d=Math.ceil(u/7);for(rS=!1,l=r.times(r),o=new e(i);;){if(a=rH(o.times(l),new e(t++*t++),u,1),o=n?i.plus(a):i.minus(a),i=rH(a.times(l),new e(t++*t++),u,1),void 0!==(a=o.plus(i)).d[d]){for(s=d;a.d[s]===o.d[s]&&s--;);if(-1==s)break}s=o,o=i,i=a,a=s}return rS=!0,a.d.length=d+1,a}function r7(e,t){for(var r=e;--t;)r*=e;return r}function r5(e,t){var r,i=t.s<0,n=rz(e,e.precision,1),s=n.times(.5);if((t=t.abs()).lte(s))return r_=i?4:1,t;if((r=t.divToInt(n)).isZero())r_=i?3:2;else{if((t=t.minus(r.times(n))).lte(s))return r_=r0(r)?i?2:3:i?4:1,t;r_=r0(r)?i?1:4:i?3:2}return t.minus(n).abs()}function r8(e,t,r,i){var n,s,a,o,l,u,d,c,f,p=e.constructor,h=void 0!==r;if(h?(rM(r,1,1e9),void 0===i?i=p.rounding:rM(i,0,8)):(r=p.precision,i=p.rounding),e.isFinite()){for(a=(d=rJ(e)).indexOf("."),h?(n=2,16==t?r=4*r-3:8==t&&(r=3*r-2)):n=t,a>=0&&(d=d.replace(".",""),(f=new p(1)).e=d.length-a,f.d=rB(rJ(f),10,n),f.e=f.d.length),s=l=(c=rB(d,10,n)).length;0==c[--l];)c.pop();if(c[0]){if(a<0?s--:((e=new p(e)).d=c,e.e=s,c=(e=rH(e,f,r,i,0,n)).d,s=e.e,u=rw),a=c[r],o=n/2,u=u||void 0!==c[r+1],u=i<4?(void 0!==a||u)&&(0===i||i===(e.s<0?3:2)):a>o||a===o&&(4===i||u||6===i&&1&c[r-1]||i===(e.s<0?8:7)),c.length=r,u)for(;++c[--r]>n-1;)c[r]=0,r||(++s,c.unshift(1));for(l=c.length;!c[l-1];--l);for(a=0,d="";a<l;a++)d+=rE.charAt(c[a]);if(h){if(l>1)if(16==t||8==t){for(a=16==t?4:3,--l;l%a;l++)d+="0";for(l=(c=rB(d,n,t)).length;!c[l-1];--l);for(a=1,d="1.";a<l;a++)d+=rE.charAt(c[a])}else d=d.charAt(0)+"."+d.slice(1);d=d+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)d="0"+d;d="0."+d}else if(++s>l)for(s-=l;s--;)d+="0";else s<l&&(d=d.slice(0,s)+"."+d.slice(s))}else d=h?"0p+0":"0";d=(16==t?"0x":2==t?"0b":8==t?"0o":"")+d}else d=r4(e);return e.s<0?"-"+d:d}function ie(e,t){if(e.length>t)return e.length=t,!0}function it(e){return new this(e).abs()}function ir(e){return new this(e).acos()}function ii(e){return new this(e).acosh()}function is(e,t){return new this(e).plus(t)}function ia(e){return new this(e).asin()}function io(e){return new this(e).asinh()}function il(e){return new this(e).atan()}function iu(e){return new this(e).atanh()}function id(e,t){e=new this(e),t=new this(t);var r,i=this.precision,n=this.rounding,s=i+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(r=t.s<0?rz(this,i,n):new this(0)).s=e.s:!e.d||t.isZero()?(r=rz(this,s,1).times(.5)).s=e.s:t.s<0?(this.precision=s,this.rounding=1,r=this.atan(rH(e,t,s,1)),t=rz(this,s,1),this.precision=i,this.rounding=n,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(rH(e,t,s,1)):(r=rz(this,s,1).times(t.s>0?.25:.75)).s=e.s:r=new this(NaN),r}function ic(e){return new this(e).cbrt()}function ip(e){return rW(e=new this(e),e.e+1,2)}function ih(e,t,r){return new this(e).clamp(t,r)}function im(e){if(!e||"object"!=typeof e)throw Error(rR+"Object expected");var t,r,i,n=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(r=s[t],n&&(this[r]=rT[r]),void 0!==(i=e[r]))if(r$(i)===i&&i>=s[t+1]&&i<=s[t+2])this[r]=i;else throw Error(rk+r+": "+i);if(r="crypto",n&&(this[r]=rT[r]),void 0!==(i=e[r]))if(!0===i||!1===i||0===i||1===i)if(i)if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(rA);else this[r]=!1;else throw Error(rk+r+": "+i);return this}function ig(e){return new this(e).cos()}function iy(e){return new this(e).cosh()}function ib(e,t){return new this(e).div(t)}function iv(e){return new this(e).exp()}function iw(e){return rW(e=new this(e),e.e+1,3)}function i_(){var e,t,r=new this(0);for(rS=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return rS=!0,new this(1/0);r=t}return rS=!0,r.sqrt()}function iE(e){return e instanceof iG||e&&e.toStringTag===rN||!1}function ix(e){return new this(e).ln()}function iP(e,t){return new this(e).log(t)}function iT(e){return new this(e).log(2)}function iS(e){return new this(e).log(10)}function iR(){return r1(this,arguments,-1)}function ik(){return r1(this,arguments,1)}function iO(e,t){return new this(e).mod(t)}function iA(e,t){return new this(e).mul(t)}function iN(e,t){return new this(e).pow(t)}function i$(e){var t,r,i,n,s=0,a=new this(1),o=[];if(void 0===e?e=this.precision:rM(e,1,1e9),i=Math.ceil(e/7),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(i));s<i;)(n=t[s])>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:o[s++]=n%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(i*=4);s<i;)(n=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((127&t[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,s):(o.push(n%1e7),s+=4);s=i/4}else throw Error(rA);else for(;s<i;)o[s++]=1e7*Math.random()|0;for(i=o[--s],e%=7,i&&e&&(n=rq(10,7-e),o[s]=(i/n|0)*n);0===o[s];s--)o.pop();if(s<0)r=0,o=[0];else{for(r=-1;0===o[0];r-=7)o.shift();for(i=1,n=o[0];n>=10;n/=10)i++;i<7&&(r-=7-i)}return a.e=r,a.d=o,a}function iq(e){return rW(e=new this(e),e.e+1,this.rounding)}function iD(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function iI(e){return new this(e).sin()}function iV(e){return new this(e).sinh()}function iU(e){return new this(e).sqrt()}function iF(e,t){return new this(e).sub(t)}function iC(){var e=0,t=arguments,r=new this(t[0]);for(rS=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return rS=!0,rW(r,this.precision,this.rounding)}function ij(e){return new this(e).tan()}function iL(e){return new this(e).tanh()}function iM(e){return rW(e=new this(e),e.e+1,1)}rj[Symbol.for("nodejs.util.inspect.custom")]=rj.toString,rj[Symbol.toStringTag]="Decimal";var iG=rj.constructor=function e(t){var r,i,n;function s(e){var t,r,i;if(!(this instanceof s))return new s(e);if(this.constructor=s,iE(e)){this.s=e.s,rS?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(i=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,r=e;r>=10;r/=10)t++;rS?t>s.maxE?(this.e=NaN,this.d=null):t<s.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return r6(this,e.toString())}if("string"===i)return 45===(r=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===r&&(e=e.slice(1)),this.s=1),rU.test(e)?r6(this,e):function(e,t){var r,i,n,s,a,o,l,u,d;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),rU.test(t))return r6(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(rI.test(t))r=16,t=t.toLowerCase();else if(rD.test(t))r=2;else if(rV.test(t))r=8;else throw Error(rk+t);for((s=t.search(/p/i))>0?(l=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),a=(s=t.indexOf("."))>=0,i=e.constructor,a&&(s=(o=(t=t.replace(".","")).length)-s,n=rX(i,new i(r),s,2*s)),s=d=(u=rB(t,r,1e7)).length-1;0===u[s];--s)u.pop();return s<0?new i(0*e.s):(e.e=rK(u,d),e.d=u,rS=!1,a&&(e=rH(e,n,4*o)),l&&(e=e.times(54>Math.abs(l)?rq(2,l):iG.pow(2,l))),rS=!0,e)}(this,e);if("bigint"===i)return e<0?(e=-e,this.s=-1):this.s=1,r6(this,e.toString());throw Error(rk+e)}if(s.prototype=rj,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=im,s.clone=e,s.isDecimal=iE,s.abs=it,s.acos=ir,s.acosh=ii,s.add=is,s.asin=ia,s.asinh=io,s.atan=il,s.atanh=iu,s.atan2=id,s.cbrt=ic,s.ceil=ip,s.clamp=ih,s.cos=ig,s.cosh=iy,s.div=ib,s.exp=iv,s.floor=iw,s.hypot=i_,s.ln=ix,s.log=iP,s.log10=iS,s.log2=iT,s.max=iR,s.min=ik,s.mod=iO,s.mul=iA,s.pow=iN,s.random=i$,s.round=iq,s.sign=iD,s.sin=iI,s.sinh=iV,s.sqrt=iU,s.sub=iF,s.sum=iC,s.tan=ij,s.tanh=iL,s.trunc=iM,void 0===t&&(t={}),t&&!0!==t.defaults)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],r=0;r<n.length;)t.hasOwnProperty(i=n[r++])||(t[i]=this[i]);return s.config(t),s}(rT);rx=new iG(rx),rP=new iG(rP);var iB=iG;function iH(e){var t;return null===e?e:Array.isArray(e)?e.map(iH):"object"==typeof e?null!==(t=e)&&"object"==typeof t&&"string"==typeof t.$type?function({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:e,byteOffset:r,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(e,r,i)}case"DateTime":return new Date(t);case"Decimal":return new iB(t);case"Json":return JSON.parse(t);default:rr(t,"Unknown tagged value")}}(e):null!==e.constructor&&"Object"!==e.constructor.name?e:rc(e,iH):e}var iW=class{_map=new Map;get(e){return this._map.get(e)?.value}set(e,t){this._map.set(e,{value:t})}getOrCreate(e,t){let r=this._map.get(e);if(r)return r.value;let i=t();return this.set(e,i),i}};function iJ(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function iK(e){let t;return{get:()=>(t||(t={value:e()}),t.value)}}function iQ(e){return{models:iz(e.models),enums:iz(e.enums),types:iz(e.types)}}function iz(e){let t={};for(let{name:r,...i}of e)t[r]=i;return t}function iY(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function iZ(e){return"Invalid Date"!==e.toString()}function iX(e){return!!iG.isDecimal(e)||null!==e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.e&&"function"==typeof e.toFixed&&Array.isArray(e.d)}var i0={};function i1(e){return{name:e.name,values:e.values.map(e=>e.name)}}c(i0,{ModelAction:()=>i2,datamodelEnumToSchemaEnum:()=>i1});var i2=(e=>(e.findUnique="findUnique",e.findUniqueOrThrow="findUniqueOrThrow",e.findFirst="findFirst",e.findFirstOrThrow="findFirstOrThrow",e.findMany="findMany",e.create="create",e.createMany="createMany",e.createManyAndReturn="createManyAndReturn",e.update="update",e.updateMany="updateMany",e.updateManyAndReturn="updateManyAndReturn",e.upsert="upsert",e.delete="delete",e.deleteMany="deleteMany",e.groupBy="groupBy",e.count="count",e.aggregate="aggregate",e.findRaw="findRaw",e.aggregateRaw="aggregateRaw",e))(i2||{});p(_()),p(r(73024));var i3={keyword:en,entity:en,value:e=>H(er(e)),punctuation:er,directive:en,function:en,variable:e=>H(er(e)),string:e=>H(ee(e)),boolean:et,number:en,comment:ea},i4=e=>e,i6={},i9=0,i7={manual:i6.Prism&&i6.Prism.manual,disableWorkerMessageHandler:i6.Prism&&i6.Prism.disableWorkerMessageHandler,util:{encode:function(e){return e instanceof i5?new i5(e.type,i7.util.encode(e.content),e.alias):Array.isArray(e)?e.map(i7.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++i9}),e.__id},clone:function e(t,r){let i,n,s=i7.util.type(t);switch(r=r||{},s){case"Object":if(r[n=i7.util.objId(t)])return r[n];for(let s in i={},r[n]=i,t)t.hasOwnProperty(s)&&(i[s]=e(t[s],r));return i;case"Array":return r[n=i7.util.objId(t)]?r[n]:(i=[],r[n]=i,t.forEach(function(t,n){i[n]=e(t,r)}),i);default:return t}}},languages:{extend:function(e,t){let r=i7.util.clone(i7.languages[e]);for(let e in t)r[e]=t[e];return r},insertBefore:function(e,t,r,i){let n=(i=i||i7.languages)[e],s={};for(let e in n)if(n.hasOwnProperty(e)){if(e==t)for(let e in r)r.hasOwnProperty(e)&&(s[e]=r[e]);r.hasOwnProperty(e)||(s[e]=n[e])}let a=i[e];return i[e]=s,i7.languages.DFS(i7.languages,function(t,r){r===a&&t!=e&&(this[t]=s)}),s},DFS:function e(t,r,i,n){n=n||{};let s=i7.util.objId;for(let a in t)if(t.hasOwnProperty(a)){r.call(t,a,t[a],i||a);let o=t[a],l=i7.util.type(o);"Object"!==l||n[s(o)]?"Array"!==l||n[s(o)]||(n[s(o)]=!0,e(o,r,a,n)):(n[s(o)]=!0,e(o,r,null,n))}}},plugins:{},highlight:function(e,t,r){let i={code:e,grammar:t,language:r};return i7.hooks.run("before-tokenize",i),i.tokens=i7.tokenize(i.code,i.grammar),i7.hooks.run("after-tokenize",i),i5.stringify(i7.util.encode(i.tokens),i.language)},matchGrammar:function(e,t,r,i,n,s,a){for(let m in r){if(!r.hasOwnProperty(m)||!r[m])continue;if(m==a)return;let g=r[m];g="Array"===i7.util.type(g)?g:[g];for(let a=0;a<g.length;++a){let y=g[a],b=y.inside,v=!!y.lookbehind,w=!!y.greedy,_=0,E=y.alias;if(w&&!y.pattern.global){let e=y.pattern.toString().match(/[imuy]*$/)[0];y.pattern=RegExp(y.pattern.source,e+"g")}y=y.pattern||y;for(let a=i,g=n;a<t.length;g+=t[a].length,++a){let i=t[a];if(t.length>e.length)return;if(i instanceof i5)continue;if(w&&a!=t.length-1){y.lastIndex=g;var o=y.exec(e);if(!o)break;var l=o.index+(v?o[1].length:0),u=o.index+o[0].length,d=a,c=g;for(let e=t.length;d<e&&(c<u||!t[d].type&&!t[d-1].greedy);++d)l>=(c+=t[d].length)&&(++a,g=c);if(t[a]instanceof i5)continue;f=d-a,i=e.slice(g,c),o.index-=g}else{y.lastIndex=0;var o=y.exec(i),f=1}if(!o){if(s)break;continue}v&&(_=o[1]?o[1].length:0);var l=o.index+_,o=o[0].slice(_),u=l+o.length,p=i.slice(0,l),h=i.slice(u);let n=[a,f];p&&(++a,g+=p.length,n.push(p));let x=new i5(m,b?i7.tokenize(o,b):o,E,o,w);if(n.push(x),h&&n.push(h),Array.prototype.splice.apply(t,n),1!=f&&i7.matchGrammar(e,t,r,a,g,!0,m),s)break}}}},tokenize:function(e,t){let r=[e],i=t.rest;if(i){for(let e in i)t[e]=i[e];delete t.rest}return i7.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=i7.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=i7.hooks.all[e];if(!(!r||!r.length))for(var i,n=0;i=r[n++];)i(t)}},Token:i5};function i5(e,t,r,i,n){this.type=e,this.content=t,this.alias=r,this.length=0|(i||"").length,this.greedy=!!n}i7.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/},i7.languages.javascript=i7.languages.extend("clike",{"class-name":[i7.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/}),i7.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,i7.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:i7.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:i7.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:i7.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:i7.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),i7.languages.markup&&i7.languages.markup.tag.addInlined("script","javascript"),i7.languages.js=i7.languages.javascript,i7.languages.typescript=i7.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/}),i7.languages.ts=i7.languages.typescript,i5.stringify=function(e,t){return"string"==typeof e?e:Array.isArray(e)?e.map(function(e){return i5.stringify(e,t)}).join(""):(i3[e.type]||i4)(e.content)};var i8={red:X,gray:ea,dim:W,bold:H,underline:K,highlightSource:e=>e.highlight()},ne={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function nt(e){let t=e.showColors?i8:ne;return function({functionName:e,location:t,message:r,isPanic:i,contextLines:n,callArguments:s},a){var o;let l,u=[""],d=t?" in":":";if(i?(u.push(a.red(`Oops, an unknown error occurred! This is ${a.bold("on us")}, you did nothing wrong.`)),u.push(a.red(`It occurred in the ${a.bold(`\`${e}\``)} invocation${d}`))):u.push(a.red(`Invalid ${a.bold(`\`${e}\``)} invocation${d}`)),t&&u.push(a.underline((l=[(o=t).fileName],o.lineNumber&&l.push(String(o.lineNumber)),o.columnNumber&&l.push(String(o.columnNumber)),l.join(":")))),n){u.push("");let e=[n.toString()];s&&(e.push(s),e.push(a.dim(")"))),u.push(e.join("")),s&&u.push("")}else u.push(""),s&&u.push(s),u.push("");return u.push(r),u.join(`
`)}(function({callsite:e,message:t,originalMethod:r,isPanic:i,callArguments:n},s){return function({message:e,originalMethod:t,isPanic:r,callArguments:i}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:i}}({message:t,originalMethod:r,isPanic:i,callArguments:n})}(e,0),t)}var nr=p(R());function ni(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function nn(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return -10;default:return 0}}var ns=class{constructor(e,t){this.name=e,this.value=t}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(e){let{colors:{green:t}}=e.context;e.addMarginSymbol(t(this.isRequired?"+":"?")),e.write(t(this.name)),this.isRequired||e.write(t("?")),e.write(t(": ")),"string"==typeof this.value?e.write(t(this.value)):e.write(this.value)}};O();var na=class{constructor(e=0,t){this.context=t,this.currentIndent=e}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(e){return"string"==typeof e?this.currentLine+=e:e.write(this),this}writeJoined(e,t,r=(e,t)=>t.write(e)){let i=t.length-1;for(let n=0;n<t.length;n++)r(t[n],this),n!==i&&this.write(e);return this}writeLine(e){return this.write(e).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let e=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,e?.(),this}withIndent(e){return this.indent(),e(this),this.unindent(),this}afterNextNewline(e){return this.afterNextNewLineCallback=e,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(e){return this.marginSymbol=e,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let e=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+e.slice(1):e}};k();var no=class{constructor(e){this.value=e}write(e){e.write(this.value)}markAsError(){this.value.markAsError()}},nl=e=>e,nu={bold:nl,red:nl,green:nl,dim:nl,enabled:!1},nd={bold:H,red:X,green:ee,dim:W,enabled:!0},nc={write(e){e.writeLine(",")}},nf=class{constructor(e){this.contents=e}isUnderlined=!1;color=e=>e;underline(){return this.isUnderlined=!0,this}setColor(e){return this.color=e,this}write(e){let t=e.getCurrentLineLength();e.write(this.color(this.contents)),this.isUnderlined&&e.afterNextNewline(()=>{e.write(" ".repeat(t)).writeLine(this.color("~".repeat(this.contents.length)))})}},np=class{hasError=!1;markAsError(){return this.hasError=!0,this}},nh=class extends np{items=[];addItem(e){return this.items.push(new no(e)),this}getField(e){return this.items[e]}getPrintWidth(){return 0===this.items.length?2:Math.max(...this.items.map(e=>e.value.getPrintWidth()))+2}write(e){if(0===this.items.length)return void this.writeEmpty(e);this.writeWithItems(e)}writeEmpty(e){let t=new nf("[]");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithItems(e){let{colors:t}=e.context;e.writeLine("[").withIndent(()=>e.writeJoined(nc,this.items).newLine()).write("]"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(t.red("~".repeat(this.getPrintWidth())))})}asObject(){}},nm=class e extends np{fields={};suggestions=[];addField(e){this.fields[e.name]=e}addSuggestion(e){this.suggestions.push(e)}getField(e){return this.fields[e]}getDeepField(t){let[r,...i]=t,n=this.getField(r);if(!n)return;let s=n;for(let t of i){let r;if(s.value instanceof e?r=s.value.getField(t):s.value instanceof nh&&(r=s.value.getField(Number(t))),!r)return;s=r}return s}getDeepFieldValue(e){return 0===e.length?this:this.getDeepField(e)?.value}hasField(e){return!!this.getField(e)}removeAllFields(){this.fields={}}removeField(e){delete this.fields[e]}getFields(){return this.fields}isEmpty(){return 0===Object.keys(this.fields).length}getFieldValue(e){return this.getField(e)?.value}getDeepSubSelectionValue(t){let r=this;for(let i of t){if(!(r instanceof e))return;let t=r.getSubSelectionValue(i);if(!t)return;r=t}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let i=r;for(let r of t){let t=i.value.getFieldValue(r);if(!t||!(t instanceof e))return;let n=t.getSelectionParent();if(!n)return;i=n}return i}getSelectionParent(){let e=this.getField("select")?.value.asObject();if(e)return{kind:"select",value:e};let t=this.getField("include")?.value.asObject();if(t)return{kind:"include",value:t}}getSubSelectionValue(e){return this.getSelectionParent()?.value.fields[e].value}getPrintWidth(){let e=Object.values(this.fields);return 0==e.length?2:Math.max(...e.map(e=>e.getPrintWidth()))+2}write(e){let t=Object.values(this.fields);if(0===t.length&&0===this.suggestions.length)return void this.writeEmpty(e);this.writeWithContents(e,t)}asObject(){return this}writeEmpty(e){let t=new nf("{}");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithContents(e,t){e.writeLine("{").withIndent(()=>{e.writeJoined(nc,[...t,...this.suggestions]).newLine()}),e.write("}"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(e.context.colors.red("~".repeat(this.getPrintWidth())))})}},ng=class extends np{constructor(e){super(),this.text=e}getPrintWidth(){return this.text.length}write(e){let t=new nf(this.text);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t)}asObject(){}},ny=class{fields=[];addField(e,t){return this.fields.push({write(r){let{green:i,dim:n}=r.context.colors;r.write(i(n(`${e}: ${t}`))).addMarginSymbol(i(n("+")))}}),this}write(e){let{colors:{green:t}}=e.context;e.writeLine(t("{")).withIndent(()=>{e.writeJoined(nc,this.fields).newLine()}).write(t("}")).addMarginSymbol(t("+"))}};function nb(e,t,r){let i=[`Unknown argument \`${e.red(t)}\`.`],n=function(e,t){let r=1/0,i;for(let n of t){let t=(0,nr.default)(e,n);t>3||t<r&&(r=t,i=n)}return i}(t,r);return n&&i.push(`Did you mean \`${e.green(n)}\`?`),r.length>0&&i.push(nx(e)),i.join(" ")}function nv(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ns(r.name,"true"))}function nw(e,t){let[r,i]=nE(e),n=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!n)return{parentKind:"unknown",fieldName:i};let s=n.getFieldValue("select")?.asObject(),a=n.getFieldValue("include")?.asObject(),o=n.getFieldValue("omit")?.asObject(),l=s?.getField(i);return s&&l?{parentKind:"select",parent:s,field:l,fieldName:i}:(l=a?.getField(i),a&&l?{parentKind:"include",field:l,parent:a,fieldName:i}:(l=o?.getField(i),o&&l?{parentKind:"omit",field:l,parent:o,fieldName:i}:{parentKind:"unknown",fieldName:i}))}function n_(e,t){if("object"===t.kind)for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ns(r.name,r.typeNames.join(" | ")))}function nE(e){let t=[...e],r=t.pop();if(!r)throw Error("unexpected empty path");return[t,r]}function nx({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function nP(e,t){if(1===t.length)return t[0];let r=[...t],i=r.pop();return`${r.join(", ")} ${e} ${i}`}var nT=class{modelName;name;typeName;isList;isEnum;constructor(e,t,r,i,n){this.modelName=e,this.name=t,this.typeName=r,this.isList=i,this.isEnum=n}_toGraphQLInputType(){let e=this.isList?"List":"",t=this.isEnum?"Enum":"";return`${e}${t}${this.typeName}FieldRefInput<${this.modelName}>`}};function nS(e){return e instanceof nT}var nR=Symbol(),nk=new WeakMap,nO=class{constructor(e){e===nR?nk.set(this,`Prisma.${this._getName()}`):nk.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return nk.get(this)}},nA=class extends nO{_getNamespace(){return"NullTypes"}},nN=class extends nA{#e};nI(nN,"DbNull");var n$=class extends nA{#e};nI(n$,"JsonNull");var nq=class extends nA{#e};nI(nq,"AnyNull");var nD={classes:{DbNull:nN,JsonNull:n$,AnyNull:nq},instances:{DbNull:new nN(nR),JsonNull:new n$(nR),AnyNull:new nq(nR)}};function nI(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var nV=class{constructor(e,t){this.name=e,this.value=t}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+2}write(e){let t=new nf(this.name);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t).write(": ").write(this.value)}},nU=class{arguments;errorMessages=[];constructor(e){this.arguments=e}write(e){e.write(this.arguments)}addErrorMessage(e){this.errorMessages.push(e)}renderAllMessages(e){return this.errorMessages.map(t=>t(e)).join(`
`)}};function nF(e){return new nU(nC(e))}function nC(e){let t=new nm;for(let[r,i]of Object.entries(e)){let e=new nV(r,function e(t){if("string"==typeof t)return new ng(JSON.stringify(t));if("number"==typeof t||"boolean"==typeof t)return new ng(String(t));if("bigint"==typeof t)return new ng(`${t}n`);if(null===t)return new ng("null");if(void 0===t)return new ng("undefined");if(iX(t))return new ng(`new Prisma.Decimal("${t.toFixed()}")`);if(t instanceof Uint8Array)return Buffer.isBuffer(t)?new ng(`Buffer.alloc(${t.byteLength})`):new ng(`new Uint8Array(${t.byteLength})`);if(t instanceof Date){let e=iZ(t)?t.toISOString():"Invalid Date";return new ng(`new Date("${e}")`)}return t instanceof nO?new ng(`Prisma.${t._getName()}`):nS(t)?new ng(`prisma.${iJ(t.modelName)}.$fields.${t.name}`):Array.isArray(t)?function(t){let r=new nh;for(let i of t)r.addItem(e(i));return r}(t):"object"==typeof t?nC(t):new ng(Object.prototype.toString.call(t))}(i));t.addField(e)}return t}function nj(e,t){let r="pretty"===t?nd:nu;return{message:e.renderAllMessages(r),args:new na(0,{colors:r}).write(e).toString()}}function nL({args:e,errors:t,errorFormat:r,callsite:i,originalMethod:n,clientVersion:s,globalOmit:a}){let o=nF(e);for(let e of t)!function e(t,r,i){switch(t.kind){case"MutuallyExclusiveFields":let n;f=t,p=r,(n=p.arguments.getDeepSubSelectionValue(f.selectionPath)?.asObject())&&(n.getField(f.firstField)?.markAsError(),n.getField(f.secondField)?.markAsError()),p.addErrorMessage(e=>`Please ${e.bold("either")} use ${e.green(`\`${f.firstField}\``)} or ${e.green(`\`${f.secondField}\``)}, but ${e.red("not both")} at the same time.`);break;case"IncludeOnScalar":!function(e,t){let[r,i]=nE(e.selectionPath),n=e.outputType,s=t.arguments.getDeepSelectionParent(r)?.value;if(s&&(s.getField(i)?.markAsError(),n))for(let e of n.fields)e.isRelation&&s.addSuggestion(new ns(e.name,"true"));t.addErrorMessage(e=>{let t=`Invalid scalar field ${e.red(`\`${i}\``)} for ${e.bold("include")} statement`;return n?t+=` on model ${e.bold(n.name)}. ${nx(e)}`:t+=".",t+=`
Note that ${e.bold("include")} statements only accept relation fields.`})}(t,r);break;case"EmptySelection":!function(e,t,r){let i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){let r=i.getField("omit")?.value.asObject();if(r){var n,s,a=e,o=t,l=r;for(let e of(l.removeAllFields(),a.outputType.fields))l.addSuggestion(new ns(e.name,"false"));o.addErrorMessage(e=>`The ${e.red("omit")} statement includes every field of the model ${e.bold(a.outputType.name)}. At least one field must be included in the result`);return}if(i.hasField("select")){let r,i,a;return n=e,s=t,r=n.outputType,i=s.arguments.getDeepSelectionParent(n.selectionPath)?.value,a=i?.isEmpty()??!1,i&&(i.removeAllFields(),nv(i,r)),s.addErrorMessage(e=>a?`The ${e.red("`select`")} statement for type ${e.bold(r.name)} must not be empty. ${nx(e)}`:`The ${e.red("`select`")} statement for type ${e.bold(r.name)} needs ${e.bold("at least one truthy value")}.`)}}if(r?.[iJ(e.outputType.name)])return function(e,t){let r=new ny;for(let t of e.outputType.fields)t.isRelation||r.addField(t.name,"false");let i=new ns("omit",r).makeRequired();if(0===e.selectionPath.length)t.arguments.addSuggestion(i);else{let[r,n]=nE(e.selectionPath),s=t.arguments.getDeepSelectionParent(r)?.value.asObject()?.getField(n);if(s){let e=s?.value.asObject()??new nm;e.addSuggestion(i),s.value=e}}t.addErrorMessage(t=>`The global ${t.red("omit")} configuration excludes every field of the model ${t.bold(e.outputType.name)}. At least one field must be included in the result`)}(e,t);t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}(t,r,i);break;case"UnknownSelectionField":!function(e,t){let r=nw(e.selectionPath,t);if("unknown"!==r.parentKind){r.field.markAsError();let t=r.parent;switch(r.parentKind){case"select":nv(t,e.outputType);break;case"include":var i=t,n=e.outputType;for(let e of n.fields)e.isRelation&&!i.hasField(e.name)&&i.addSuggestion(new ns(e.name,"true"));break;case"omit":var s=t,a=e.outputType;for(let e of a.fields)s.hasField(e.name)||e.isRelation||s.addSuggestion(new ns(e.name,"true"))}}t.addErrorMessage(t=>{let i=[`Unknown field ${t.red(`\`${r.fieldName}\``)}`];return"unknown"!==r.parentKind&&i.push(`for ${t.bold(r.parentKind)} statement`),i.push(`on model ${t.bold(`\`${e.outputType.name}\``)}.`),i.push(nx(t)),i.join(" ")})}(t,r);break;case"InvalidSelectionValue":let s;h=t,m=r,"unknown"!==(s=nw(h.selectionPath,m)).parentKind&&s.field.value.markAsError(),m.addErrorMessage(e=>`Invalid value for selection field \`${e.red(s.fieldName)}\`: ${h.underlyingError}`);break;case"UnknownArgument":let a,o;g=t,y=r,a=g.argumentPath[0],(o=y.arguments.getDeepSubSelectionValue(g.selectionPath)?.asObject())&&(o.getField(a)?.markAsError(),function(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ns(r.name,r.typeNames.join(" | ")))}(o,g.arguments)),y.addErrorMessage(e=>nb(e,a,g.arguments.map(e=>e.name)));break;case"UnknownInputField":!function(e,t){let[r,i]=nE(e.argumentPath),n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){n.getDeepField(e.argumentPath)?.markAsError();let t=n.getDeepFieldValue(r)?.asObject();t&&n_(t,e.inputType)}t.addErrorMessage(t=>nb(t,i,e.inputType.fields.map(e=>e.name)))}(t,r);break;case"RequiredArgumentMissing":!function(e,t){let r;t.addErrorMessage(e=>r?.value instanceof ng&&"null"===r.value.text?`Argument \`${e.green(s)}\` must not be ${e.red("null")}.`:`Argument \`${e.green(s)}\` is missing.`);let i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!i)return;let[n,s]=nE(e.argumentPath),a=new ny,o=i.getDeepFieldValue(n)?.asObject();if(o)if((r=o.getField(s))&&o.removeField(s),1===e.inputTypes.length&&"object"===e.inputTypes[0].kind){for(let t of e.inputTypes[0].fields)a.addField(t.name,t.typeNames.join(" | "));o.addSuggestion(new ns(s,a).makeRequired())}else{let t=e.inputTypes.map(function e(t){return"list"===t.kind?`${e(t.elementType)}[]`:t.name}).join(" | ");o.addSuggestion(new ns(s,t).makeRequired())}}(t,r);break;case"InvalidArgumentType":let l,u;b=t,v=r,l=b.argument.name,(u=v.arguments.getDeepSubSelectionValue(b.selectionPath)?.asObject())&&u.getDeepFieldValue(b.argumentPath)?.markAsError(),v.addErrorMessage(e=>{let t=nP("or",b.argument.typeNames.map(t=>e.green(t)));return`Argument \`${e.bold(l)}\`: Invalid value provided. Expected ${t}, provided ${e.red(b.inferredType)}.`});break;case"InvalidArgumentValue":let d,c;w=t,_=r,d=w.argument.name,(c=_.arguments.getDeepSubSelectionValue(w.selectionPath)?.asObject())&&c.getDeepFieldValue(w.argumentPath)?.markAsError(),_.addErrorMessage(e=>{let t=[`Invalid value for argument \`${e.bold(d)}\``];if(w.underlyingError&&t.push(`: ${w.underlyingError}`),t.push("."),w.argument.typeNames.length>0){let r=nP("or",w.argument.typeNames.map(t=>e.green(t)));t.push(` Expected ${r}.`)}return t.join("")});break;case"ValueTooLarge":var f,p,h,m,g,y,b,v,w,_,E=t,x=r;let P=E.argument.name,T=x.arguments.getDeepSubSelectionValue(E.selectionPath)?.asObject(),S;if(T){let e=T.getDeepField(E.argumentPath)?.value;e?.markAsError(),e instanceof ng&&(S=e.text)}x.addErrorMessage(e=>{let t=["Unable to fit value"];return S&&t.push(e.red(S)),t.push(`into a 64-bit signed integer for field \`${e.bold(P)}\``),t.join(" ")});break;case"SomeFieldsMissing":var R=t,k=r;let O=R.argumentPath[R.argumentPath.length-1],A=k.arguments.getDeepSubSelectionValue(R.selectionPath)?.asObject();if(A){let e=A.getDeepFieldValue(R.argumentPath)?.asObject();e&&n_(e,R.inputType)}k.addErrorMessage(e=>{let t=[`Argument \`${e.bold(O)}\` of type ${e.bold(R.inputType.name)} needs`];return 1===R.constraints.minFieldCount?R.constraints.requiredFields?t.push(`${e.green("at least one of")} ${nP("or",R.constraints.requiredFields.map(t=>`\`${e.bold(t)}\``))} arguments.`):t.push(`${e.green("at least one")} argument.`):t.push(`${e.green(`at least ${R.constraints.minFieldCount}`)} arguments.`),t.push(nx(e)),t.join(" ")});break;case"TooManyFieldsGiven":var N=t,$=r;let q=N.argumentPath[N.argumentPath.length-1],D=$.arguments.getDeepSubSelectionValue(N.selectionPath)?.asObject(),I=[];if(D){let e=D.getDeepFieldValue(N.argumentPath)?.asObject();e&&(e.markAsError(),I=Object.keys(e.getFields()))}$.addErrorMessage(e=>{let t=[`Argument \`${e.bold(q)}\` of type ${e.bold(N.inputType.name)} needs`];return 1===N.constraints.minFieldCount&&1==N.constraints.maxFieldCount?t.push(`${e.green("exactly one")} argument,`):1==N.constraints.maxFieldCount?t.push(`${e.green("at most one")} argument,`):t.push(`${e.green(`at most ${N.constraints.maxFieldCount}`)} arguments,`),t.push(`but you provided ${nP("and",I.map(t=>e.red(t)))}. Please choose`),1===N.constraints.maxFieldCount?t.push("one."):t.push(`${N.constraints.maxFieldCount}.`),t.join(" ")});break;case"Union":let V;(V=function(e){var t=(e,t)=>{let r=ni(e),i=ni(t);return r!==i?r-i:nn(e)-nn(t)};if(0===e.length)return;let r=e[0];for(let i=1;i<e.length;i++)0>t(r,e[i])&&(r=e[i]);return r}(function(e){let t=new Map,r=[];for(let s of e){var i,n;if("InvalidArgumentType"!==s.kind){r.push(s);continue}let e=`${s.selectionPath.join(".")}:${s.argumentPath.join(".")}`,a=t.get(e);a?t.set(e,{...s,argument:{...s.argument,typeNames:(i=a.argument.typeNames,n=s.argument.typeNames,[...new Set(i.concat(n))])}}):t.set(e,s)}return r.push(...t.values()),r}(function e(t){return t.errors.flatMap(t=>"Union"===t.kind?e(t):[t])}(t))))?e(V,r,i):r.addErrorMessage(()=>"Unknown error");break;default:throw Error("not implemented: "+t.kind)}}(e,o,a);let{message:l,args:u}=nj(o,r);throw new rv(nt({message:l,callsite:i,originalMethod:n,showColors:"pretty"===r,callArguments:u}),{clientVersion:s})}function nM(e){return e.replace(/^./,e=>e.toLowerCase())}function nG(e,t,r){return r?rc(r,({needs:e,compute:r},i)=>{var n,s,a;let o;return{name:i,needs:e?Object.keys(e).filter(t=>e[t]):[],compute:(n=t,s=i,a=r,(o=n?.[s]?.compute)?e=>a({...e,[s]:o(e)}):a)}}):{}}var nB=class{constructor(e,t){this.extension=e,this.previous=t}computedFieldsCache=new iW;modelExtensionsCache=new iW;queryCallbacksCache=new iW;clientExtensions=iK(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=iK(()=>{let e=this.previous?.getAllBatchQueryCallbacks()??[],t=this.extension.query?.$__internalBatch;return t?e.concat(t):e});getAllComputedFields(e){return this.computedFieldsCache.getOrCreate(e,()=>{var t,r,i;let n,s,a;return t=this.previous?.getAllComputedFields(e),r=this.extension,n=nM(e),r.result&&(r.result.$allModels||r.result[n])?(i={...t,...nG(r.name,t,r.result.$allModels),...nG(r.name,t,r.result[n])},s=new iW,a=(e,t)=>s.getOrCreate(e,()=>t.has(e)?[e]:(t.add(e),i[e]?i[e].needs.flatMap(e=>a(e,t)):[e])),rc(i,e=>({...e,needs:a(e.name,new Set)}))):t})}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(e){return this.modelExtensionsCache.getOrCreate(e,()=>{let t=nM(e);return this.extension.model&&(this.extension.model[t]||this.extension.model.$allModels)?{...this.previous?.getAllModelExtensions(e),...this.extension.model.$allModels,...this.extension.model[t]}:this.previous?.getAllModelExtensions(e)})}getAllQueryCallbacks(e,t){return this.queryCallbacksCache.getOrCreate(`${e}:${t}`,()=>{let r=this.previous?.getAllQueryCallbacks(e,t)??[],i=[],n=this.extension.query;return n&&(n[e]||n.$allModels||n[t]||n.$allOperations)?(void 0!==n[e]&&(void 0!==n[e][t]&&i.push(n[e][t]),void 0!==n[e].$allOperations&&i.push(n[e].$allOperations)),"$none"!==e&&void 0!==n.$allModels&&(void 0!==n.$allModels[t]&&i.push(n.$allModels[t]),void 0!==n.$allModels.$allOperations&&i.push(n.$allModels.$allOperations)),void 0!==n[t]&&i.push(n[t]),void 0!==n.$allOperations&&i.push(n.$allOperations),r.concat(i)):r})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},nH=class e{constructor(e){this.head=e}static empty(){return new e}static single(t){return new e(new nB(t))}isEmpty(){return void 0===this.head}append(t){return new e(new nB(t,this.head))}getAllComputedFields(e){return this.head?.getAllComputedFields(e)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(e){return this.head?.getAllModelExtensions(e)}getAllQueryCallbacks(e,t){return this.head?.getAllQueryCallbacks(e,t)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}},nW=class{constructor(e){this.name=e}};function nJ(e){return new nW(e)}var nK=Symbol(),nQ=class{constructor(e){if(e!==nK)throw Error("Skip instance can not be constructed directly")}ifUndefined(e){return void 0===e?nz:e}},nz=new nQ(nK);function nY(e){return e instanceof nQ}var nZ={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},nX="explicitly `undefined` values are not allowed";function n0({modelName:e,action:t,args:r,runtimeDataModel:i,extensions:n=nH.empty(),callsite:s,clientMethod:a,errorFormat:o,clientVersion:l,previewFeatures:u,globalOmit:d}){let c=new n3({runtimeDataModel:i,modelName:e,action:t,rootArgs:r,callsite:s,extensions:n,selectionPath:[],argumentPath:[],originalMethod:a,errorFormat:o,clientVersion:l,previewFeatures:u,globalOmit:d});return{modelName:e,action:nZ[t],query:function e({select:t,include:r,...i}={},n){var s,a,o,l,u,d,c;let f,p=i.omit;return delete i.omit,{arguments:n1(i,n),selection:(s=t,a=r,o=p,l=n,s?(a?l.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:l.getSelectionPath()}):o&&l.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:l.getSelectionPath()}),function(t,r){let i={},n=r.getComputedFields();for(let[s,a]of Object.entries(function(e,t){if(!t)return e;let r={...e};for(let i of Object.values(t))if(e[i.name])for(let e of i.needs)r[e]=!0;return r}(t,n))){if(nY(a))continue;let t=r.nestSelection(s);n2(a,t);let o=r.findField(s);if(!(n?.[s]&&!o)){if(!1===a||void 0===a||nY(a)){i[s]=!1;continue}if(!0===a){o?.kind==="object"?i[s]=e({},t):i[s]=!0;continue}i[s]=e(a,t)}}return i}(s,l)):(u=l,d=a,c=o,f={},u.modelOrType&&!u.isRawAction()&&(f.$composites=!0,f.$scalars=!0),d&&function(t,r,i){for(let[n,s]of Object.entries(r)){if(nY(s))continue;let r=i.nestSelection(n);if(n2(s,r),!1===s||void 0===s){t[n]=!1;continue}let a=i.findField(n);if(a&&"object"!==a.kind&&i.throwValidationError({kind:"IncludeOnScalar",selectionPath:i.getSelectionPath().concat(n),outputType:i.getOutputTypeDescription()}),a){t[n]=e(!0===s?{}:s,r);continue}if(!0===s){t[n]=!0;continue}t[n]=e(s,r)}}(f,d,u),function(e,t,r){let i=r.getComputedFields();for(let[n,s]of Object.entries(function(e,t){if(!t)return e;let r={...e};for(let i of Object.values(t))if(!e[i.name])for(let e of i.needs)delete r[e];return r}({...r.getGlobalOmit(),...t},i))){if(nY(s))continue;n2(s,r.nestSelection(n));let t=r.findField(n);i?.[n]&&!t||(e[n]=!s)}}(f,c,u),f))}}(r,c)}}function n1(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let i in e){let n=e[i],s=t.nestArgument(i);nY(n)||(void 0!==n?r[i]=function e(t,r){var i,n;if(null===t)return null;if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return t;if("bigint"==typeof t)return{$type:"BigInt",value:String(t)};if(iY(t)){if(iZ(t))return{$type:"DateTime",value:t.toISOString()};r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:r.getSelectionPath(),argumentPath:r.getArgumentPath(),argument:{name:r.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(t instanceof nW)return{$type:"Param",value:t.name};if(nS(t))return{$type:"FieldRef",value:{_ref:t.name,_container:t.modelName}};if(Array.isArray(t))return function(t,r){let i=[];for(let n=0;n<t.length;n++){let s=r.nestArgument(String(n)),a=t[n];if(void 0===a||nY(a)){let e=void 0===a?"undefined":"Prisma.skip";r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:s.getSelectionPath(),argumentPath:s.getArgumentPath(),argument:{name:`${r.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${e}\` value within array. Use \`null\` or filter out \`${e}\` values`})}i.push(e(a,s))}return i}(t,r);if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:r,byteLength:i}=t;return{$type:"Bytes",value:Buffer.from(e,r,i).toString("base64")}}if("object"==typeof(i=t)&&null!==i&&!0===i.__prismaRawParameters__)return t.values;if(iX(t))return{$type:"Decimal",value:t.toFixed()};if(t instanceof nO){if(t!==nD.instances[t._getName()])throw Error("Invalid ObjectEnumValue");return{$type:"Enum",value:t._getName()}}return"object"==typeof(n=t)&&null!==n&&"function"==typeof n.toJSON?t.toJSON():"object"==typeof t?n1(t,r):void r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:r.getSelectionPath(),argumentPath:r.getArgumentPath(),argument:{name:r.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(t)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}(n,s):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:s.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:nX}))}return r}function n2(e,t){void 0===e&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:nX})}var n3=class e{constructor(e){this.params=e,this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(e){nL({errors:[e],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(e=>({name:e.name,typeName:"boolean",isRelation:"object"===e.kind}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(e){return this.params.previewFeatures.includes(e)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(e){return this.modelOrType?.fields.find(t=>t.name===e)}nestSelection(t){let r=this.findField(t),i=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:i,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[iJ(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:rr(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function n4(e){if(!e._hasPreviewFlag("metrics"))throw new rv("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var n6=class{_client;constructor(e){this._client=e}prometheus(e){return n4(this._client),this._client._engine.metrics({format:"prometheus",...e})}json(e){return n4(this._client),this._client._engine.metrics({format:"json",...e})}};function n9(e,t){let r=iK(()=>{var e;return{datamodel:{models:n7((e=t).models),enums:n7(e.enums),types:n7(e.types)}}});Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function n7(e){return Object.entries(e).map(([e,t])=>({name:e,...t}))}var n5=new WeakMap,n8="$$PrismaTypedSql",se=class{constructor(e,t){n5.set(this,{sql:e,values:t}),Object.defineProperty(this,n8,{value:n8})}get sql(){return n5.get(this).sql}get values(){return n5.get(this).values}};function st(e){return(...t)=>new se(e,t)}function sr(e){return null!=e&&e[n8]===n8}var si=p(b()),sn=r(16698),ss=r(78474),sa=p(r(73024)),so=p(r(76760)),sl=class e{constructor(t,r){if(t.length-1!==r.length)throw 0===t.length?TypeError("Expected at least 1 string"):TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let i=r.reduce((t,r)=>t+(r instanceof e?r.values.length:1),0);this.values=Array(i),this.strings=Array(i+1),this.strings[0]=t[0];let n=0,s=0;for(;n<r.length;){let i=r[n++],a=t[n];if(i instanceof e){this.strings[s]+=i.strings[0];let e=0;for(;e<i.values.length;)this.values[s++]=i.values[e++],this.strings[s]=i.strings[e];this.strings[s]+=a}else this.values[s++]=i,this.strings[s]=a}}get sql(){let e=this.strings.length,t=1,r=this.strings[0];for(;t<e;)r+=`?${this.strings[t++]}`;return r}get statement(){let e=this.strings.length,t=1,r=this.strings[0];for(;t<e;)r+=`:${t}${this.strings[t++]}`;return r}get text(){let e=this.strings.length,t=1,r=this.strings[0];for(;t<e;)r+=`$${t}${this.strings[t++]}`;return r}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function su(e,t=",",r="",i=""){if(0===e.length)throw TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new sl([r,...Array(e.length-1).fill(t),i],e)}function sd(e){return new sl([e],[])}var sc=sd("");function sf(e,...t){return new sl(e,t)}function sp(e){return{getKeys:()=>Object.keys(e),getPropertyValue:t=>e[t]}}function sh(e,t){return{getKeys:()=>[e],getPropertyValue:()=>t()}}function sm(e){let t=new iW;return{getKeys:()=>e.getKeys(),getPropertyValue:r=>t.getOrCreate(r,()=>e.getPropertyValue(r)),getPropertyDescriptor:t=>e.getPropertyDescriptor?.(t)}}var sg={enumerable:!0,configurable:!0,writable:!0};function sy(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>sg,has:(e,r)=>t.has(r),set:(e,r,i)=>t.add(r)&&Reflect.set(e,r,i),ownKeys:()=>[...t]}}var sb=Symbol.for("nodejs.util.inspect.custom");function sv(e,t){let r=function(e){let t=new Map;for(let r of e)for(let e of r.getKeys())t.set(e,r);return t}(t),i=new Set,n=new Proxy(e,{get(e,t){if(i.has(t))return e[t];let n=r.get(t);return n?n.getPropertyValue(t):e[t]},has(e,t){if(i.has(t))return!0;let n=r.get(t);return n?n.has?.(t)??!0:Reflect.has(e,t)},ownKeys:e=>[...new Set([...sw(Reflect.ownKeys(e),r),...sw(Array.from(r.keys()),r),...i])],set:(e,t,n)=>r.get(t)?.getPropertyDescriptor?.(t)?.writable!==!1&&(i.add(t),Reflect.set(e,t,n)),getOwnPropertyDescriptor(e,t){let i=Reflect.getOwnPropertyDescriptor(e,t);if(i&&!i.configurable)return i;let n=r.get(t);return n?n.getPropertyDescriptor?{...sg,...n?.getPropertyDescriptor(t)}:sg:i},defineProperty:(e,t,r)=>(i.add(t),Reflect.defineProperty(e,t,r)),getPrototypeOf:()=>Object.prototype});return n[sb]=function(){let e={...this};return delete e[sb],e},n}function sw(e,t){return e.filter(e=>t.get(e)?.has?.(e)??!0)}function s_(e){return{getKeys:()=>e,has:()=>!1,getPropertyValue(){}}}function sE(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function sx({error:e,user_facing_error:t},r,i){var n,s;let a;return t.error_code?new rg((n=t,s=i,a=n.message,("postgresql"===s||"postgres"===s||"mysql"===s)&&"P2037"===n.error_code&&(a+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),a),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new rb(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}var sP="<unknown>",sT=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,sS=/\((\S*)(?::(\d+))(?::(\d+))\)/,sR=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,sk=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,sO=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,sA=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i,sN=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i,s$=class{getLocation(){return null}},sq=class{_error;constructor(){this._error=Error()}getLocation(){let e=this._error.stack;if(!e)return null;let t=e.split(`
`).reduce(function(e,t){var r,i,n,s,a,o,l=function(e){var t=sT.exec(e);if(!t)return null;var r=t[2]&&0===t[2].indexOf("native"),i=t[2]&&0===t[2].indexOf("eval"),n=sS.exec(t[2]);return i&&null!=n&&(t[2]=n[1],t[3]=n[2],t[4]=n[3]),{file:r?null:t[2],methodName:t[1]||sP,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}(t)||(r=t,(i=sR.exec(r))?{file:i[2],methodName:i[1]||sP,arguments:[],lineNumber:+i[3],column:i[4]?+i[4]:null}:null)||function(e){var t=sk.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,i=sO.exec(t[3]);return r&&null!=i&&(t[3]=i[1],t[4]=i[2],t[5]=null),{file:t[3],methodName:t[1]||sP,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}(t)||(n=t,(s=sN.exec(n))?{file:s[2],methodName:s[1]||sP,arguments:[],lineNumber:+s[3],column:s[4]?+s[4]:null}:null)||(a=t,(o=sA.exec(a))?{file:o[3],methodName:o[1]||sP,arguments:[],lineNumber:+o[4],column:o[5]?+o[5]:null}:null);return l&&e.push(l),e},[]).find(e=>{var t;if(!e.file)return!1;let r=(t=e.file,ri.default.sep===ri.default.posix.sep?t:t.split(ri.default.sep).join(ri.default.posix.sep));return"<anonymous>"!==r&&!r.includes("@prisma")&&!r.includes("/packages/client/src/runtime/")&&!r.endsWith("/runtime/binary.js")&&!r.endsWith("/runtime/library.js")&&!r.endsWith("/runtime/edge.js")&&!r.endsWith("/runtime/edge-esm.js")&&!r.startsWith("internal/")&&!e.methodName.includes("new ")&&!e.methodName.includes("getCallSite")&&!e.methodName.includes("Proxy.")&&e.methodName.split(".").length<4});return t&&t.file?{fileName:t.file,lineNumber:t.lineNumber,columnNumber:t.column}:null}};function sD(e){return"minimal"===e?"function"==typeof $EnabledCallSite&&"minimal"!==e?new $EnabledCallSite:new s$:new sq}var sI={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function sV(e={}){return Object.entries(function(e={}){return"boolean"==typeof e._count?{...e,_count:{_all:e._count}}:e}(e)).reduce((e,[t,r])=>(void 0!==sI[t]?e.select[t]={select:r}:e[t]=r,e),{select:{}})}function sU(e={}){return t=>("boolean"==typeof e._count&&(t._count=t._count._all),t)}function sF(e={}){let{select:t,...r}=e;return"object"==typeof t?sV({...r,_count:t}):sV({...r,_count:{_all:!0}})}function sC(e={}){let t=sV(e);if(Array.isArray(t.by))for(let e of t.by)"string"==typeof e&&(t.select[e]=!0);else"string"==typeof t.by&&(t.select[t.by]=!0);return t}var sj=e=>Array.isArray(e)?e:e.split("."),sL=(e,t)=>sj(t).reduce((e,t)=>e&&e[t],e),sM=(e,t,r)=>sj(t).reduceRight((t,r,i,n)=>Object.assign({},sL(e,n.slice(0,i)),{[r]:t}),r),sG=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],sB=["aggregate","count","groupBy"];function sH(e,t){var r,i,n,s;let a,o,l=e._extensions.getAllModelExtensions(t)??{};return sv({},[(r=e,a=nM(i=t),o=Object.keys(i2).concat("count"),{getKeys:()=>o,getPropertyValue(e){var t;let n=t=>n=>{let s=sD(r._errorFormat);return r._createPrismaPromise(o=>{let l={args:n,dataPath:[],action:e,model:i,clientMethod:`${a}.${e}`,jsModelName:a,transaction:o,callsite:s};return r._request({...l,...t})},{action:e,args:n,model:i})};return sG.includes(e)?function e(t,r,i,n,s,a){let o=t._runtimeDataModel.models[r].fields.reduce((e,t)=>({...e,[t.name]:t}),{});return l=>{var u,d;let c=sD(t._errorFormat),f=void 0===n||void 0===s?[]:[...s,"select",n],p=void 0===a?l??{}:sM(a,f,l||!0),h=i({dataPath:f,callsite:c})(p),m=(u=t,d=r,u._runtimeDataModel.models[d].fields.filter(e=>"object"===e.kind).map(e=>e.name));return new Proxy(h,{get:(r,n)=>m.includes(n)?e(t,o[n].type,i,n,f,p):r[n],...sy([...m,...Object.getOwnPropertyNames(h)])})}}(r,i,n):(t=e,sB.includes(t))?"aggregate"===e?e=>n({action:"aggregate",unpacker:sU(e),argsMapper:sV})(e):"count"===e?e=>n({action:"count",unpacker:function(e={}){return"object"==typeof e.select?t=>sU(e)(t)._count:t=>sU(e)(t)._count._all}(e),argsMapper:sF})(e):"groupBy"===e?e=>n({action:"groupBy",unpacker:function(e={}){return t=>("boolean"==typeof e?._count&&t.forEach(e=>{e._count=e._count._all}),t)}(e),argsMapper:sC})(e):void 0:n({})}}),(n=e,s=t,sm(sh("fields",()=>{let e,t=n._runtimeDataModel.models[s];return new Proxy({},{get(t,r){if(r in t||"symbol"==typeof r)return t[r];let i=e[r];if(i)return new nT(s,r,i.type,i.isList,"enum"===i.kind)},...sy(Object.keys(e=function(e,t){let r={};for(let i of e)r[i[t]]=i;return r}(t.fields.filter(e=>!e.relationName),"name")))})}))),sp(l),sh("name",()=>t),sh("$name",()=>t),sh("$parent",()=>e._appliedParent)])}var sW=Symbol();function sJ(e){var t,r;let i,n,s,a,o=[(i=[...new Set(Object.getOwnPropertyNames(Object.getPrototypeOf((t=e)._originalClient)))],{getKeys:()=>i,getPropertyValue:e=>t[e]}),(s=(n=Object.keys((r=e)._runtimeDataModel.models)).map(nM),a=[...new Set(n.concat(s))],sm({getKeys:()=>a,getPropertyValue(e){let t=e.replace(/^./,e=>e.toUpperCase());return void 0!==r._runtimeDataModel.models[t]?sH(r,t):void 0!==r._runtimeDataModel.models[e]?sH(r,e):void 0},getPropertyDescriptor(e){if(!s.includes(e))return{enumerable:!1}}})),sh(sW,()=>e),sh("$parent",()=>e._appliedParent)],l=e._extensions.getAllClientExtensions();return l&&o.push(sp(l)),sv(e,o)}function sK(e){if("function"==typeof e)return e(this);if(e.client?.__AccelerateEngine){let t=e.client.__AccelerateEngine;this._originalClient._engine=new t(this._originalClient._accelerateEngineConfig)}return sJ(Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}}))}function sQ({visitor:e,result:t,args:r,runtimeDataModel:i,modelName:n}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=sQ({result:t[s],args:r,modelName:n,runtimeDataModel:i,visitor:e});return t}let s=e(t,n,r)??t;return r.include&&sz({includeOrSelect:r.include,result:s,parentModelName:n,runtimeDataModel:i,visitor:e}),r.select&&sz({includeOrSelect:r.select,result:s,parentModelName:n,runtimeDataModel:i,visitor:e}),s}function sz({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:i,visitor:n}){for(let[s,a]of Object.entries(e)){if(!a||null==t[s]||nY(a))continue;let e=i.models[r].fields.find(e=>e.name===s);if(!e||"object"!==e.kind||!e.relationName)continue;let o="object"==typeof a?a:{};t[s]=sQ({visitor:n,result:t[s],args:o,modelName:e.type,runtimeDataModel:i})}}var sY=["$connect","$disconnect","$on","$transaction","$use","$extends"];function sZ(e){if("object"!=typeof e||null==e||e instanceof nO||nS(e))return e;if(iX(e))return new iB(e.toFixed());if(iY(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=sZ(e[t]);return r}if("object"==typeof e){let t={};for(let r in e)"__proto__"===r?Object.defineProperty(t,r,{value:sZ(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=sZ(e[r]);return t}rr(e,"Unknown value")}var sX=e=>e;function s0(e=sX,t=sX){return r=>e(t(r))}var s1=eE("prisma:client"),s2={Vercel:"vercel","Netlify CI":"netlify"},s3=()=>globalThis.process?.release?.name==="node",s4=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,s6=()=>!!globalThis.Deno,s9=()=>"object"==typeof globalThis.Netlify,s7=()=>"object"==typeof globalThis.EdgeRuntime,s5=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers",s8={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function ae(){let e=[[s9,"netlify"],[s7,"edge-light"],[s5,"workerd"],[s6,"deno"],[s4,"bun"],[s3,"node"]].flatMap(e=>e[0]()?[e[1]]:[]).at(0)??"";return{id:e,prettyName:s8[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var at=p(r(73024)),ar=p(r(76760));function ai(e){let{runtimeBinaryTarget:t}=e;return`Add "${t}" to \`binaryTargets\` in the "schema.prisma" file and run \`prisma generate\` after saving it:

${function(e){let{generator:t,generatorBinaryTargets:r,runtimeBinaryTarget:i}=e,n=[...r,{fromEnvVar:null,value:i}];return String(new t2({...t,binaryTargets:n}))}(e)}`}function an(e){let{runtimeBinaryTarget:t}=e;return`Prisma Client could not locate the Query Engine for runtime "${t}".`}function as(e){let{searchedLocations:t}=e;return`The following locations have been searched:
${[...new Set(t)].map(e=>`  ${e}`).join(`
`)}`}function aa(e){return`We would appreciate if you could take the time to share some information with us.
Please help us by answering a few questions: https://pris.ly/${e}`}function ao(e){let{errorStack:t}=e;return t?.match(/\/\.next|\/next@|\/next\//)?`

We detected that you are using Next.js, learn how to fix this: https://pris.ly/d/engine-not-found-nextjs.`:""}var al=eE("prisma:client:engines:resolveEnginePath"),au=()=>RegExp("runtime[\\\\/]library\\.m?js$");async function ad(e,t){let r={binary:process.env.PRISMA_QUERY_ENGINE_BINARY,library:process.env.PRISMA_QUERY_ENGINE_LIBRARY}[e]??t.prismaPath;if(void 0!==r)return r;let{enginePath:i,searchedLocations:n}=await ac(e,t);if(al("enginePath",i),void 0!==i&&"binary"===e&&function(e){if("win32"===process.platform)return;let t=tY.default.statSync(e),r=64|t.mode|9;if(t.mode===r)return tZ(`Execution permissions of ${e} are fine`);let i=r.toString(8).slice(-3);tZ(`Have to call chmodPlusX on ${e}`),tY.default.chmodSync(e,i)}(i),void 0!==i)return t.prismaPath=i;let s=await te(),a=t.generator?.binaryTargets??[],o=a.some(e=>e.native),l=!a.some(e=>e.value===s),u=null===__filename.match(au()),d={searchedLocations:n,generatorBinaryTargets:a,generator:t.generator,runtimeBinaryTarget:s,queryEngineName:af(e,s),expectedLocation:ar.default.relative(process.cwd(),t.dirname),errorStack:Error().stack},c;throw new rm(o&&l?function(e){let{runtimeBinaryTarget:t,generatorBinaryTargets:r}=e,i=r.find(e=>e.native);return`${an(e)}

This happened because Prisma Client was generated for "${i?.value??"unknown"}", but the actual deployment required "${t}".
${ai(e)}

${as(e)}`}(d):l?function(e){let{runtimeBinaryTarget:t}=e;return`${an(e)}

This happened because \`binaryTargets\` have been pinned, but the actual deployment also required "${t}".
${ai(e)}

${as(e)}`}(d):u?function(e){let{queryEngineName:t}=e;return`${an(e)}${ao(e)}

This is likely caused by a bundler that has not copied "${t}" next to the resulting bundle.
Ensure that "${t}" has been copied next to the bundle or in "${e.expectedLocation}".

${aa("engine-not-found-bundler-investigation")}

${as(e)}`}(d):function(e){let{queryEngineName:t}=e;return`${an(e)}${ao(e)}

This is likely caused by tooling that has not copied "${t}" to the deployment folder.
Ensure that you ran \`prisma generate\` and that "${t}" has been copied to "${e.expectedLocation}".

${aa("engine-not-found-tooling-investigation")}

${as(e)}`}(d),t.clientVersion)}async function ac(e,t){let r=await te(),i=[],n=[t.dirname,ar.default.resolve(__dirname,".."),t.generator?.output?.value??__dirname,ar.default.resolve(__dirname,"../../../.prisma/client"),"/tmp/prisma-engines",t.cwd];for(let t of(__filename.includes("resolveEnginePath")&&n.push(tz.default.join(__dirname,"../")),n)){let n=af(e,r),s=ar.default.join(t,n);if(i.push(t),at.default.existsSync(s))return{enginePath:s,searchedLocations:i}}return{enginePath:void 0,searchedLocations:i}}function af(e,t){return"library"===e?t.includes("windows")?`query_engine-${t}.dll.node`:t.includes("darwin")?`${eP}-${t}.dylib.node`:`${eP}-${t}.so.node`:`query-engine-${t}${"windows"===t?".exe":""}`}var ap=p(x()),ah=p(S());function am(e){return"DriverAdapterError"===e.name&&"object"==typeof e.cause}function ag(e){return{ok:!0,value:e,map:t=>ag(t(e)),flatMap:t=>t(e)}}function ay(e){return{ok:!1,error:e,map:()=>ay(e),flatMap:()=>ay(e)}}var ab=eE("driver-adapter-utils"),av=class{registeredErrors=[];consumeError(e){return this.registeredErrors[e]}registerNewError(e){let t=0;for(;void 0!==this.registeredErrors[t];)t++;return this.registeredErrors[t]={error:e},t}},aw=(e,t=new av)=>{var r,i;let n={adapterName:e.adapterName,errorRegistry:t,queryRaw:aE(t,e.queryRaw.bind(e)),executeRaw:aE(t,e.executeRaw.bind(e)),executeScript:aE(t,e.executeScript.bind(e)),dispose:aE(t,e.dispose.bind(e)),provider:e.provider,startTransaction:async(...r)=>(await aE(t,e.startTransaction.bind(e))(...r)).map(e=>a_(t,e))};return e.getConnectionInfo&&(r=t,i=e.getConnectionInfo.bind(e),n.getConnectionInfo=(...e)=>{try{return ag(i(...e))}catch(e){if(ab("[error@wrapSync]",e),am(e))return ay(e.cause);return ay({kind:"GenericJs",id:r.registerNewError(e)})}}),n},a_=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:aE(e,t.queryRaw.bind(t)),executeRaw:aE(e,t.executeRaw.bind(t)),commit:aE(e,t.commit.bind(t)),rollback:aE(e,t.rollback.bind(t))});function aE(e,t){return async(...r)=>{try{return ag(await t(...r))}catch(t){if(ab("[error@wrapAsync]",t),am(t))return ay(t.cause);return ay({kind:"GenericJs",id:e.registerNewError(t)})}}}function ax({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:i}){let n,s=Object.keys(e)[0],a=e[s]?.url,o=t[s]?.url;if(void 0===s?n=void 0:o?n=o:a?.value?n=a.value:a?.fromEnvVar&&(n=r[a.fromEnvVar]),a?.fromEnvVar!==void 0&&void 0===n)throw new rm(`error: Environment variable not found: ${a.fromEnvVar}.`,i);if(void 0===n)throw new rm("error: Missing URL environment variable, value, or override.",i);return n}var aP=class extends Error{clientVersion;cause;constructor(e,t){super(e),this.clientVersion=t.clientVersion,this.cause=t.cause}get[Symbol.toStringTag](){return this.name}},aT=class extends aP{isRetryable;constructor(e,t){super(e,t),this.isRetryable=t.isRetryable??!0}};function aS(e,t){return{...e,isRetryable:t}}var aR=class extends aT{name="ForcedRetryError";code="P5001";constructor(e){super("This request must be retried",aS(e,!0))}};rf(aR,"ForcedRetryError");var ak=class extends aT{name="InvalidDatasourceError";code="P6001";constructor(e,t){super(e,aS(t,!1))}};rf(ak,"InvalidDatasourceError");var aO=class extends aT{name="NotImplementedYetError";code="P5004";constructor(e,t){super(e,aS(t,!1))}};rf(aO,"NotImplementedYetError");var aA=class extends aT{response;constructor(e,t){super(e,t),this.response=t.response;let r=this.response.headers.get("prisma-request-id");if(r){let e=`(The request id was: ${r})`;this.message=this.message+" "+e}}},aN=class extends aA{name="SchemaMissingError";code="P5005";constructor(e){super("Schema needs to be uploaded",aS(e,!0))}};rf(aN,"SchemaMissingError");var a$="This request could not be understood by the server",aq=class extends aA{name="BadRequestError";code="P5000";constructor(e,t,r){super(t||a$,aS(e,!1)),r&&(this.code=r)}};rf(aq,"BadRequestError");var aD=class extends aA{name="HealthcheckTimeoutError";code="P5013";logs;constructor(e,t){super("Engine not started: healthcheck timeout",aS(e,!0)),this.logs=t}};rf(aD,"HealthcheckTimeoutError");var aI=class extends aA{name="EngineStartupError";code="P5014";logs;constructor(e,t,r){super(t,aS(e,!0)),this.logs=r}};rf(aI,"EngineStartupError");var aV=class extends aA{name="EngineVersionNotSupportedError";code="P5012";constructor(e){super("Engine version is not supported",aS(e,!1))}};rf(aV,"EngineVersionNotSupportedError");var aU="Request timed out",aF=class extends aA{name="GatewayTimeoutError";code="P5009";constructor(e,t=aU){super(t,aS(e,!1))}};rf(aF,"GatewayTimeoutError");var aC=class extends aA{name="InteractiveTransactionError";code="P5015";constructor(e,t="Interactive transaction error"){super(t,aS(e,!1))}};rf(aC,"InteractiveTransactionError");var aj=class extends aA{name="InvalidRequestError";code="P5011";constructor(e,t="Request parameters are invalid"){super(t,aS(e,!1))}};rf(aj,"InvalidRequestError");var aL="Requested resource does not exist",aM=class extends aA{name="NotFoundError";code="P5003";constructor(e,t=aL){super(t,aS(e,!1))}};rf(aM,"NotFoundError");var aG="Unknown server error",aB=class extends aA{name="ServerError";code="P5006";logs;constructor(e,t,r){super(t||aG,aS(e,!0)),this.logs=r}};rf(aB,"ServerError");var aH="Unauthorized, check your connection string",aW=class extends aA{name="UnauthorizedError";code="P5007";constructor(e,t=aH){super(t,aS(e,!1))}};rf(aW,"UnauthorizedError");var aJ="Usage exceeded, retry again later",aK=class extends aA{name="UsageExceededError";code="P5008";constructor(e,t=aJ){super(t,aS(e,!0))}};async function aQ(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let e=JSON.parse(t);if("string"==typeof e)if("InternalDataProxyError"===e)return{type:"DataProxyError",body:e};else return{type:"UnknownTextError",body:e};if("object"==typeof e&&null!==e){if("is_panic"in e&&"message"in e&&"error_code"in e)return{type:"QueryEngineError",body:e};if("EngineNotStarted"in e||"InteractiveTransactionMisrouted"in e||"InvalidRequestError"in e){let t=Object.values(e)[0].reason;return"string"!=typeof t||["SchemaMissing","EngineVersionNotSupported"].includes(t)?{type:"DataProxyError",body:e}:{type:"UnknownJsonError",body:e}}}return{type:"UnknownJsonError",body:e}}catch{return""===t?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function az(e,t){if(e.ok)return;let r={clientVersion:t,response:e},i=await aQ(e);if("QueryEngineError"===i.type)throw new rg(i.body.message,{code:i.body.error_code,clientVersion:t});if("DataProxyError"===i.type){if("InternalDataProxyError"===i.body)throw new aB(r,"Internal Data Proxy error");if("EngineNotStarted"in i.body){if("SchemaMissing"===i.body.EngineNotStarted.reason)return new aN(r);if("EngineVersionNotSupported"===i.body.EngineNotStarted.reason)throw new aV(r);if("EngineStartupError"in i.body.EngineNotStarted.reason){let{msg:e,logs:t}=i.body.EngineNotStarted.reason.EngineStartupError;throw new aI(r,e,t)}if("KnownEngineStartupError"in i.body.EngineNotStarted.reason){let{msg:e,error_code:r}=i.body.EngineNotStarted.reason.KnownEngineStartupError;throw new rm(e,t,r)}if("HealthcheckTimeout"in i.body.EngineNotStarted.reason){let{logs:e}=i.body.EngineNotStarted.reason.HealthcheckTimeout;throw new aD(r,e)}}if("InteractiveTransactionMisrouted"in i.body)throw new aC(r,{IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"}[i.body.InteractiveTransactionMisrouted.reason]);if("InvalidRequestError"in i.body)throw new aj(r,i.body.InvalidRequestError.reason)}if(401===e.status||403===e.status)throw new aW(r,aY(aH,i));if(404===e.status)return new aM(r,aY(aL,i));if(429===e.status)throw new aK(r,aY(aJ,i));if(504===e.status)throw new aF(r,aY(aU,i));if(e.status>=500)throw new aB(r,aY(aG,i));if(e.status>=400)throw new aq(r,aY(a$,i))}function aY(e,t){return"EmptyError"===t.type?e:`${e}: ${JSON.stringify(t)}`}rf(aK,"UsageExceededError");var aZ="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function aX(e){return new Date(1e3*e[0]+e[1]/1e6)}var a0={"@prisma/engines-version":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e"},a1=class extends aT{name="RequestError";code="P5010";constructor(e,t){super(`Cannot fetch data from service:
${e}`,aS(t,!0))}};async function a2(e,t,r=e=>e){let{clientVersion:i,...n}=t,s=r(fetch);try{return await s(e,n)}catch(e){throw new a1(e.message??"Unknown error",{clientVersion:i,cause:e})}}rf(a1,"RequestError");var a3=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,a4=eE("prisma:client:dataproxyEngine");async function a6(e,t){let r=a0["@prisma/engines-version"],i=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&"0.0.0"!==i&&"in-memory"!==i)return i;let[n,s]=i?.split("-")??[];if(void 0===s&&a3.test(n))return n;if(void 0!==s||"0.0.0"===i||"in-memory"===i){var a;let e,[t]=r.split("-")??[],[n,s,o]=t.split("."),l=(a=`<=${n}.${s}.${o}`,encodeURI(`https://unpkg.com/prisma@${a}/package.json`)),u=await a2(l,{clientVersion:i});if(!u.ok)throw Error(`Failed to fetch stable Prisma version, unpkg.com status ${u.status} ${u.statusText}, response body: ${await u.text()||"<empty body>"}`);let d=await u.text();a4("length of body fetched from unpkg.com",d.length);try{e=JSON.parse(d)}catch(e){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),e}return e.version}throw new aO("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:i})}async function a9(e,t){let r=await a6(e,t);return a4("version",r),r}var a7,a5=eE("prisma:client:dataproxyEngine"),a8=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:e,tracingHelper:t,logLevel:r,logQueries:i,engineHash:n}){this.apiKey=e,this.tracingHelper=t,this.logLevel=r,this.logQueries=i,this.engineHash=n}build({traceparent:e,interactiveTransaction:t}={}){let r={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(r.traceparent=e??this.tracingHelper.getTraceParent()),t&&(r["X-transaction-id"]=t.id);let i=this.buildCaptureSettings();return i.length>0&&(r["X-capture-telemetry"]=i.join(", ")),r}buildCaptureSettings(){let e=[];return this.tracingHelper.isEnabled()&&e.push("tracing"),this.logLevel&&e.push(this.logLevel),this.logQueries&&e.push("query"),e}},oe=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(e){(function(e){if(e.generator?.previewFeatures.some(e=>e.toLowerCase().includes("metrics")))throw new rm("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)})(e),this.config=e,this.env={...e.env,..."u">typeof process?process.env:{}},this.inlineSchema=function(e){let t=new TextEncoder().encode(e),r="",i=t.byteLength,n=i%3,s=i-n,a,o,l,u,d;for(let e=0;e<s;e+=3)a=(0xfc0000&(d=t[e]<<16|t[e+1]<<8|t[e+2]))>>18,o=(258048&d)>>12,l=(4032&d)>>6,u=63&d,r+=aZ[a]+aZ[o]+aZ[l]+aZ[u];return 1==n?(a=(252&(d=t[s]))>>2,o=(3&d)<<4,r+=aZ[a]+aZ[o]+"=="):2==n&&(a=(64512&(d=t[s]<<8|t[s+1]))>>10,o=(1008&d)>>4,l=(15&d)<<2,r+=aZ[a]+aZ[o]+aZ[l]+"="),r}(e.inlineSchema),this.inlineDatasources=e.inlineDatasources,this.inlineSchemaHash=e.inlineSchemaHash,this.clientVersion=e.clientVersion,this.engineHash=e.engineVersion,this.logEmitter=e.logEmitter,this.tracingHelper=e.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){void 0!==this.startPromise&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:e,url:t}=this.getURLAndAPIKey();this.host=t.host,this.headerBuilder=new a8({apiKey:e,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=!function(e){if(!t0(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}(t)?"https":"http",this.remoteClientVersion=await a9(this.host,this.config),a5("host",this.host),a5("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(e){e?.logs?.length&&e.logs.forEach(e=>{switch(e.level){case"debug":case"trace":a5(e);break;case"error":case"warn":case"info":this.logEmitter.emit(e.level,{timestamp:aX(e.timestamp),message:e.attributes.message??"",target:e.target});break;case"query":this.logEmitter.emit("query",{query:e.attributes.query??"",timestamp:aX(e.timestamp),duration:e.attributes.duration_ms??0,params:e.attributes.params??"",target:e.target});break;default:e.level}}),e?.traces?.length&&this.tracingHelper.dispatchEngineSpans(e.traces)}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the remote query engine')}async url(e){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${e}`}async uploadSchema(){return this.tracingHelper.runInChildSpan({name:"schemaUpload",internal:!0},async()=>{let e=await a2(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});e.ok||a5("schema response status",e.status);let t=await az(e,this.clientVersion);if(t)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${t.message}`,timestamp:new Date,target:""}),t;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(e,{traceparent:t,interactiveTransaction:r,customDataProxyFetch:i}){return this.requestInternal({body:e,traceparent:t,interactiveTransaction:r,customDataProxyFetch:i})}async requestBatch(e,{traceparent:t,transaction:r,customDataProxyFetch:i}){let n=r?.kind==="itx"?r.options:void 0,s=sE(e,r);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:n,traceparent:t})).map(e=>(e.extensions&&this.propagateResponseExtensions(e.extensions),"errors"in e?this.convertProtocolErrorsToClientError(e.errors):e))}requestInternal({body:e,traceparent:t,customDataProxyFetch:r,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:n})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");n(s);let a=await a2(s,{method:"POST",headers:this.headerBuilder.build({traceparent:t,interactiveTransaction:i}),body:JSON.stringify(e),clientVersion:this.clientVersion},r);a.ok||a5("graphql response status",a.status),await this.handleError(await az(a,this.clientVersion));let o=await a.json();if(o.extensions&&this.propagateResponseExtensions(o.extensions),"errors"in o)throw this.convertProtocolErrorsToClientError(o.errors);return"batchResult"in o?o.batchResult:o}})}async transaction(e,t,r){return this.withRetry({actionGerund:`${{start:"starting",commit:"committing",rollback:"rolling back"}[e]} transaction`,callback:async({logHttpCall:i})=>{if("start"===e){let e=JSON.stringify({max_wait:r.maxWait,timeout:r.timeout,isolation_level:r.isolationLevel}),n=await this.url("transaction/start");i(n);let s=await a2(n,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),body:e,clientVersion:this.clientVersion});await this.handleError(await az(s,this.clientVersion));let a=await s.json(),{extensions:o}=a;return o&&this.propagateResponseExtensions(o),{id:a.id,payload:{endpoint:a["data-proxy"].endpoint}}}{let n=`${r.payload.endpoint}/${e}`;i(n);let s=await a2(n,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),clientVersion:this.clientVersion});await this.handleError(await az(s,this.clientVersion));let{extensions:a}=await s.json();a&&this.propagateResponseExtensions(a);return}}})}getURLAndAPIKey(){let e={clientVersion:this.clientVersion},t=Object.keys(this.inlineDatasources)[0],r=ax({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(r)}catch{throw new ak(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,e)}let{protocol:n,searchParams:s}=i;if("prisma:"!==n&&n!==tX)throw new ak(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,e);let a=s.get("api_key");if(null===a||a.length<1)throw new ak(`Error validating datasource \`${t}\`: the URL must contain a valid API key`,e);return{apiKey:a,url:i}}metrics(){throw new aO("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(e){for(let t=0;;t++){let r=e=>{this.logEmitter.emit("info",{message:`Calling ${e} (n=${t})`,timestamp:new Date,target:""})};try{return await e.callback({logHttpCall:r})}catch(i){if(!(i instanceof aT)||!i.isRetryable)throw i;if(t>=3)throw i instanceof aR?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${t+1}/3 failed for ${e.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let r=await function(e){let t=50*Math.pow(2,e),r=Math.ceil(Math.random()*t)-Math.ceil(t/2),i=t+r;return new Promise(e=>setTimeout(()=>e(i),i))}(t);this.logEmitter.emit("warn",{message:`Retrying after ${r}ms`,timestamp:new Date,target:""})}}}async handleError(e){if(e instanceof aN)throw await this.uploadSchema(),new aR({clientVersion:this.clientVersion,cause:e});if(e)throw e}convertProtocolErrorsToClientError(e){return 1===e.length?sx(e[0],this.config.clientVersion,this.config.activeProvider):new rb(JSON.stringify(e),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw Error("Method not implemented.")}},ot=p(r(48161)),or=p(r(76760)),oi=Symbol("PrismaLibraryEngineCache"),on={async loadLibrary(e){let t=await tt(),r=await ad("library",e);try{return e.tracingHelper.runInChildSpan({name:"loadLibrary",internal:!0},()=>(function(e){let t,r=(void 0===(t=globalThis)[oi]&&(t[oi]={}),t[oi]);if(void 0!==r[e])return r[e];let i=or.default.toNamespacedPath(e),n={exports:{}},s=0;return"win32"!==process.platform&&(s=ot.default.constants.dlopen.RTLD_LAZY|ot.default.constants.dlopen.RTLD_DEEPBIND),process.dlopen(n,i,s),r[e]=n.exports,n.exports})(r))}catch(c){var i,n;let s,a,o,l,u,d;throw new rm((s=(i={e:c,platformInfo:t,id:r}).e,a=e=>`Prisma cannot find the required \`${e}\` system library in your system`,o=s.message.includes("cannot open shared object file"),l=`Please refer to the documentation about Prisma's system requirements: ${tJ(n="https://pris.ly/d/system-requirements",n,{fallback:K})}`,u=`Unable to require(\`${W(i.id)}\`).`,d=eK({message:s.message,code:s.code}).with({code:"ENOENT"},()=>"File does not exist.").when(({message:e})=>o&&e.includes("libz"),()=>`${a("libz")}. Please install it and try again.`).when(({message:e})=>o&&e.includes("libgcc_s"),()=>`${a("libgcc_s")}. Please install it and try again.`).when(({message:e})=>o&&e.includes("libssl"),()=>{let e=i.platformInfo.libssl?`openssl-${i.platformInfo.libssl}`:"openssl";return`${a("libssl")}. Please install ${e} and try again.`}).when(({message:e})=>e.includes("GLIBC"),()=>`Prisma has detected an incompatible version of the \`glibc\` C standard library installed in your system. This probably means your system may be too old to run Prisma. ${l}`).when(({message:e})=>"linux"===i.platformInfo.platform&&e.includes("symbol not found"),()=>`The Prisma engines are not compatible with your system ${i.platformInfo.originalDistro} on (${i.platformInfo.archFromUname}) which uses the \`${i.platformInfo.binaryTarget}\` binaryTarget by default. ${l}`).otherwise(()=>`The Prisma engines do not seem to be compatible with your system. ${l}`),`${u}
${d}

Details: ${s.message}`),e.clientVersion)}}},os={async loadLibrary(e){let{clientVersion:t,adapter:r,engineWasm:i}=e;if(void 0===r)throw new rm(`The \`adapter\` option for \`PrismaClient\` is required in this context (${ae().prettyName})`,t);if(void 0===i)throw new rm("WASM engine was unexpectedly `undefined`",t);return void 0===a7&&(a7=(async()=>{let e=await i.getRuntime(),r=await i.getQueryEngineWasmModule();if(null==r)throw new rm("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let n=new WebAssembly.Instance(r,{"./query_engine_bg.js":e}),s=n.exports.__wbindgen_start;return e.__wbg_set_wasm(n.exports),s(),e.QueryEngine})()),{debugPanic:()=>Promise.reject("{}"),dmmf:()=>Promise.resolve("{}"),version:()=>({commit:"unknown",version:"unknown"}),QueryEngine:await a7}}},oa=eE("prisma:client:libraryEngine"),oo=["darwin","darwin-arm64","debian-openssl-1.0.x","debian-openssl-1.1.x","debian-openssl-3.0.x","rhel-openssl-1.0.x","rhel-openssl-1.1.x","rhel-openssl-3.0.x","linux-arm64-openssl-1.1.x","linux-arm64-openssl-1.0.x","linux-arm64-openssl-3.0.x","linux-arm-openssl-1.1.x","linux-arm-openssl-1.0.x","linux-arm-openssl-3.0.x","linux-musl","linux-musl-openssl-3.0.x","linux-musl-arm64-openssl-1.1.x","linux-musl-arm64-openssl-3.0.x","linux-nixos","linux-static-x64","linux-static-arm64","windows","freebsd11","freebsd12","freebsd13","freebsd14","freebsd15","openbsd","netbsd","arm","native"],ol=1n,ou=class{name="LibraryEngine";engine;libraryInstantiationPromise;libraryStartingPromise;libraryStoppingPromise;libraryStarted;executingQueryPromise;config;QueryEngineConstructor;libraryLoader;library;logEmitter;libQueryEnginePath;binaryTarget;datasourceOverrides;datamodel;logQueries;logLevel;lastQuery;loggerRustPanic;tracingHelper;adapterPromise;versionInfo;constructor(e,t){this.libraryLoader=t??on,void 0!==e.engineWasm&&(this.libraryLoader=t??os),this.config=e,this.libraryStarted=!1,this.logQueries=e.logQueries??!1,this.logLevel=e.logLevel??"error",this.logEmitter=e.logEmitter,this.datamodel=e.inlineSchema,this.tracingHelper=e.tracingHelper,e.enableDebugLogs&&(this.logLevel="debug");let r=Object.keys(e.overrideDatasources)[0],i=e.overrideDatasources[r]?.url;void 0!==r&&void 0!==i&&(this.datasourceOverrides={[r]:i}),this.libraryInstantiationPromise=this.instantiateLibrary()}wrapEngine(e){return{applyPendingMigrations:e.applyPendingMigrations?.bind(e),commitTransaction:this.withRequestId(e.commitTransaction.bind(e)),connect:this.withRequestId(e.connect.bind(e)),disconnect:this.withRequestId(e.disconnect.bind(e)),metrics:e.metrics?.bind(e),query:this.withRequestId(e.query.bind(e)),rollbackTransaction:this.withRequestId(e.rollbackTransaction.bind(e)),sdlSchema:e.sdlSchema?.bind(e),startTransaction:this.withRequestId(e.startTransaction.bind(e)),trace:e.trace.bind(e)}}withRequestId(e){return async(...t)=>{let r,i=(r=ol++,ol>0xffffffffffffffffn&&(ol=1n),r).toString();try{return await e(...t,i)}finally{if(this.tracingHelper.isEnabled()){let e=await this.engine?.trace(i);if(e){let t=JSON.parse(e);this.tracingHelper.dispatchEngineSpans(t.spans)}}}}}async applyPendingMigrations(){throw Error("Cannot call this method from this type of engine instance")}async transaction(e,t,r){var i;await this.start();let n=await this.adapterPromise,s=JSON.stringify(t),a;if("start"===e){let e=JSON.stringify({max_wait:r.maxWait,timeout:r.timeout,isolation_level:r.isolationLevel});a=await this.engine?.startTransaction(e,s)}else"commit"===e?a=await this.engine?.commitTransaction(r.id,s):"rollback"===e&&(a=await this.engine?.rollbackTransaction(r.id,s));let o=this.parseEngineResponse(a);if("object"==typeof(i=o)&&null!==i&&void 0!==i.error_code){let e=this.getExternalAdapterError(o,n?.errorRegistry);throw e?e.error:new rg(o.message,{code:o.error_code,clientVersion:this.config.clientVersion,meta:o.meta})}if("string"==typeof o.message)throw new rb(o.message,{clientVersion:this.config.clientVersion});return o}async instantiateLibrary(){if(oa("internalSetup"),this.libraryInstantiationPromise)return this.libraryInstantiationPromise;(function(){let e=process.env.PRISMA_QUERY_ENGINE_LIBRARY;if(!(e&&ex.default.existsSync(e))&&"ia32"===process.arch)throw Error('The default query engine type (Node-API, "library") is currently not supported for 32bit Node. Please set `engineType = "binary"` in the "generator" block of your "schema.prisma" file (or use the environment variables "PRISMA_CLIENT_ENGINE_TYPE=binary" and/or "PRISMA_CLI_QUERY_ENGINE_TYPE=binary".)')})(),this.binaryTarget=await this.getCurrentBinaryTarget(),await this.tracingHelper.runInChildSpan("load_engine",()=>this.loadEngine()),this.version()}async getCurrentBinaryTarget(){{if(this.binaryTarget)return this.binaryTarget;let e=await this.tracingHelper.runInChildSpan("detect_platform",()=>te());if(!oo.includes(e))throw new rm(`Unknown ${X("PRISMA_QUERY_ENGINE_LIBRARY")} ${X(H(e))}. Possible binaryTargets: ${ee(oo.join(", "))} or a path to the query engine library.
You may have to run ${ee("prisma generate")} for your changes to take effect.`,this.config.clientVersion);return e}}parseEngineResponse(e){if(!e)throw new rb("Response from the Engine was empty",{clientVersion:this.config.clientVersion});try{return JSON.parse(e)}catch{throw new rb("Unable to JSON.parse response from engine",{clientVersion:this.config.clientVersion})}}async loadEngine(){if(!this.engine){this.QueryEngineConstructor||(this.library=await this.libraryLoader.loadLibrary(this.config),this.QueryEngineConstructor=this.library.QueryEngine);try{let e=new WeakRef(this);this.adapterPromise||(this.adapterPromise=this.config.adapter?.connect()?.then(aw));let t=await this.adapterPromise;t&&oa("Using driver adapter: %O",t),this.engine=this.wrapEngine(new this.QueryEngineConstructor({datamodel:this.datamodel,env:process.env,logQueries:this.config.logQueries??!1,ignoreEnvVarErrors:!0,datasourceOverrides:this.datasourceOverrides??{},logLevel:this.logLevel,configDir:this.config.cwd,engineProtocol:"json",enableTracing:this.tracingHelper.isEnabled()},t=>{e.deref()?.logger(t)},t))}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new rm(e.message,this.config.clientVersion,e.error_code)}}}logger(e){let t=this.parseEngineResponse(e);t&&(t.level=t?.level.toLowerCase()??"unknown","query"===t.item_type&&"query"in t?this.logEmitter.emit("query",{timestamp:new Date,query:t.query,params:t.params,duration:Number(t.duration_ms),target:t.module_path}):"level"in t&&"error"===t.level&&"PANIC"===t.message?this.loggerRustPanic=new ry(od(this,`${t.message}: ${t.reason} in ${t.file}:${t.line}:${t.column}`),this.config.clientVersion):this.logEmitter.emit(t.level,{timestamp:new Date,message:t.message,target:t.module_path}))}parseInitError(e){try{return JSON.parse(e)}catch{}return e}parseRequestError(e){try{return JSON.parse(e)}catch{}return e}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){if(await this.libraryInstantiationPromise,await this.libraryStoppingPromise,this.libraryStartingPromise)return oa(`library already starting, this.libraryStarted: ${this.libraryStarted}`),this.libraryStartingPromise;if(this.libraryStarted)return;let e=async()=>{oa("library starting");try{let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.connect(JSON.stringify(e)),this.libraryStarted=!0,oa("library started")}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new rm(e.message,this.config.clientVersion,e.error_code)}finally{this.libraryStartingPromise=void 0}};return this.libraryStartingPromise=this.tracingHelper.runInChildSpan("connect",e),this.libraryStartingPromise}async stop(){if(await this.libraryInstantiationPromise,await this.libraryStartingPromise,await this.executingQueryPromise,this.libraryStoppingPromise)return oa("library is already stopping"),this.libraryStoppingPromise;if(!this.libraryStarted)return;let e=async()=>{await new Promise(e=>setTimeout(e,5)),oa("library stopping");let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.disconnect(JSON.stringify(e)),this.libraryStarted=!1,this.libraryStoppingPromise=void 0,await (await this.adapterPromise)?.dispose(),this.adapterPromise=void 0,oa("library stopped")};return this.libraryStoppingPromise=this.tracingHelper.runInChildSpan("disconnect",e),this.libraryStoppingPromise}version(){return this.versionInfo=this.library?.version(),this.versionInfo?.version??"unknown"}debugPanic(e){return this.library?.debugPanic(e)}async request(e,{traceparent:t,interactiveTransaction:r}){oa(`sending request, this.libraryStarted: ${this.libraryStarted}`);let i=JSON.stringify({traceparent:t}),n=JSON.stringify(e);try{await this.start();let e=await this.adapterPromise;this.executingQueryPromise=this.engine?.query(n,i,r?.id),this.lastQuery=n;let t=this.parseEngineResponse(await this.executingQueryPromise);if(t.errors)throw 1===t.errors.length?this.buildQueryError(t.errors[0],e?.errorRegistry):new rb(JSON.stringify(t.errors),{clientVersion:this.config.clientVersion});if(this.loggerRustPanic)throw this.loggerRustPanic;return{data:t}}catch(t){if(t instanceof rm)throw t;if("GenericFailure"===t.code&&t.message?.startsWith("PANIC:"))throw new ry(od(this,t.message),this.config.clientVersion);let e=this.parseRequestError(t.message);throw"string"==typeof e?t:new rb(`${e.message}
${e.backtrace}`,{clientVersion:this.config.clientVersion})}}async requestBatch(e,{transaction:t,traceparent:r}){oa("requestBatch");let i=sE(e,t);await this.start();let n=await this.adapterPromise;this.lastQuery=JSON.stringify(i),this.executingQueryPromise=this.engine.query(this.lastQuery,JSON.stringify({traceparent:r}),function(e){if(e?.kind==="itx")return e.options.id}(t));let s=await this.executingQueryPromise,a=this.parseEngineResponse(s);if(a.errors)throw 1===a.errors.length?this.buildQueryError(a.errors[0],n?.errorRegistry):new rb(JSON.stringify(a.errors),{clientVersion:this.config.clientVersion});let{batchResult:o,errors:l}=a;if(Array.isArray(o))return o.map(e=>e.errors&&e.errors.length>0?this.loggerRustPanic??this.buildQueryError(e.errors[0],n?.errorRegistry):{data:e});throw l&&1===l.length?Error(l[0].error):Error(JSON.stringify(a))}buildQueryError(e,t){if(e.user_facing_error.is_panic)return new ry(od(this,e.user_facing_error.message),this.config.clientVersion);let r=this.getExternalAdapterError(e.user_facing_error,t);return r?r.error:sx(e,this.config.clientVersion,this.config.activeProvider)}getExternalAdapterError(e,t){if("P2036"===e.error_code&&t){let r=e.meta?.id;rt("number"==typeof r,"Malformed external JS error received from the engine");let i=t.consumeError(r);return rt(i,"External error with reported id was not registered"),i}}async metrics(e){await this.start();let t=await this.engine.metrics(JSON.stringify(e));return"prometheus"===e.format?t:this.parseEngineResponse(t)}};function od(e,t){return function({version:e,binaryTarget:t,title:r,description:i,engineVersion:n,database:s,query:a}){var o;let l=function(e=7500){let t=ey.map(([e,...t])=>`${e} ${t.map(e=>"string"==typeof e?e:JSON.stringify(e)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}(6e3-(a?.length??0)),u=(0,ap.default)(l).split(`
`).map(e=>e.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`),d=i?`# Description
\`\`\`
${i}
\`\`\``:"",c=function({title:e,user:t="prisma",repo:r="prisma",template:i="bug_report.yml",body:n}){return(0,ah.default)({user:t,repo:r,template:i,title:e,body:n})}({title:r,body:(0,ap.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${n?.padEnd(19)}|
| Database        | ${s?.padEnd(19)}|

${d}

## Logs
\`\`\`
${u}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${a&&(o=a)?o.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,e=>`${e[0]}5`):""}
\`\`\`
`)});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${K(c)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}({binaryTarget:e.binaryTarget,title:t,version:e.config.clientVersion,engineVersion:e.versionInfo?.commit,database:e.config.activeProvider,query:e.lastQuery})}function oc({generator:e}){return e?.previewFeatures??[]}var of=e=>({command:e}),op=e=>e.strings.reduce((e,t,r)=>`${e}@P${r}${t}`);function oh(e){try{return om(e,"fast")}catch{return om(e,"slow")}}function om(e,t){return JSON.stringify(e.map(e=>(function e(t,r){var i;if(Array.isArray(t))return t.map(t=>e(t,r));if("bigint"==typeof t)return{prisma__type:"bigint",prisma__value:t.toString()};if(iY(t))return{prisma__type:"date",prisma__value:t.toJSON()};if(iB.isDecimal(t))return{prisma__type:"decimal",prisma__value:t.toJSON()};if(Buffer.isBuffer(t))return{prisma__type:"bytes",prisma__value:t.toString("base64")};if((i=t)instanceof ArrayBuffer||i instanceof SharedArrayBuffer||"object"==typeof i&&null!==i&&("ArrayBuffer"===i[Symbol.toStringTag]||"SharedArrayBuffer"===i[Symbol.toStringTag]))return{prisma__type:"bytes",prisma__value:Buffer.from(t).toString("base64")};if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:r,byteLength:i}=t;return{prisma__type:"bytes",prisma__value:Buffer.from(e,r,i).toString("base64")}}return"object"==typeof t&&"slow"===r?og(t):t})(e,t)))}function og(e){if("object"!=typeof e||null===e)return e;if("function"==typeof e.toJSON)return e.toJSON();if(Array.isArray(e))return e.map(oy);let t={};for(let r of Object.keys(e))t[r]=oy(e[r]);return t}function oy(e){return"bigint"==typeof e?e.toString():og(e)}var ob=/^(\s*alter\s)/i,ov=eE("prisma:client");function ow(e,t,r,i){if(("postgresql"===e||"cockroachdb"===e)&&r.length>0&&ob.exec(t))throw Error(`Running ALTER using ${i} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var o_=({clientMethod:e,activeProvider:t})=>r=>{let i="",n;if(sr(r))i=r.sql,n={values:oh(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[e,...t]=r;i=e,n={values:oh(t||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":i=r.sql,n={values:oh(r.values),__prismaRawParameters__:!0};break;case"cockroachdb":case"postgresql":case"postgres":i=r.text,n={values:oh(r.values),__prismaRawParameters__:!0};break;case"sqlserver":i=op(r),n={values:oh(r.values),__prismaRawParameters__:!0};break;default:throw Error(`The ${t} provider does not support ${e}`)}return n?.values?ov(`prisma.${e}(${i}, ${n.values})`):ov(`prisma.${e}(${i})`),{query:i,parameters:n}},oE={requestArgsToMiddlewareArgs:e=>[e.strings,...e.values],middlewareArgsToRequestArgs(e){let[t,...r]=e;return new sl(t,r)}},ox={requestArgsToMiddlewareArgs:e=>[e],middlewareArgsToRequestArgs:e=>e[0]};function oP(e){return function(t,r){let i,n=(r=e)=>{try{return void 0===r||r?.kind==="itx"?i??=oT(t(r)):oT(t(r))}catch(e){return Promise.reject(e)}};return{get spec(){return r},then:(e,t)=>n().then(e,t),catch:e=>n().catch(e),finally:e=>n().finally(e),requestTransaction(e){let t=n(e);return t.requestTransaction?t.requestTransaction(e):t},[Symbol.toStringTag]:"PrismaPromise"}}}function oT(e){return"function"==typeof e.then?e:Promise.resolve(e)}var oS=tK.split(".")[0],oR={isEnabled:()=>!1,getTraceParent:()=>"00-10-10-00",dispatchEngineSpans(){},getActiveContext(){},runInChildSpan:(e,t)=>t()},ok=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(e){return this.getGlobalTracingHelper().getTraceParent(e)}dispatchEngineSpans(e){return this.getGlobalTracingHelper().dispatchEngineSpans(e)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(e,t){return this.getGlobalTracingHelper().runInChildSpan(e,t)}getGlobalTracingHelper(){let e=globalThis[`V${oS}_PRISMA_INSTRUMENTATION`],t=globalThis.PRISMA_INSTRUMENTATION;return e?.helper??t?.helper??oR}},oO=class{_middlewares=[];use(e){this._middlewares.push(e)}get(e){return this._middlewares[e]}has(e){return!!this._middlewares[e]}length(){return this._middlewares.length}},oA=p(x());function oN(e){return"number"==typeof e.batchRequestIdx}function o$(e){return`(${Object.keys(e).sort().map(t=>{let r=e[t];return"object"==typeof r&&null!==r?`(${t} ${o$(r)})`:t}).join(" ")})`}var oq={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0},oD=class{constructor(e){this.options=e,this.batches={}}batches;tickActive=!1;request(e){let t=this.options.batchBy(e);return t?(this.batches[t]||(this.batches[t]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((r,i)=>{this.batches[t].push({request:e,resolve:r,reject:i})})):this.options.singleLoader(e)}dispatchBatches(){for(let e in this.batches){let t=this.batches[e];delete this.batches[e],1===t.length?this.options.singleLoader(t[0].request).then(e=>{e instanceof Error?t[0].reject(e):t[0].resolve(e)}).catch(e=>{t[0].reject(e)}):(t.sort((e,t)=>this.options.batchOrder(e.request,t.request)),this.options.batchLoader(t.map(e=>e.request)).then(e=>{if(e instanceof Error)for(let r=0;r<t.length;r++)t[r].reject(e);else for(let r=0;r<t.length;r++){let i=e[r];i instanceof Error?t[r].reject(i):t[r].resolve(i)}}).catch(e=>{for(let r=0;r<t.length;r++)t[r].reject(e)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function oI(e){let t=[],r=function(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}(e);for(let i=0;i<e.rows.length;i++){let n=e.rows[i],s={...r};for(let t=0;t<n.length;t++)s[e.columns[t]]=function e(t,r){if(null===r)return r;switch(t){case"bigint":return BigInt(r);case"bytes":{let{buffer:e,byteOffset:t,byteLength:i}=Buffer.from(r,"base64");return new Uint8Array(e,t,i)}case"decimal":return new iB(r);case"datetime":case"date":return new Date(r);case"time":return new Date(`1970-01-01T${r}Z`);case"bigint-array":return r.map(t=>e("bigint",t));case"bytes-array":return r.map(t=>e("bytes",t));case"decimal-array":return r.map(t=>e("decimal",t));case"datetime-array":return r.map(t=>e("datetime",t));case"date-array":return r.map(t=>e("date",t));case"time-array":return r.map(t=>e("time",t));default:return r}}(e.types[t],n[t]);t.push(s)}return t}var oV=eE("prisma:client:request_handler"),oU=class{client;dataloader;logEmitter;constructor(e,t){this.logEmitter=t,this.client=e,this.dataloader=new oD({batchLoader:function(e){return t=>{let r={requests:t},i=t[0].extensions.getAllBatchQueryCallbacks();return i.length?function e(t,r,i,n){if(i===r.length)return n(t);let s=t.customDataProxyFetch,a=t.requests[0].transaction;return r[i]({args:{queries:t.requests.map(e=>({model:e.modelName,operation:e.action,args:e.args})),transaction:a?{isolationLevel:"batch"===a.kind?a.isolationLevel:void 0}:void 0},__internalParams:t,query(a,o=t){let l=o.customDataProxyFetch;return o.customDataProxyFetch=s0(s,l),e(o,r,i+1,n)}})}(r,i,0,e):e(r)}}(async({requests:e,customDataProxyFetch:t})=>{let{transaction:r,otelParentCtx:i}=e[0],n=e.map(e=>e.protocolQuery),s=this.client._tracingHelper.getTraceParent(i),a=e.some(e=>oq[e.protocolQuery.action]);return(await this.client._engine.requestBatch(n,{traceparent:s,transaction:function(e){if(e){if("batch"===e.kind)return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if("itx"===e.kind)return{kind:"itx",options:oF(e)};rr(e,"Unknown transaction kind")}}(r),containsWrite:a,customDataProxyFetch:t})).map((t,r)=>{if(t instanceof Error)return t;try{return this.mapQueryEngineResult(e[r],t)}catch(e){return e}})}),singleLoader:async e=>{let t=e.transaction?.kind==="itx"?oF(e.transaction):void 0,r=await this.client._engine.request(e.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:t,isWrite:oq[e.protocolQuery.action],customDataProxyFetch:e.customDataProxyFetch});return this.mapQueryEngineResult(e,r)},batchBy:e=>e.transaction?.id?`transaction-${e.transaction.id}`:function(e){if("findUnique"!==e.action&&"findUniqueOrThrow"!==e.action)return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(o$(e.query.arguments)),t.push(o$(e.query.selection)),t.join("")}(e.protocolQuery),batchOrder:(e,t)=>e.transaction?.kind==="batch"&&t.transaction?.kind==="batch"?e.transaction.index-t.transaction.index:0})}async request(e){try{return await this.dataloader.request(e)}catch(a){let{clientMethod:t,callsite:r,transaction:i,args:n,modelName:s}=e;this.handleAndLogRequestError({error:a,clientMethod:t,callsite:r,transaction:i,args:n,modelName:s,globalOmit:e.globalOmit})}}mapQueryEngineResult({dataPath:e,unpacker:t},r){let i=r?.data,n=this.unpack(i,e,t);return process.env.PRISMA_CLIENT_GET_TIME?{data:n}:n}handleAndLogRequestError(e){try{this.handleRequestError(e)}catch(t){throw this.logEmitter&&this.logEmitter.emit("error",{message:t.message,target:e.clientMethod,timestamp:new Date}),t}}handleRequestError({error:e,clientMethod:t,callsite:r,transaction:i,args:n,modelName:s,globalOmit:a}){var o,l,u;if(oV(e),o=e,l=i,oN(o)&&l?.kind==="batch"&&o.batchRequestIdx!==l.index)throw e;e instanceof rg&&("P2009"===(u=e).code||"P2012"===u.code)&&nL({args:n,errors:[function e(t){if("Union"===t.kind)return{kind:"Union",errors:t.errors.map(e)};if(Array.isArray(t.selectionPath)){let[,...e]=t.selectionPath;return{...t,selectionPath:e}}return t}(e.meta)],callsite:r,errorFormat:this.client._errorFormat,originalMethod:t,clientVersion:this.client._clientVersion,globalOmit:a});let d=e.message;if(r&&(d=nt({callsite:r,originalMethod:t,isPanic:e.isPanic,showColors:"pretty"===this.client._errorFormat,message:d})),d=this.sanitizeMessage(d),e.code){let t=s?{modelName:s,...e.meta}:e.meta;throw new rg(d,{code:e.code,clientVersion:this.client._clientVersion,meta:t,batchRequestIdx:e.batchRequestIdx})}if(e.isPanic)throw new ry(d,this.client._clientVersion);if(e instanceof rb)throw new rb(d,{clientVersion:this.client._clientVersion,batchRequestIdx:e.batchRequestIdx});if(e instanceof rm)throw new rm(d,this.client._clientVersion);if(e instanceof ry)throw new ry(d,this.client._clientVersion);throw e.clientVersion=this.client._clientVersion,e}sanitizeMessage(e){return this.client._errorFormat&&"pretty"!==this.client._errorFormat?(0,oA.default)(e):e}unpack(e,t,r){if(!e||(e.data&&(e=e.data),!e))return e;let i=Object.keys(e)[0],n=sL(Object.values(e)[0],t.filter(e=>"select"!==e&&"include"!==e)),s="queryRaw"===i?oI(n):iH(n);return r?r(s):s}get[Symbol.toStringTag](){return"RequestHandler"}};function oF(e){return{id:e.id,payload:e.payload}}var oC=p(R()),oj=class extends Error{constructor(e){super(e+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};rf(oj,"PrismaClientConstructorValidationError");var oL=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],oM=["pretty","colorless","minimal"],oG=["info","query","warn","error"],oB={datasources:(e,{datasourceNames:t})=>{if(e){if("object"!=typeof e||Array.isArray(e))throw new oj(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,i]of Object.entries(e)){if(!t.includes(r)){let e=oH(r,t)||` Available datasources: ${t.join(", ")}`;throw new oj(`Unknown datasource ${r} provided to PrismaClient constructor.${e}`)}if("object"!=typeof i||Array.isArray(i))throw new oj(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(i&&"object"==typeof i)for(let[t,n]of Object.entries(i)){if("url"!==t)throw new oj(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if("string"!=typeof n)throw new oj(`Invalid value ${JSON.stringify(n)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&"client"===tQ(t.generator))throw new oj('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(null!==e){if(void 0===e)throw new oj('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!oc(t).includes("driverAdapters"))throw new oj('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if("binary"===tQ(t.generator))throw new oj('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')}},datasourceUrl:e=>{if("u">typeof e&&"string"!=typeof e)throw new oj(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if("string"!=typeof e)throw new oj(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!oM.includes(e)){let t=oH(e,oM);throw new oj(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(e){if(!Array.isArray(e))throw new oj(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);for(let r of e){t(r);let e={level:t,emit:e=>{let t=["stdout","event"];if(!t.includes(e)){let r=oH(e,t);throw new oj(`Invalid value ${JSON.stringify(e)} for "emit" in logLevel provided to PrismaClient constructor.${r}`)}}};if(r&&"object"==typeof r)for(let[t,i]of Object.entries(r))if(e[t])e[t](i);else throw new oj(`Invalid property ${t} for "log" provided to PrismaClient constructor`)}}function t(e){if("string"==typeof e&&!oG.includes(e)){let t=oH(e,oG);throw new oj(`Invalid log level "${e}" provided to PrismaClient constructor.${t}`)}}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(null!=t&&t<=0)throw new oj(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(null!=r&&r<=0)throw new oj(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if("object"!=typeof e)throw new oj('"omit" option is expected to be an object.');if(null===e)throw new oj('"omit" option can not be `null`');let r=[];for(let[i,n]of Object.entries(e)){let e=function(e,t){return oW(t.models,e)??oW(t.types,e)}(i,t.runtimeDataModel);if(!e){r.push({kind:"UnknownModel",modelKey:i});continue}for(let[t,s]of Object.entries(n)){let n=e.fields.find(e=>e.name===t);if(!n){r.push({kind:"UnknownField",modelKey:i,fieldName:t});continue}if(n.relationName){r.push({kind:"RelationInOmit",modelKey:i,fieldName:t});continue}"boolean"!=typeof s&&r.push({kind:"InvalidFieldValue",modelKey:i,fieldName:t})}}if(r.length>0)throw new oj(function(e,t){let r=nF(e);for(let e of t)switch(e.kind){case"UnknownModel":r.arguments.getField(e.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${e.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${e.modelKey}" does not have a field named "${e.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([e.modelKey,e.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.")}let{message:i,args:n}=nj(r,"colorless");return`Error validating "omit" option:

${n}

${i}`}(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if("object"!=typeof e)throw new oj(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let e=oH(r,t);throw new oj(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${e}`)}}};function oH(e,t){if(0===t.length||"string"!=typeof e)return"";let r=function(e,t){if(0===t.length)return null;let r=t.map(t=>({value:t,distance:(0,oC.default)(e,t)}));r.sort((e,t)=>e.distance<t.distance?-1:1);let i=r[0];return i.distance<3?i.value:null}(e,t);return r?` Did you mean "${r}"?`:""}function oW(e,t){let r=Object.keys(e).find(e=>iJ(e)===t);if(r)return e[r]}var oJ=eE("prisma:client");"object"==typeof globalThis&&(globalThis.NODE_CLIENT=!0);var oK={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},oQ=Symbol.for("prisma.client.transaction.id"),oz={id:0,nextId(){return++this.id}};function oY(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new oO;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=oP();constructor(t){(function({postinstall:e,ciName:t,clientVersion:r}){if(s1("checkPlatformCaching:postinstall",e),s1("checkPlatformCaching:ciName",t),!0===e&&t&&t in s2){let e=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${s2[t]}-build`;throw console.error(e),new rm(e,r)}})(e=t?.__internal?.configOverride?.(e)??e),t&&function(e,t){for(let[r,i]of Object.entries(e)){if(!oL.includes(r)){let e=oH(r,oL);throw new oj(`Unknown property ${r} provided to PrismaClient constructor.${e}`)}oB[r](i,t)}if(e.datasourceUrl&&e.datasources)throw new oj('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}(t,e);let r=new ss.EventEmitter().on("error",()=>{});this._extensions=nH.empty(),this._previewFeatures=oc(e),this._clientVersion=e.clientVersion??"6.9.0",this._activeProvider=e.activeProvider,this._globalOmit=t?.omit,this._tracingHelper=new ok;let i=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&so.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&so.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},n;if(t?.adapter){n=t.adapter;let r="postgresql"===e.activeProvider?"postgres":e.activeProvider;if(n.provider!==r)throw new rm(`The Driver Adapter \`${n.adapterName}\`, based on \`${n.provider}\`, is not compatible with the provider \`${r}\` specified in the Prisma schema.`,this._clientVersion);if(t.datasources||void 0!==t.datasourceUrl)throw new rm("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let s=!n&&i&&rl(i,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{var a,o;let i=t??{},l=i.__internal??{},u=!0===l.debug;u&&eE.enable("prisma:client");let d=so.default.resolve(e.dirname,e.relativePath);sa.default.existsSync(d)||(d=e.dirname),oJ("dirname",e.dirname),oJ("relativePath",e.relativePath),oJ("cwd",d);let c=l.engine||{};if(i.errorFormat?this._errorFormat=i.errorFormat:this._errorFormat="minimal",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:d,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:c.allowTriggerPanic,prismaPath:c.binaryPath??void 0,engineEndpoint:c.endpoint,generator:e.generator,showColors:"pretty"===this._errorFormat,logLevel:i.log&&(a=i.log,"string"==typeof a?a:a.reduce((e,t)=>{let r="string"==typeof t?t:t.level;return"query"===r?e:e&&("info"===t||"info"===e)?"info":r},void 0)),logQueries:i.log&&!!("string"==typeof i.log?"query"===i.log:i.log.find(e=>"string"==typeof e?"query"===e:"query"===e.level)),env:s?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:(o=e.datasourceNames,i?i.datasources?i.datasources:i.datasourceUrl?{[o[0]]:{url:i.datasourceUrl}}:{}:{}),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:i.transactionOptions?.maxWait??2e3,timeout:i.transactionOptions?.timeout??5e3,isolationLevel:i.transactionOptions?.isolationLevel},logEmitter:r,isBundled:e.isBundled,adapter:n},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:ax,getBatchRequestPayload:sE,prismaGraphQLToJSError:sx,PrismaClientUnknownRequestError:rb,PrismaClientInitializationError:rm,PrismaClientKnownRequestError:rg,debug:eE("prisma:client:accelerateEngine"),engineVersion:si.version,clientVersion:e.clientVersion}},oJ("clientVersion",e.clientVersion),this._engine=function({copyEngine:e=!0},t){let r;try{r=ax({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let i=!!(r?.startsWith("prisma://")||t0(r));e&&i&&rh("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"),tQ(t.generator);let n=i||!e,s=!!t.adapter;if(n&&s){let i;throw new rv((e?r?.startsWith("prisma://")?["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]).join(`
`),{clientVersion:t.clientVersion})}return n?new oe(t):new ou(t)}(e,this._engineConfig),this._requestHandler=new oU(this,r),i.log)for(let e of i.log){let t="string"==typeof e?e:"stdout"===e.emit?e.level:null;t&&this.$on(t,e=>{t3.log(`${t3.tags[t]??""}`,e.message||e.query)})}}catch(e){throw e.clientVersion=this._clientVersion,e}return this._appliedParent=sJ(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(e){this._middlewares.use(e)}$on(e,t){return"beforeExit"===e?this._engine.onBeforeExit(t):e&&this._engineConfig.logEmitter.on(e,t),this}$connect(){try{return this._engine.start()}catch(e){throw e.clientVersion=this._clientVersion,e}}async $disconnect(){try{await this._engine.stop()}catch(e){throw e.clientVersion=this._clientVersion,e}finally{ey.length=0}}$executeRawInternal(e,t,r,i){let n=this._activeProvider;return this._request({action:"executeRaw",args:r,transaction:e,clientMethod:t,argsMapper:o_({clientMethod:t,activeProvider:n}),callsite:sD(this._errorFormat),dataPath:[],middlewareArgsMapper:i})}$executeRaw(e,...t){return this._createPrismaPromise(r=>{if(void 0!==e.raw||void 0!==e.sql){let[i,n]=oZ(e,t);return ow(this._activeProvider,i.text,i.values,Array.isArray(e)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(r,"$executeRaw",i,n)}throw new rv("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(e,...t){return this._createPrismaPromise(r=>(ow(this._activeProvider,e,t,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(r,"$executeRawUnsafe",[e,...t])))}$runCommandRaw(t){if("mongodb"!==e.activeProvider)throw new rv(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(e=>this._request({args:t,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:of,callsite:sD(this._errorFormat),transaction:e}))}async $queryRawInternal(e,t,r,i){let n=this._activeProvider;return this._request({action:"queryRaw",args:r,transaction:e,clientMethod:t,argsMapper:o_({clientMethod:t,activeProvider:n}),callsite:sD(this._errorFormat),dataPath:[],middlewareArgsMapper:i})}$queryRaw(e,...t){return this._createPrismaPromise(r=>{if(void 0!==e.raw||void 0!==e.sql)return this.$queryRawInternal(r,"$queryRaw",...oZ(e,t));throw new rv("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(e){return this._createPrismaPromise(t=>{if(!this._hasPreviewFlag("typedSql"))throw new rv("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(t,"$queryRawTyped",e)})}$queryRawUnsafe(e,...t){return this._createPrismaPromise(r=>this.$queryRawInternal(r,"$queryRawUnsafe",[e,...t]))}_transactionWithArray({promises:e,options:t}){var r;let i=oz.nextId(),n=function(e,t=()=>{}){let r,i=new Promise(e=>r=e);return{then:n=>(0==--e&&r(t()),n?.(i))}}(e.length);return 0===(r=e.map((e,r)=>{if(e?.[Symbol.toStringTag]!=="PrismaPromise")throw Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let s=t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel;return e.requestTransaction?.({kind:"batch",id:i,index:r,isolationLevel:s,lock:n})??e})).length?Promise.resolve([]):new Promise((e,t)=>{let i=Array(r.length),n=null,s=!1,a=0,o=()=>{s||++a===r.length&&(s=!0,n?t(n):e(i))},l=e=>{s||(s=!0,t(e))};for(let e=0;e<r.length;e++)r[e].then(t=>{i[e]=t,o()},t=>{if(!oN(t))return void l(t);t.batchRequestIdx===e?l(t):(n||(n=t),o())})})}async _transactionWithCallback({callback:e,options:t}){let r={traceparent:this._tracingHelper.getTraceParent()},i={maxWait:t?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:t?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},n=await this._engine.transaction("start",r,i),s;try{let t={kind:"itx",...n};s=await e(this._createItxClient(t)),await this._engine.transaction("commit",r,n)}catch(e){throw await this._engine.transaction("rollback",r,n).catch(()=>{}),e}return s}_createItxClient(e){return sv(sJ(sv(this[sW]?this[sW]:this,[sh("_appliedParent",()=>this._appliedParent._createItxClient(e)),sh("_createPrismaPromise",()=>oP(e)),sh(oQ,()=>e.id)])),[s_(sY)])}$transaction(e,t){let r;return r="function"==typeof e?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?()=>{throw Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:()=>this._transactionWithCallback({callback:e,options:t}):()=>this._transactionWithArray({promises:e,options:t}),this._tracingHelper.runInChildSpan({name:"transaction",attributes:{method:"$transaction"}},r)}_request(e){e.otelParentCtx=this._tracingHelper.getActiveContext();let t=e.middlewareArgsMapper??oK,r={args:t.requestArgsToMiddlewareArgs(e.args),dataPath:e.dataPath,runInTransaction:!!e.transaction,action:e.action,model:e.model},i={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:r.action,model:r.model,name:r.model?`${r.model}.${r.action}`:r.action}}},n=-1,s=async r=>{let a=this._middlewares.get(++n);if(a)return this._tracingHelper.runInChildSpan(i.middleware,e=>a(r,t=>(e?.end(),s(t))));let{runInTransaction:o,args:l,...u}=r,d={...e,...u};l&&(d.args=t.middlewareArgsToRequestArgs(l)),void 0!==e.transaction&&!1===o&&delete d.transaction;let c=await function(e,t){let{jsModelName:r,action:i,clientMethod:n}=t;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",r?i:n);return function e(t,r,i,n=0){return t._createPrismaPromise(s=>{let a=r.customDataProxyFetch;return"transaction"in r&&void 0!==s&&(r.transaction?.kind==="batch"&&r.transaction.lock.then(),r.transaction=s),n===i.length?t._executeRequest(r):i[n]({model:r.model,operation:r.model?r.action:r.clientMethod,args:function(e){var t,r;if(e instanceof sl){return new sl((t=e).strings,t.values)}if(sr(e)){return new se((r=e).sql,r.values)}if(Array.isArray(e)){let t=[e[0]];for(let r=1;r<e.length;r++)t[r]=sZ(e[r]);return t}let i={};for(let t in e)i[t]=sZ(e[t]);return i}(r.args??{}),__internalParams:r,query:(s,o=r)=>{let l=o.customDataProxyFetch;return o.customDataProxyFetch=s0(a,l),o.args=s,e(t,o,i,n+1)}})})}(e,t,s)}(this,d);return d.model?function({result:e,modelName:t,args:r,extensions:i,runtimeDataModel:n,globalOmit:s}){return i.isEmpty()||null==e||"object"!=typeof e||!n.models[t]?e:sQ({result:e,args:r??{},modelName:t,runtimeDataModel:n,visitor:(e,t,r)=>{let n=nM(t);return function({result:e,modelName:t,select:r,omit:i,extensions:n}){let s=n.getAllComputedFields(t);if(!s)return e;let a=[],o=[];for(let t of Object.values(s)){if(i){if(i[t.name])continue;let e=t.needs.filter(e=>i[e]);e.length>0&&o.push(s_(e))}else if(r){if(!r[t.name])continue;let e=t.needs.filter(e=>!r[e]);e.length>0&&o.push(s_(e))}(function(e,t){return t.every(t=>Object.prototype.hasOwnProperty.call(e,t))})(e,t.needs)&&a.push(function(e,t){return sm(sh(e.name,()=>e.compute(t)))}(t,sv(e,a)))}return a.length>0||o.length>0?sv(e,[...a,...o]):e}({result:e,modelName:n,select:r.select,omit:r.select?void 0:{...s?.[n],...r.omit},extensions:i})}})}({result:c,modelName:d.model,args:d.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):c};return this._tracingHelper.runInChildSpan(i.operation,()=>new sn.AsyncResource("prisma-client-request").runInAsyncScope(()=>s(r)))}async _executeRequest({args:e,clientMethod:t,dataPath:r,callsite:i,action:n,model:s,argsMapper:a,transaction:o,unpacker:l,otelParentCtx:u,customDataProxyFetch:d}){try{e=a?a(e):e;let c=this._tracingHelper.runInChildSpan({name:"serialize"},()=>n0({modelName:s,runtimeDataModel:this._runtimeDataModel,action:n,args:e,clientMethod:t,callsite:i,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return eE.enabled("prisma:client")&&(oJ("Prisma Client call:"),oJ(`prisma.${t}(${function(e){if(void 0===e)return"";let t=nF(e);return new na(0,{colors:nu}).write(t).toString()}(e)})`),oJ("Generated request:"),oJ(JSON.stringify(c,null,2)+`
`)),o?.kind==="batch"&&await o.lock,this._requestHandler.request({protocolQuery:c,modelName:s,action:n,clientMethod:t,dataPath:r,callsite:i,args:e,extensions:this._extensions,transaction:o,unpacker:l,otelParentCtx:u,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:d})}catch(e){throw e.clientVersion=this._clientVersion,e}}$metrics=new n6(this);_hasPreviewFlag(e){return!!this._engineConfig.previewFeatures?.includes(e)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=sK}return t}function oZ(e,t){var r;return Array.isArray(r=e)&&Array.isArray(r.raw)?[new sl(e,t),oE]:[e,ox]}var oX=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function o0(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!oX.has(t))throw TypeError(`Invalid enum value: ${String(t)}`)}})}function o1(e){rl(e,{conflictCheck:"warn"})}},67566:(e,t,r)=>{e.exports={...r(94970)}},86647:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let{PrismaClientKnownRequestError:i,PrismaClientUnknownRequestError:n,PrismaClientRustPanicError:s,PrismaClientInitializationError:a,PrismaClientValidationError:o,getPrismaClient:l,sqltag:u,empty:d,join:c,raw:f,skip:p,Decimal:h,Debug:m,objectEnumValues:g,makeStrictEnum:y,Extensions:b,warnOnce:v,defineDmmfProperty:w,Public:_,getRuntime:E,createParam:x}=r(63667),P={};t.Prisma=P,t.$Enums={},P.prismaVersion={client:"6.9.0",engine:"81e4af48011447c3cc503a190e86995b66d2a28e"},P.PrismaClientKnownRequestError=i,P.PrismaClientUnknownRequestError=n,P.PrismaClientRustPanicError=s,P.PrismaClientInitializationError=a,P.PrismaClientValidationError=o,P.Decimal=h,P.sql=u,P.empty=d,P.join=c,P.raw=f,P.validator=_.validator,P.getExtensionContext=b.getExtensionContext,P.defineExtension=b.defineExtension,P.DbNull=g.instances.DbNull,P.JsonNull=g.instances.JsonNull,P.AnyNull=g.instances.AnyNull,P.NullTypes={DbNull:g.classes.DbNull,JsonNull:g.classes.JsonNull,AnyNull:g.classes.AnyNull};let T=r(33873);t.Prisma.TransactionIsolationLevel=y({ReadUncommitted:"ReadUncommitted",ReadCommitted:"ReadCommitted",RepeatableRead:"RepeatableRead",Serializable:"Serializable"}),t.Prisma.AccountScalarFieldEnum={id:"id",userId:"userId",type:"type",provider:"provider",providerAccountId:"providerAccountId",refresh_token:"refresh_token",access_token:"access_token",expires_at:"expires_at",token_type:"token_type",scope:"scope",id_token:"id_token",session_state:"session_state",oauth_token_secret:"oauth_token_secret",oauth_token:"oauth_token"},t.Prisma.SessionScalarFieldEnum={id:"id",sessionToken:"sessionToken",userId:"userId",expires:"expires"},t.Prisma.UserScalarFieldEnum={id:"id",name:"name",email:"email",emailVerified:"emailVerified",image:"image",created_at:"created_at",updated_at:"updated_at"},t.Prisma.VerificationTokenScalarFieldEnum={identifier:"identifier",token:"token",expires:"expires"},t.Prisma.UserProfileScalarFieldEnum={user_id:"user_id",first_name:"first_name",last_name:"last_name",settings:"settings",created_at:"created_at",updated_at:"updated_at",deleted_at:"deleted_at"},t.Prisma.TrendChangeScalarFieldEnum={id:"id",originalIndex:"originalIndex",index:"index",description:"description",trend:"trend",buyTrade:"buyTrade",sellTrade:"sellTrade",previousClose:"previousClose",date:"date"},t.Prisma.UpsideDownsidePotentialScalarFieldEnum={id:"id",originalIndex:"originalIndex",ticker:"ticker",description:"description",trend:"trend",downsidePotential:"downsidePotential",upsidePotential:"upsidePotential",date:"date",price:"price"},t.Prisma.PushSubscriptionScalarFieldEnum={id:"id",endpoint:"endpoint",auth:"auth",p256dh:"p256dh",user_id:"user_id",created_at:"created_at",updated_at:"updated_at",deleted_at:"deleted_at"},t.Prisma.WatchlistScalarFieldEnum={id:"id",user_id:"user_id",ticker:"ticker",created_at:"created_at",updated_at:"updated_at",deleted_at:"deleted_at",settings:"settings",audit_log:"audit_log"},t.Prisma.AutoOrderPrefillScalarFieldEnum={id:"id",user_id:"user_id",action:"action",ticker:"ticker",quantity:"quantity",price:"price",order_type:"order_type",account_id:"account_id",created_at:"created_at",updated_at:"updated_at",deleted_at:"deleted_at",metadata:"metadata"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.NullableJsonNullValueInput={DbNull:P.DbNull,JsonNull:P.JsonNull},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.Prisma.NullsOrder={first:"first",last:"last"},t.Prisma.JsonNullValueFilter={DbNull:P.DbNull,JsonNull:P.JsonNull,AnyNull:P.AnyNull},t.Prisma.ModelName={Account:"Account",Session:"Session",User:"User",VerificationToken:"VerificationToken",UserProfile:"UserProfile",TrendChange:"TrendChange",UpsideDownsidePotential:"UpsideDownsidePotential",PushSubscription:"PushSubscription",Watchlist:"Watchlist",AutoOrderPrefill:"AutoOrderPrefill"};let S={generator:{name:"client",provider:{fromEnvVar:null,value:"prisma-client-js"},output:{value:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/node_modules/.pnpm/@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/@prisma/client",fromEnvVar:null},config:{engineType:"library"},binaryTargets:[{fromEnvVar:null,value:"darwin-arm64",native:!0}],previewFeatures:[],sourceFilePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/packages/database/prisma/schema.prisma"},relativeEnvPaths:{rootEnvPath:null},relativePath:"../../../../../../packages/database/prisma",clientVersion:"6.9.0",engineVersion:"81e4af48011447c3cc503a190e86995b66d2a28e",datasourceNames:["db"],activeProvider:"postgresql",postinstall:!1,inlineDatasources:{db:{url:{fromEnvVar:"PG_HEDGE_PRISMA_URL",value:null}}},inlineSchema:'generator client {\n  provider = "prisma-client-js"\n}\n\ndatasource db {\n  provider  = "postgresql"\n  url       = env("PG_HEDGE_PRISMA_URL")\n  directUrl = env("PG_HEDGE_URL_NON_POOLING")\n}\n\nmodel Account {\n  id                 String  @id @default(cuid())\n  userId             String  @map("user_id")\n  type               String\n  provider           String\n  providerAccountId  String  @map("provider_account_id")\n  refresh_token      String?\n  access_token       String?\n  expires_at         Int?\n  token_type         String?\n  scope              String?\n  id_token           String?\n  session_state      String?\n  oauth_token_secret String?\n  oauth_token        String?\n  user               User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([provider, providerAccountId])\n  @@map("account")\n}\n\nmodel Session {\n  id           String   @id @default(cuid())\n  sessionToken String   @unique @map("session_token")\n  userId       String   @map("user_id")\n  expires      DateTime\n  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map("session")\n}\n\nmodel User {\n  id                 String             @id @default(cuid())\n  name               String?\n  email              String?            @unique\n  emailVerified      DateTime?          @map("email_verified") @db.Timestamptz(3)\n  image              String?\n  created_at         DateTime           @default(now()) @db.Timestamptz(3)\n  updated_at         DateTime           @default(now()) @db.Timestamptz(3)\n  accounts           Account[]\n  auto_order_prefill AutoOrderPrefill[]\n  subscriptions      PushSubscription[]\n  sessions           Session[]\n  user_profiles      UserProfile?\n  watchlist          Watchlist[]\n\n  @@map("user")\n}\n\nmodel VerificationToken {\n  identifier String\n  token      String   @unique\n  expires    DateTime\n\n  @@unique([identifier, token])\n  @@map("verification_token")\n}\n\nmodel UserProfile {\n  user_id    String    @id\n  first_name String?\n  last_name  String?\n  settings   Json?\n  created_at DateTime  @default(now()) @db.Timestamptz(3)\n  updated_at DateTime  @updatedAt @db.Timestamptz(3)\n  deleted_at DateTime? @db.Timestamptz(3)\n  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)\n\n  @@map("user_profile")\n}\n\nmodel TrendChange {\n  id            String   @id @default(cuid())\n  originalIndex Int      @map("original_index")\n  index         String   @db.VarChar(20)\n  description   String?\n  trend         String   @db.VarChar(12)\n  buyTrade      Decimal  @map("buy_trade") @db.Decimal(9, 3)\n  sellTrade     Decimal  @map("sell_trade") @db.Decimal(9, 3)\n  previousClose Decimal  @map("previous_close") @db.Decimal(9, 3)\n  date          DateTime @db.Timestamptz(3)\n\n  @@map("trend_change")\n}\n\nmodel UpsideDownsidePotential {\n  id                String   @id @default(cuid())\n  originalIndex     Int      @map("original_index")\n  ticker            String   @db.VarChar(20)\n  description       String?\n  trend             String   @db.VarChar(12)\n  downsidePotential Decimal  @map("downside_potential") @db.Decimal(9, 2)\n  upsidePotential   Decimal  @map("upside_potential") @db.Decimal(9, 2)\n  date              DateTime @db.Timestamptz(3)\n  price             Decimal  @db.Money\n\n  @@map("upside_downside_potential")\n}\n\nmodel PushSubscription {\n  id         String    @id @default(cuid())\n  endpoint   String\n  auth       String\n  p256dh     String\n  user_id    String\n  created_at DateTime  @default(now()) @db.Timestamptz(3)\n  updated_at DateTime  @default(now()) @db.Timestamptz(3)\n  deleted_at DateTime? @db.Timestamptz(3)\n  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)\n\n  @@map("push_subscription")\n}\n\nmodel Watchlist {\n  id         String    @id @default(cuid())\n  user_id    String\n  ticker     String\n  created_at DateTime  @default(now()) @db.Timestamptz(3)\n  updated_at DateTime  @default(now()) @db.Timestamptz(3)\n  deleted_at DateTime? @db.Timestamptz(3)\n  settings   Json?\n  audit_log  Json?\n  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)\n\n  @@map("watchlist")\n}\n\nmodel AutoOrderPrefill {\n  id         String    @id @default(cuid())\n  user_id    String\n  action     String\n  ticker     String\n  quantity   Int\n  price      Decimal   @db.Money\n  order_type String\n  account_id String\n  created_at DateTime  @default(now()) @db.Timestamptz(3)\n  updated_at DateTime  @default(now()) @db.Timestamptz(3)\n  deleted_at DateTime? @db.Timestamptz(3)\n  metadata   Json?\n  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)\n\n  @@map("auto_order_prefill")\n}\n',inlineSchemaHash:"2994042ef08a75de3c9d8ac2e5068cbd88a50c32b7a4de10ea31c345991c0ae9",copyEngine:!0},R=r(29021);if(S.dirname=__dirname,!R.existsSync(T.join(__dirname,"schema.prisma"))){let e=["../../node_modules/.pnpm/@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/.prisma/client","../node_modules/.pnpm/@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/.prisma/client"],t=e.find(e=>R.existsSync(T.join(process.cwd(),e,"schema.prisma")))??e[0];S.dirname=T.join(process.cwd(),t),S.isBundled=!0}S.runtimeDataModel=JSON.parse('{"models":{"Account":{"dbName":"account","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"userId","dbName":"user_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"provider","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"providerAccountId","dbName":"provider_account_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"refresh_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"access_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"expires_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"token_type","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"scope","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"id_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"session_state","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"oauth_token_secret","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"oauth_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"AccountToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["provider","providerAccountId"]],"uniqueIndexes":[{"name":null,"fields":["provider","providerAccountId"]}],"isGenerated":false},"Session":{"dbName":"session","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"sessionToken","dbName":"session_token","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"userId","dbName":"user_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"expires","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"SessionToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"User":{"dbName":"user","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"name","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"email","kind":"scalar","isList":false,"isRequired":false,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"emailVerified","dbName":"email_verified","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"image","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"created_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updated_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"accounts","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Account","nativeType":null,"relationName":"AccountToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"auto_order_prefill","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"AutoOrderPrefill","nativeType":null,"relationName":"AutoOrderPrefillToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"subscriptions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"PushSubscription","nativeType":null,"relationName":"PushSubscriptionToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"sessions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Session","nativeType":null,"relationName":"SessionToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"user_profiles","kind":"object","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"UserProfile","nativeType":null,"relationName":"UserToUserProfile","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"watchlist","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Watchlist","nativeType":null,"relationName":"UserToWatchlist","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"VerificationToken":{"dbName":"verification_token","schema":null,"fields":[{"name":"identifier","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"token","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"expires","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["identifier","token"]],"uniqueIndexes":[{"name":null,"fields":["identifier","token"]}],"isGenerated":false},"UserProfile":{"dbName":"user_profile","schema":null,"fields":[{"name":"user_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"first_name","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"last_name","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"settings","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"created_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updated_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":true},{"name":"deleted_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"UserToUserProfile","relationFromFields":["user_id"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"TrendChange":{"dbName":"trend_change","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"originalIndex","dbName":"original_index","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"index","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["VarChar",["20"]],"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"trend","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["VarChar",["12"]],"isGenerated":false,"isUpdatedAt":false},{"name":"buyTrade","dbName":"buy_trade","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Decimal",["9","3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"sellTrade","dbName":"sell_trade","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Decimal",["9","3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"previousClose","dbName":"previous_close","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Decimal",["9","3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"date","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"UpsideDownsidePotential":{"dbName":"upside_downside_potential","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"originalIndex","dbName":"original_index","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"ticker","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["VarChar",["20"]],"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"trend","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":["VarChar",["12"]],"isGenerated":false,"isUpdatedAt":false},{"name":"downsidePotential","dbName":"downside_potential","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Decimal",["9","2"]],"isGenerated":false,"isUpdatedAt":false},{"name":"upsidePotential","dbName":"upside_potential","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Decimal",["9","2"]],"isGenerated":false,"isUpdatedAt":false},{"name":"date","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"price","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Money",[]],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"PushSubscription":{"dbName":"push_subscription","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"endpoint","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"auth","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"p256dh","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"created_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updated_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"deleted_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"PushSubscriptionToUser","relationFromFields":["user_id"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Watchlist":{"dbName":"watchlist","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"user_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"ticker","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"created_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updated_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"deleted_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"settings","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"audit_log","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"UserToWatchlist","relationFromFields":["user_id"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"AutoOrderPrefill":{"dbName":"auto_order_prefill","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"user_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"action","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"ticker","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"quantity","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"price","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Decimal","nativeType":["Money",[]],"isGenerated":false,"isUpdatedAt":false},{"name":"order_type","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"account_id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"created_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updated_at","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":["Timestamptz",["3"]],"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"deleted_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":["Timestamptz",["3"]],"isGenerated":false,"isUpdatedAt":false},{"name":"metadata","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"AutoOrderPrefillToUser","relationFromFields":["user_id"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false}},"enums":{},"types":{}}'),w(t.Prisma,S.runtimeDataModel),S.engineWasm=void 0,S.compilerWasm=void 0;let{warnEnvConflicts:k}=r(63667);k({rootEnvPath:S.relativeEnvPaths.rootEnvPath&&T.resolve(S.dirname,S.relativeEnvPaths.rootEnvPath),schemaEnvPath:S.relativeEnvPaths.schemaEnvPath&&T.resolve(S.dirname,S.relativeEnvPaths.schemaEnvPath)}),t.PrismaClient=l(S),Object.assign(t,P),T.join(__dirname,"libquery_engine-darwin-arm64.dylib.node"),T.join(process.cwd(),"../../node_modules/.pnpm/@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/.prisma/client/libquery_engine-darwin-arm64.dylib.node"),T.join(__dirname,"schema.prisma"),T.join(process.cwd(),"../../node_modules/.pnpm/@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/.prisma/client/schema.prisma")},94970:(e,t,r)=>{e.exports={...r(86647)}}};