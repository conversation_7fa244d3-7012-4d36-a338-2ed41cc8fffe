{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "rIJjUWLYgjUFbGb2dE04U", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "0587d42dbc9cb0339baaac002f186410", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "78c0c92d59efadac6e4813a97677998fc570e23c9b5f89b68e4e4c205fbd0352", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2bf6b5773cd83211a514f8ba5a30becc3ca80649561a282b94021de7b7a8c9d0"}}}, "functions": {}, "sortedMiddleware": ["/"]}