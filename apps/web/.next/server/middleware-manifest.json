{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "o4wTkk7bI04LHu_YmFnZf", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "fe06544ed19d5968b19e9480a76864f4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "07c8ba7f774b243d1f6496bcd9057da5af444fb6936c917f4383bf4fe38f198f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2451a1944962605d778ca6b37de44faf1bff01cc75f40a1a0bc48c3642a4d067"}}}, "functions": {}, "sortedMiddleware": ["/"]}