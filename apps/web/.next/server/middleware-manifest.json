{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "me820GGiJasqnrmtGa-ZX", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "2fd41124a4aa43fd134eb3153151789e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "262205520ffdcd16748dd75fa3ec95507d4f7193c28c547a4c10313c20cc97f5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8c54c37cb344200e5a5a4c7405ffd582c1513a0fee42836f286a2fcb8801b944"}}}, "functions": {}, "sortedMiddleware": ["/"]}