{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|manifest.json|.*\\.png$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|manifest.json|.*\\.png$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "-znQZ12iihgkjQ3BACCdO", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MZtz+m9YouAD4e7Yc/ap1vXOsNextrxIX48/Z7xGWP4=", "__NEXT_PREVIEW_MODE_ID": "54bcd1986e88a05cf880afdc0af1777b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3bf27d5aa61aed02cce15e9413200a2b5f5e0ff99163bececa0133d0c0a80db8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5c7376d69dd0d39c5f22433afa56a0f1995dbd8c68343243e82e6631feaece34"}}}, "functions": {}, "sortedMiddleware": ["/"]}