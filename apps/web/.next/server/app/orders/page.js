(()=>{var e={};e.id=1778,e.ids=[1778],e.modules={416:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>u});var s=t(43197),a=t(14824),d=t(51001);let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",e),...r}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},1708:e=>{"use strict";e.exports=require("node:process")},3188:(e,r,t)=>{"use strict";function s(e,[r,t]){return Math.min(t,Math.max(r,e))}t.d(r,{q:()=>s})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},7628:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=t(42585),a=t(59246),d=t(63528),i=t.n(d),n=t(83599),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let o={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81760)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/orders/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/orders/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11742:(e,r,t)=>{Promise.resolve().then(t.bind(t,35545))},13317:(e,r,t)=>{"use strict";t.d(r,{j2:()=>o,Y9:()=>l,Jv:()=>c});var s=t(74723),a=t(89886),d=t(68941),i=t(30935);let n={...{secret:process.env.AUTH_SECRET,providers:[i.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,a.y)(d.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let t=await d.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!t)return e;let s=t.user_profiles?.settings;return{...e,user:{...e.user,id:t.id,role:s.role??"user"}}}}},{handlers:l,auth:o,signIn:c,signOut:u}=(0,s.Ay)(n)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31595:(e,r,t)=>{"use strict";t.d(r,{V:()=>d,e:()=>i});var s=t(20349);t(71241);var a=t(80336);async function d(e){let r=await a.A.userProfile.findUnique({where:{user_id:e}});return r?{userId:r.user_id,firstName:r.first_name||"",lastName:r.last_name||"",createdAt:r.created_at,updatedAt:r.updated_at,deletedAt:r.deleted_at,settings:r.settings}:null}async function i(e,r){let t=await a.A.userProfile.update({where:{user_id:e},data:{first_name:r.firstName,last_name:r.lastName,settings:JSON.parse(JSON.stringify(r.settings)),updated_at:new Date}});return{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}}(0,t(68785).D)([d,i]),(0,s.A)(d,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,s.A)(i,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},33873:e=>{"use strict";e.exports=require("path")},39832:(e,r,t)=>{"use strict";t.d(r,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>i,nA:()=>u,nd:()=>c});var s=t(43197),a=t(14824),d=t(51001);let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,d.cn)("w-full caption-bottom text-sm",e),...r})}));i.displayName="Table";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("thead",{ref:t,className:(0,d.cn)("[&_tr]:border-b",e),...r}));n.displayName="TableHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("tbody",{ref:t,className:(0,d.cn)("[&_tr:last-child]:border-0",e),...r}));l.displayName="TableBody",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("tfoot",{ref:t,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("tr",{ref:t,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r}));o.displayName="TableRow";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("th",{ref:t,className:(0,d.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...r}));c.displayName="TableHead";let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("td",{ref:t,className:(0,d.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...r}));u.displayName="TableCell",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("caption",{ref:t,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",e),...r})).displayName="TableCaption"},43169:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(99292);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},45806:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(44736).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},51948:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(14824);function a(e){let[r,t]=(0,s.useState)(!1);return r}},55511:e=>{"use strict";e.exports=require("crypto")},55751:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});var s=t(43197),a=t(14824),d=t(47990),i=t(51001);let n=a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsxs)(d.bL,{ref:a,className:(0,i.cn)("relative overflow-hidden",e),...t,children:[(0,s.jsx)(d.LM,{className:"h-full w-full rounded-[inherit]",children:r}),(0,s.jsx)(l,{}),(0,s.jsx)(d.OK,{})]}));n.displayName=d.bL.displayName;let l=a.forwardRef(({className:e,orientation:r="vertical",...t},a)=>(0,s.jsx)(d.VM,{ref:a,orientation:r,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:(0,s.jsx)(d.lr,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=d.VM.displayName},57975:e=>{"use strict";e.exports=require("node:util")},61786:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"409a303cc9c9a1946c183a09b48b42bcabb7822fd3":()=>s.i});var s=t(62806)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64357:(e,r,t)=>{"use strict";t.d(r,{E:()=>n});var s=t(43197);t(14824);var a=t(71001),d=t(51001);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-500 hover:bg-green-500/20"}},defaultVariants:{variant:"default"}});function n({className:e,variant:r,...t}){return(0,s.jsx)("div",{className:(0,d.cn)(i({variant:r}),e),...t})}},68941:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(67566);let a=globalThis.__prisma||new s.PrismaClient},73024:e=>{"use strict";e.exports=require("node:fs")},76658:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=t(52927),a=t(60154),d=t(83687);let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",e),...r}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,d.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80336:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(51291);let a=globalThis.__prisma||new s.PrismaClient},81760:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>N});var s=t(52927),a=t(83687);function d({className:e,...r}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...r})}var i=t(13317),n=t(52058),l=t(60154),o=t(93507),c=t(76658),u=t(53471);let p=(0,t(16386).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var f=t(57657),m=t(21749);let x=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,v=m.$,b=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return v(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:d}=r,i=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],s=null==d?void 0:d[e];if(null===r)return null;let i=x(r)||x(s);return a[e][i]}),n=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return v(e,i,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...d,...n}[r]):({...d,...n})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),h=l.forwardRef(({className:e,variant:r,...t},d)=>(0,s.jsx)("div",{ref:d,role:"alert",className:(0,a.cn)(b({variant:r}),e),...t}));h.displayName="Alert";let g=l.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,a.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));g.displayName="AlertTitle";let y=l.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("text-sm [&_p]:leading-relaxed",e),...r}));async function N(){let e=await (0,i.j2)();return e||(0,n.redirect)("/auth"),(0,s.jsxs)("div",{className:"space-y-4 py-4 p-4",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"Orders"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"View and manage your trading orders and auto order prefills."})]}),(0,s.jsxs)(h,{className:"bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"}),(0,s.jsx)(g,{className:"text-amber-800 dark:text-amber-400",children:"IBKR Connection Required"}),(0,s.jsx)(y,{className:"text-amber-700 dark:text-amber-300",children:"Active orders will appear here when connected to your IBKR account. Currently displaying auto order prefills only."})]}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(p,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Auto Order Prefills"})]}),(0,s.jsx)(c.BT,{children:"These prefilled orders are ready to be submitted when you connect to your IBKR account."})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsx)(l.Suspense,{fallback:(0,s.jsx)(d,{className:"h-[400px]"}),children:(0,s.jsx)(o.AutoOrderPrefillList,{userId:e.user.id})})})]}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Active Orders"})]}),(0,s.jsx)(c.BT,{children:"Connect to IBKR to view and manage your active orders."})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"rounded-full bg-muted p-3 mb-4",children:(0,s.jsx)(f.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,s.jsx)("h3",{className:"font-medium mb-1",children:"No Connection to IBKR"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground max-w-md",children:"Connect to your Interactive Brokers account to view your active orders. You can set up the connection in your profile settings."})]})})]})]})}y.displayName="AlertDescription"},83687:(e,r,t)=>{"use strict";t.d(r,{cn:()=>d});var s=t(21749),a=t(66501);function d(...e){return(0,a.QP)((0,s.$)(e))}},88182:(e,r,t)=>{Promise.resolve().then(t.bind(t,93507))},93507:(e,r,t)=>{"use strict";t.d(r,{AutoOrderPrefillList:()=>s});let s=(0,t(21601).registerClientReference)(function(){throw Error("Attempted to call AutoOrderPrefillList() from the server but AutoOrderPrefillList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/auto-order-prefill-list.tsx","AutoOrderPrefillList")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,4923,9292,575,3265,2452,4515,100],()=>t(7628));module.exports=s})();