"use strict";(()=>{var e={};e.id=1684,e.ids=[1684],e.modules={1274:(e,r,t)=>{t.d(r,{A:()=>d,_:()=>o});var a=t(43806),n=t(91581),i=t(22333);class s{static async autoOrder(e,r){await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/order/auto-order`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":process.env.INTERNAL_API_KEY??""},body:JSON.stringify({changes:e,userId:r})})}}async function o(e,r,t){let s=await (0,a.V)(t),o=s?.settings?.enableAutoOrderPrefill??!1,d=s?.settings?.defaultIBAccountId??"";if(!o||!d)return JSON.stringify({status:"error",message:"Auto-order prefill is not enabled"});let l=await Promise.all(e.map(async e=>({...e,marketQuote:await (0,n.TZ)(e.symbol)}))),u=await Promise.all(r.map(async e=>({...e,marketQuote:await (0,n.TZ)(e.symbol)})));return await (0,i.k)(t,l,u),JSON.stringify({status:"success",message:"Auto-order prefill is enabled"})}let d=s},1708:e=>{e.exports=require("node:process")},2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},13275:(e,r,t)=>{t.d(r,{Jt:()=>o,KU:()=>d,Nd:()=>l,u6:()=>u,w4:()=>s});var a=t(49068);t(77048);var n=t(68941),i=t(84672);let s=async(e,r=!0)=>{if(console.info("1. Input targetDate:",e),isNaN(e.getTime()))return console.info("2. Invalid date detected:",e),[];let t=e.toISOString().split("T")[0];console.info("3. Converted dateString:",t);let a=await l();console.info("4. Retrieved archives:",{archiveDates:a.trendChanges.map(e=>e.toISOString().split("T")[0])});let i=a.trendChanges.some(e=>e.toISOString().split("T")[0]===t);if(console.info("5. Date exists in archives?",i),!i&&r){if(console.info("6. Target date not found, finding closest date..."),0===a.trendChanges.length)throw Error("No trend changes available in archives");console.info("7. Found closest date:",t=a.trendChanges.reduce((r,t)=>{let a=Math.abs(t.getTime()-e.getTime()),n=Math.abs(r.getTime()-e.getTime()),i=a<n?t:r;return console.info("6a. Comparing dates:",{date:t.toISOString(),diff:a,isCloser:a<n}),i},a.trendChanges[0]).toISOString().split("T")[0])}console.info("8. Querying database with dateString:",t);let s=(await n.A.$queryRaw`SELECT id, original_index as "originalIndex", index, description, trend, buy_trade as "buyTrade", sell_trade as "sellTrade", previous_close as "previousClose", date FROM "trend_change" WHERE CAST(date AS DATE) = CAST(${t} AS DATE);`).map(e=>({...e,buyTrade:"object"==typeof e.buyTrade&&null!==e.buyTrade?Number(e.buyTrade.toString()):e.buyTrade,sellTrade:"object"==typeof e.sellTrade&&null!==e.sellTrade?Number(e.sellTrade.toString()):e.sellTrade,previousClose:"object"==typeof e.previousClose&&null!==e.previousClose?Number(e.previousClose.toString()):e.previousClose}));return console.info("9. Query results:",{count:s.length,firstResult:s[0]}),s};async function o(e){let r=e?new Date(e):new Date;r.setUTCHours(12,0,0,0);let t=await s(r);if(0===t.length){let e=await n.A.trendChange.findFirst({orderBy:{date:"desc"}});return JSON.stringify(await s(e?.date?new Date(e.date.setUTCHours(12,0,0,0)):new Date))}return JSON.stringify(t.map(e=>({...e,date:e.date.toISOString()})))}async function d(e){if(0===e.length)return 0;let r=await s(e[0].date,!1);if(console.info("existing.length ->",r.length),r&&r.length>0)return console.log("Existing trend change already in the database for this date: ",e[0].date),0;let t=e.map((e,r)=>({...e,originalIndex:e.originalIndex??r,buyTrade:"number"==typeof e.buyTrade?e.buyTrade:Number(e.buyTrade),sellTrade:"number"==typeof e.sellTrade?e.sellTrade:Number(e.sellTrade),previousClose:"number"==typeof e.previousClose?e.previousClose:Number(e.previousClose)}));return(await n.A.trendChange.createMany({data:t})).count}async function l(){try{let e=await n.A.trendChange.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[],r=await n.A.upsideDownsidePotential.groupBy({by:["date"],_max:{date:!0},orderBy:{date:"desc"}})||[];return{trendChanges:e.map(e=>e._max.date).filter(e=>null!==e),visualization:r.map(e=>e._max.date).filter(e=>null!==e)}}catch(e){return console.error("Error fetching archives of risk signals:",e),{trendChanges:[],visualization:[]}}}async function u(){let e=await l();if(0===e.trendChanges.length)throw Error("No trend changes found in archives");return e.trendChanges[0].toISOString()}(0,i.D)([s,o,d,l,u]),(0,a.A)(s,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a",null),(0,a.A)(o,"4082914884143dd2970140de0dd1f1be2975301463",null),(0,a.A)(d,"40e54e4c2e785198c3edb84e94b2846d18cc39479b",null),(0,a.A)(l,"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b",null),(0,a.A)(u,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520",null)},16698:e=>{e.exports=require("node:async_hooks")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20854:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var n=t(96849),i=t(59246),s=t(27109),o=t(52461),d=e([o]);o=(d.then?(await d)():d)[0];let u=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/push/notify-trend-changes/route",pathname:"/api/push/notify-trend-changes",filename:"route",bundlePath:"app/api/push/notify-trend-changes/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify-trend-changes/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:f}=u;function l(){return(0,s.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}a()}catch(e){a(e)}})},21820:e=>{e.exports=require("os")},22333:(e,r,t)=>{t.d(r,{k:()=>s});var a=t(49068);t(77048);var n=t(43806),i=t(68941);async function s(e,r,t){if(!e)throw Error("User ID is required");try{let a=await (0,n.V)(e),s=a?.settings?.enableAutoOrderPrefill??!1,o=a?.settings?.defaultIBAccountId;if(!s||!o)return[];let d=r.map(r=>({user_id:e,account_id:o,order_type:"MARKET",action:"BUY",ticker:r.symbol,quantity:1,price:r.marketQuote.price})),l=t.map(r=>({user_id:e,account_id:o,order_type:"MARKET",action:"SELL",ticker:r.symbol,quantity:1,price:r.marketQuote.price})),u=[...d,...l];return await i.A.autoOrderPrefill.createMany({data:u})}catch(e){throw console.error("[CREATE_AUTO_ORDER_PREFILL_ERROR]",e),Error(e instanceof Error?e.message:"Failed to create auto order prefill record")}}(0,t(84672).D)([s]),(0,a.A)(s,"705b4adc5d4233fa16130f30f56b1e17fe8d25b9cd",null)},22978:(e,r,t)=>{t.d(r,{Hr:()=>h});var a=t(60154),n=t(52927),i=Object.defineProperty,s=Object.defineProperties,o=Object.getOwnPropertyDescriptors,d=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,c=(e,r,t)=>r in e?i(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,p=(e,r)=>{for(var t in r||(r={}))l.call(r,t)&&c(e,t,r[t]);if(d)for(var t of d(r))u.call(r,t)&&c(e,t,r[t]);return e},f=(e,r)=>s(e,o(r)),g=(e,r)=>{var t={};for(var a in e)l.call(e,a)&&0>r.indexOf(a)&&(t[a]=e[a]);if(null!=e&&d)for(var a of d(e))0>r.indexOf(a)&&u.call(e,a)&&(t[a]=e[a]);return t},h=a.forwardRef((e,r)=>{var{style:t}=e,a=g(e,["style"]);return(0,n.jsx)("hr",f(p({},a),{ref:r,style:p({width:"100%",border:"none",borderTop:"1px solid #eaeaea"},t)}))});h.displayName="Hr"},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},39100:(e,r,t)=>{t.d(r,{n:()=>s});var a=t(49068);t(77048);var n=t(13275);function i(e){let r=e instanceof Date?e:new Date(e);if(isNaN(r.getTime()))throw Error("Invalid date provided");let t=r.getFullYear();return new Date(Date.UTC(t,r.getMonth(),r.getDate(),0,0,0))}async function s(e,r){let t,a=i(e);if(r?t=i(r):((t=new Date(a)).setDate(t.getDate()-1),t=i(t)),t.getTime()===a.getTime())throw Error("previousTrendChangeDate and selectedTrendChangeDate cannot be the same date");if(t>a)throw Error("previousTrendChangeDate must be before selectedTrendChangeDate");if(t.getFullYear()!==a.getFullYear())throw Error("dates must be in the same year");if(t.getMonth()!==a.getMonth())throw Error("dates must be in the same month");let s=await (0,n.w4)(a),o=await (0,n.w4)(t),d=new Map(s.map(e=>[e.index,e])),l=new Map(o.map(e=>[e.index,e])),u=[],c=new Set;for(let[e,r]of d){if(c.has(e))continue;let t=l.get(e);t&&r.trend!==t.trend&&("BULLISH"===r.trend&&"BEARISH"===t.trend||"BEARISH"===r.trend&&"BULLISH"===t.trend||"NEUTRAL"===r.trend&&("BULLISH"===t.trend||"BEARISH"===t.trend)||("BULLISH"===r.trend||"BEARISH"===r.trend)&&"NEUTRAL"===t.trend)&&(u.push({current:r,previous:t}),c.add(e))}return u}(0,t(84672).D)([s]),(0,a.A)(s,"60afccde42b0dc1f25d6ac9ae7ee09877d448467b4",null)},43806:(e,r,t)=>{t.d(r,{V:()=>i,e:()=>s});var a=t(49068);t(77048);var n=t(68941);async function i(e){let r=await n.A.userProfile.findUnique({where:{user_id:e}});return r?{userId:r.user_id,firstName:r.first_name||"",lastName:r.last_name||"",createdAt:r.created_at,updatedAt:r.updated_at,deletedAt:r.deleted_at,settings:r.settings}:null}async function s(e,r){let t=await n.A.userProfile.update({where:{user_id:e},data:{first_name:r.firstName,last_name:r.lastName,settings:JSON.parse(JSON.stringify(r.settings)),updated_at:new Date}});return{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}}(0,t(84672).D)([i,s]),(0,a.A)(i,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,a.A)(s,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},52461:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{POST:()=>g});var n=t(87728),i=t(17304),s=t(68941),o=t(13317),d=t(39100),l=t(78631),u=t(98595),c=t(61835),p=t(1274),f=e([u,c]);async function g(e){try{if(e.headers.get("x-api-key")!==process.env.INTERNAL_API_KEY){let e=await (0,o.j2)();if(!e?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401})}let{selectedTrendChangeDate:r}=await e.json().catch(()=>({}));if(!r)return n.NextResponse.json({error:"selectedTrendChangeDate is required"},{status:400});let t=await (0,d.n)(r);if(!t?.length)return n.NextResponse.json({message:"No trend changes found"});let a=await s.A.pushSubscription.findMany({where:{deleted_at:null}});if(!a.length)return n.NextResponse.json({message:"No active subscriptions found"});let f={};a.forEach(e=>{f[e.user_id]||(f[e.user_id]=[]),f[e.user_id].push(e)});let g=await Promise.allSettled(Object.entries(f).map(async([e,a])=>{try{let n=(await s.A.watchlist.findMany({where:{user_id:e}})).filter(e=>t.some(r=>r.current.index===e.ticker));if(n.length){let o=n.map(e=>{let a=t.find(r=>r.current.index===e.ticker);return{symbol:e.ticker,previousTrend:a?.previous?.trend||"Unknown",newTrend:a?.current?.trend||"Unknown",date:r}});o=function(e){let r=new Map;return e.forEach(e=>{let t=`${e.symbol}-${e.previousTrend}-${e.newTrend}-${e.date}`;r.set(t,e)}),Array.from(r.values())}(o),await Promise.all(a.map(async e=>{let r={title:"Risk Signal Trend Changes",body:`Trend changes detected for: ${[...new Set(n.map(e=>e.ticker))].join(", ")}`,icon:"/icon-512x512.png",badge:"/icon-96x96.png",data:{url:"/risk-signals",tickers:n.map(e=>e.ticker)}};if(!e.endpoint||!e.p256dh||!e.auth)throw Error("Invalid subscription details");try{await i.sendNotification({endpoint:e.endpoint,keys:{p256dh:e.p256dh,auth:e.auth}},JSON.stringify(r))}catch(r){throw 410===r.statusCode&&await s.A.pushSubscription.update({where:{id:e.id},data:{deleted_at:new Date}}),r}}));let d=await s.A.user.findUnique({where:{id:e},select:{name:!0,email:!0}});if(!d?.email)throw Error("User email not found");let f={body:await (0,c.render)((0,u.A)({changes:o,recipientName:d.name||void 0})),to:[d.email],subject:`Trend Changes Alert - ${o.length} Updates`};await (0,l.Z)(f),await p.A.autoOrder(o,e)}return{success:!0,userId:e}}catch(r){return{success:!1,userId:e,error:r.message||"Unknown error"}}}));return n.NextResponse.json({results:g})}catch(e){return console.error("Error sending notifications:",e),n.NextResponse.json({error:"Failed to send notifications"},{status:500})}}[u,c]=f.then?(await f)():f,i.setVapidDetails("mailto:<EMAIL>",process.env.VAPID_PUBLIC_KEY,process.env.VAPID_PRIVATE_KEY),a()}catch(e){a(e)}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83505:e=>{e.exports=import("prettier/standalone")},83997:e=>{e.exports=require("tty")},84169:(e,r,t)=>{t.r(r),t.d(r,{"0059add535fb01195d5e0ce2de3fa23c92c6dd6a1b":()=>n.Nd,"00651d8bbf79c87fe037bd0f517ffb0e18dcb67520":()=>n.u6,"4061815cf2a2c2868673e85dee1a5fab71cb0423aa":()=>i.Z,"4082914884143dd2970140de0dd1f1be2975301463":()=>n.Jt,"40e54e4c2e785198c3edb84e94b2846d18cc39479b":()=>n.KU,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665":()=>s.V,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e":()=>s.e,"60afccde42b0dc1f25d6ac9ae7ee09877d448467b4":()=>a.n,"705b4adc5d4233fa16130f30f56b1e17fe8d25b9cd":()=>o.k,"7fbbf22445abc6f8e0fd5017d598fdb53bdf774d0a":()=>n.w4});var a=t(39100),n=t(13275),i=t(78631),s=t(43806),o=t(22333)},84297:e=>{e.exports=require("async_hooks")},91581:(e,r,t)=>{t.d(r,{TZ:()=>s,fR:()=>d,gO:()=>o});var a=t(91773),n=t(47524);n.A.suppressNotices(["yahooSurvey"]);let i={SPX:"^GSPC",VIX:"^VIX",DJI:"^DJI",IXIC:"^IXIC"};async function s(e,r=2){(0,a.unstable_noStore)();try{let t=i[e]||e;for(let a=0;a<=r;a++)try{a>0&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,a)));let e=await n.A.quote(t,{fields:["regularMarketPrice","regularMarketChange","regularMarketChangePercent","regularMarketTime"]});if(e&&"number"==typeof e.regularMarketPrice)return{regularMarketPrice:e.regularMarketPrice,regularMarketChange:e.regularMarketChange??0,regularMarketChangePercent:e.regularMarketChangePercent??0,regularMarketTime:new Date(e.regularMarketTime??Date.now())};throw Error("Invalid response format")}catch(t){if(console.warn(`Attempt ${a+1} failed for ${e}:`,t),a===r)throw t}throw Error("All retry attempts failed")}catch(r){return console.error(`Failed to fetch quote for ${e}:`,r),{regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date}}}async function o(e,r=2){(0,a.unstable_noStore)();try{let t=e.map(e=>i[e]||e);for(let a=0;a<=r;a++)try{let r=await n.A.quote(t,{fields:["regularMarketPrice","regularMarketChange","regularMarketChangePercent","regularMarketTime"]}),a={};return(Array.isArray(r)?r:[r]).forEach((r,t)=>{let n=e[t];n&&(r&&"number"==typeof r.regularMarketPrice?a[n]={regularMarketPrice:r.regularMarketPrice,regularMarketChange:r.regularMarketChange??0,regularMarketChangePercent:r.regularMarketChangePercent??0,regularMarketTime:new Date(r.regularMarketTime??Date.now())}:a[n]={regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date})}),a}catch(e){if(a===r)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("All retry attempts failed")}catch(r){return console.error(`Failed to fetch quotes for ${e.join(", ")}:`,r),e.reduce((e,r)=>(e[r]={regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date},e),{})}}function d(){n.A.setGlobalConfig({queue:{concurrency:2,timeout:5e3},validation:{logErrors:!0}})}},91645:e=>{e.exports=require("net")},98595:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.d(r,{A:()=>S});var n=t(52927),i=t(95444),s=t(11611),o=t(83756),d=t(64214),l=t(82906),u=t(57582),c=t(62892),p=t(64313),f=t(22978);let e={backgroundColor:"#f6f9fc",fontFamily:'-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'},g={backgroundColor:"#ffffff",margin:"0 auto",padding:"40px 20px",borderRadius:"5px",maxWidth:"600px"},h={color:"#1a1a1a",fontSize:"24px",lineHeight:"1.3",margin:"0 0 20px"},m={fontSize:"16px",lineHeight:"1.5",color:"#4a5568",margin:"0 0 20px"},y={margin:"20px 0"},b={padding:"10px 0"},x={fontSize:"18px",fontWeight:"bold",margin:"0 0 5px",color:"#2d3748"},w={display:"flex",alignItems:"center",gap:"8px",margin:"5px 0"},T={fontSize:"16px",fontWeight:"500",margin:"0"},v={fontSize:"16px",color:"#718096",margin:"0"},A={fontSize:"14px",color:"#718096",margin:"5px 0 0"},k={borderTop:"1px solid #e2e8f0",margin:"10px 0"},C={fontSize:"14px",color:"#718096",margin:"30px 0 0",textAlign:"center"},S=({changes:r,recipientName:t})=>{let a=e=>{switch(e.toLowerCase()){case"bullish":return"#22c55e";case"bearish":return"#ef4444";case"neutral":return"#f59e0b";default:return"#6b7280"}};return(0,n.jsxs)(i.E,{children:[(0,n.jsx)(s.p,{}),(0,n.jsx)(o.l,{children:`Daily Trend Changes Alert - ${r.length} Updates`}),(0,n.jsx)(d.n,{style:e,children:(0,n.jsxs)(l.m,{style:g,children:[(0,n.jsx)(u.D,{as:"h1",style:h,children:"Market Trend Changes Alert"}),(0,n.jsx)(c.E,{style:m,children:t?`Hello ${t},`:"Hello,"}),(0,n.jsx)(c.E,{style:m,children:"The following ticker symbols have experienced trend changes:"}),(0,n.jsx)(p.w,{style:y,children:r.map((e,t)=>(0,n.jsxs)("div",{style:b,children:[(0,n.jsx)(c.E,{style:x,children:e.symbol}),(0,n.jsxs)("div",{style:w,children:[(0,n.jsx)(c.E,{style:{...T,color:a(e.previousTrend)},children:e.previousTrend}),(0,n.jsx)(c.E,{style:v,children:"→"}),(0,n.jsx)(c.E,{style:{...T,color:a(e.newTrend)},children:e.newTrend})]}),(0,n.jsx)(c.E,{style:A,children:e.date}),t<r.length-1&&(0,n.jsx)(f.Hr,{style:k})]},t))}),(0,n.jsx)(c.E,{style:C,children:"This is an automated notification. Please do not reply to this email."})]})})]})};a()}catch(e){a(e)}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,1835,5496,5227,2677],()=>t(20854));module.exports=a})();