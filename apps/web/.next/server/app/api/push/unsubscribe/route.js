(()=>{var e={};e.id=1536,e.ids=[1536],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,r,s)=>{"use strict";s.d(r,{j2:()=>p,Y9:()=>a,Jv:()=>c});var t=s(74723),i=s(89886),o=s(68941),u=s(30935);let n={...{secret:process.env.AUTH_SECRET,providers:[u.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,i.y)(o.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let s=await o.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!s)return e;let t=s.user_profiles?.settings;return{...e,user:{...e.user,id:s.id,role:t.role??"user"}}}}},{handlers:a,auth:p,signIn:c,signOut:d}=(0,t.Ay)(n)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63787:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>b,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{POST:()=>c});var i=s(96849),o=s(59246),u=s(27109),n=s(87728),a=s(68941),p=s(13317);async function c(e){try{let r=await (0,p.j2)();if(!r?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{endpoint:s}=await e.json();console.log("Unsubscribing endpoint:",s);let t=await a.A.pushSubscription.findFirst({where:{endpoint:s,user_id:r.user.id}});return t?(await a.A.pushSubscription.delete({where:{id:t.id}}),console.log("Subscription deleted from database")):console.log("No subscription found to delete"),n.NextResponse.json({message:"Unsubscribed successfully"})}catch(e){return console.error("Error in unsubscribe:",e),n.NextResponse.json({error:"Failed to unsubscribe"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/push/unsubscribe/route",pathname:"/api/push/unsubscribe",filename:"route",bundlePath:"app/api/push/unsubscribe/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/unsubscribe/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:b}=d;function h(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},68941:(e,r,s)=>{"use strict";s.d(r,{A:()=>i});var t=s(67566);let i=globalThis.__prisma||new t.PrismaClient},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},86641:()=>{},87313:()=>{},96849:(e,r,s)=>{"use strict";e.exports=s(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[3491,7728,7566,5045,5438],()=>s(63787));module.exports=t})();