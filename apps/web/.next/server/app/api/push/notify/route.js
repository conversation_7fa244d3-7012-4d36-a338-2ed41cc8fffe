"use strict";(()=>{var e={};e.id=7798,e.ids=[7798],e.modules={1708:e=>{e.exports=require("node:process")},2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},7066:e=>{e.exports=require("node:tty")},10470:(e,r,t)=>{t.a(e,async(e,n)=>{try{t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var i=t(96849),s=t(59246),a=t(27109),o=t(39863),d=e([o]);o=(d.then?(await d)():d)[0];let p=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/push/notify/route",pathname:"/api/push/notify",filename:"route",bundlePath:"app/api/push/notify/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:x}=p;function l(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}n()}catch(e){n(e)}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},16698:e=>{e.exports=require("node:async_hooks")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},22461:(e,r,t)=>{t.r(r),t.d(r,{"4061815cf2a2c2868673e85dee1a5fab71cb0423aa":()=>n.Z});var n=t(78631)},22690:(e,r,t)=>{t.a(e,async(e,n)=>{try{t.d(r,{A:()=>q});var i=t(52927),s=t(95444),a=t(11611),o=t(83756),d=t(64214),l=t(82906),p=t(57582),u=t(62892),c=t(64313),x=t(34314);let e={backgroundColor:"#f6f9fc",fontFamily:'-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'},h={backgroundColor:"#ffffff",margin:"0 auto",padding:"40px 20px",borderRadius:"5px",maxWidth:"600px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},f={color:"#1a1a1a",fontSize:"24px",lineHeight:"1.3",margin:"0 0 20px",textAlign:"center"},g={fontSize:"16px",lineHeight:"1.5",color:"#4a5568",margin:"0 0 20px"},m={margin:"20px 0"},y={backgroundColor:"#f8fafc",padding:"20px",borderRadius:"8px",border:"1px solid #e2e8f0",margin:"20px 0"},b={fontSize:"16px",color:"#2d3748",margin:"10px 0",lineHeight:"1.5"},v={backgroundColor:"#3182ce",borderRadius:"5px",color:"#fff",fontSize:"16px",fontWeight:"bold",textDecoration:"none",textAlign:"center",display:"block",width:"100%",maxWidth:"200px",margin:"20px auto"},j={fontSize:"14px",color:"#718096",fontStyle:"italic",margin:"20px 0",padding:"10px",backgroundColor:"#f7fafc",borderRadius:"5px"},w={fontSize:"14px",color:"#718096",margin:"30px 0 10px",textAlign:"center"},k={fontSize:"12px",color:"#a0aec0",margin:"10px 0 0",textAlign:"center"},q=({recipientName:r,updateDate:t,totalSignals:n,dashboardUrl:q})=>(0,i.jsxs)(s.E,{children:[(0,i.jsx)(a.p,{}),(0,i.jsx)(o.l,{children:"Risk Range Signals Have Been Updated"}),(0,i.jsx)(d.n,{style:e,children:(0,i.jsxs)(l.m,{style:h,children:[(0,i.jsx)(p.D,{as:"h1",style:f,children:"Risk Range Signals Update"}),(0,i.jsx)(u.E,{style:g,children:r?`Hello ${r},`:"Hello,"}),(0,i.jsxs)(c.w,{style:m,children:[(0,i.jsx)(u.E,{style:g,children:"We've successfully updated our Risk Range Signals database. Here's a quick summary:"}),(0,i.jsxs)("div",{style:y,children:[(0,i.jsxs)(u.E,{style:b,children:["✨ ",(0,i.jsx)("strong",{children:"Update Completed:"})," ",t]}),(0,i.jsxs)(u.E,{style:b,children:["\uD83D\uDCCA ",(0,i.jsx)("strong",{children:"Total Signals Processed:"})," ",n]})]}),(0,i.jsx)(u.E,{style:g,children:"You can view the latest signals and analysis on your dashboard:"}),(0,i.jsx)(x.$,{style:{...v,padding:"12px 20px"},href:q,children:"View Dashboard"})]}),(0,i.jsx)(u.E,{style:j,children:"Note: This data is updated automatically to ensure you have the most current market insights."}),(0,i.jsx)(u.E,{style:w,children:"This is an automated notification. Please do not reply to this email."}),(0,i.jsx)(u.E,{style:k,children:"The information provided is for informational purposes only and should not be considered as investment advice."})]})})]});n()}catch(e){n(e)}})},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},34314:(e,r,t)=>{t.d(r,{$:()=>y});var n=t(60154),i=t(52927),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,d=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,c=(e,r)=>{for(var t in r||(r={}))l.call(r,t)&&u(e,t,r[t]);if(d)for(var t of d(r))p.call(r,t)&&u(e,t,r[t]);return e},x=(e,r)=>a(e,o(r)),h=(e,r)=>{var t={};for(var n in e)l.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&d)for(var n of d(e))0>r.indexOf(n)&&p.call(e,n)&&(t[n]=e[n]);return t};function f(e){let r=0;if(!e)return r;if("number"==typeof e)return e;let t=/^([\d.]+)(px|em|rem|%)$/.exec(e);if(!t||3!==t.length)return 0;{let e=parseFloat(t[1]);switch(t[2]){case"px":default:return e;case"em":case"rem":return 16*e;case"%":return e/100*600}}}var g=e=>"number"!=typeof e||isNaN(Number(e))?null:3*e/4;function m(e){if(0===e)return[0,0];let r=0,t=()=>r>0?e/r/2:1/0;for(;t()>5;)r++;return[t(),r]}var y=n.forwardRef((e,r)=>{var t,n,s,a,{children:o,style:d,target:l="_blank"}=e,p=h(e,["children","style","target"]);let{pt:u,pr:y,pb:j,pl:w}=function({padding:e="",paddingTop:r,paddingRight:t,paddingBottom:n,paddingLeft:i}){let s=0,a=0,o=0,d=0;if("number"==typeof e)s=e,a=e,o=e,d=e;else{let r=e.split(/\s+/);switch(r.length){case 1:s=f(r[0]),a=f(r[0]),o=f(r[0]),d=f(r[0]);break;case 2:s=f(r[0]),o=f(r[0]),a=f(r[1]),d=f(r[1]);break;case 3:s=f(r[0]),a=f(r[1]),d=f(r[1]),o=f(r[2]);break;case 4:s=f(r[0]),a=f(r[1]),o=f(r[2]),d=f(r[3])}}return{pt:r?f(r):s,pr:t?f(t):a,pb:n?f(n):o,pl:i?f(i):d}}({padding:null==d?void 0:d.padding,paddingLeft:null!=(t=null==d?void 0:d.paddingLeft)?t:null==d?void 0:d.paddingInline,paddingRight:null!=(n=null==d?void 0:d.paddingRight)?n:null==d?void 0:d.paddingInline,paddingTop:null!=(s=null==d?void 0:d.paddingTop)?s:null==d?void 0:d.paddingBlock,paddingBottom:null!=(a=null==d?void 0:d.paddingBottom)?a:null==d?void 0:d.paddingBlock}),k=g(u+j),[q,R]=m(w),[S,P]=m(y);return(0,i.jsxs)("a",x(c({},p),{ref:r,style:b(x(c({},d),{pt:u,pr:y,pb:j,pl:w})),target:l,children:[(0,i.jsx)("span",{dangerouslySetInnerHTML:{__html:`<!--[if mso]><i style="mso-font-width:${100*q}%;mso-text-raise:${k}" hidden>${"&#8202;".repeat(R)}</i><![endif]-->`}}),(0,i.jsx)("span",{style:v(j),children:o}),(0,i.jsx)("span",{dangerouslySetInnerHTML:{__html:`<!--[if mso]><i style="mso-font-width:${100*S}%" hidden>${"&#8202;".repeat(P)}&#8203;</i><![endif]-->`}})]}))});y.displayName="Button";var b=e=>{let r=e||{},{pt:t,pr:n,pb:i,pl:s}=r;return x(c({lineHeight:"100%",textDecoration:"none",display:"inline-block",maxWidth:"100%",msoPaddingAlt:"0px"},h(r,["pt","pr","pb","pl"])),{padding:`${t}px ${n}px ${i}px ${s}px`})},v=e=>({maxWidth:"100%",display:"inline-block",lineHeight:"120%",msoPaddingAlt:"0px",msoTextRaise:g(e||0)})},34631:e=>{e.exports=require("tls")},39863:(e,r,t)=>{t.a(e,async(e,n)=>{try{t.r(r),t.d(r,{POST:()=>c});var i=t(87728),s=t(17304),a=t(68941),o=t(13317),d=t(78631),l=t(22690),p=t(61835),u=e([l,p]);async function c(e){try{let r=e.headers.get("x-api-key")===process.env.INTERNAL_API_KEY,{message:t,count:n}=await e.json();if(!r){let e=await (0,o.j2)();if(!e?.user?.id)return i.NextResponse.json({error:"Unauthorized"},{status:401})}let u=await a.A.pushSubscription.findMany({where:{deleted_at:null}});if(!u.length)return i.NextResponse.json({message:"No active subscriptions found"});let c={};u.forEach(e=>{c[e.user_id]||(c[e.user_id]=[]),c[e.user_id].push(e)});let x={title:"Risk Signal Update",body:t,icon:"/icon-512x512.png",badge:"/icon-96x96.png",data:{url:"/risk-signals"}},h=await Promise.allSettled(Object.entries(c).map(async([e,r])=>{try{await Promise.all(r.map(async e=>{if(!e.endpoint||!e.p256dh||!e.auth)throw Error("Invalid subscription details");try{await s.sendNotification({endpoint:e.endpoint,keys:{p256dh:e.p256dh,auth:e.auth}},JSON.stringify(x))}catch(r){throw 410===r.statusCode&&await a.A.pushSubscription.update({where:{id:e.id},data:{deleted_at:new Date}}),r}}));let t=await a.A.user.findUnique({where:{id:e},select:{name:!0,email:!0}});if(!t?.email)throw Error("User email not found");let i={body:await (0,p.render)((0,l.A)({recipientName:t.name||void 0,updateDate:new Date().toLocaleDateString(),totalSignals:n,dashboardUrl:`${process.env.NEXT_PUBLIC_APP_URL}/risk-signals`})),to:[t.email],subject:"Risk Range Signals Have Been Updated"};return await (0,d.Z)(i),{success:!0,userId:e}}catch(r){return{success:!1,userId:e,error:r.message||"Unknown error"}}}));return i.NextResponse.json({results:h})}catch(e){return console.error("Error sending notifications:",e),i.NextResponse.json({error:"Failed to send notifications"},{status:500})}}[l,p]=u.then?(await u)():u,s.setVapidDetails("mailto:<EMAIL>",process.env.VAPID_PUBLIC_KEY,process.env.VAPID_PRIVATE_KEY),n()}catch(e){n(e)}})},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83505:e=>{e.exports=import("prettier/standalone")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[3491,7728,6631,7566,5045,5438,1835,5496,5227,2677],()=>t(10470));module.exports=n})();