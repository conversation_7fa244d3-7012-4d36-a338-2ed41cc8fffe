(()=>{var e={};e.id=5623,e.ids=[5623],e.modules={1274:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,_:()=>o});var a=r(43806),s=r(91581),i=r(22333);class n{static async autoOrder(e,t){await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/order/auto-order`,{method:"POST",headers:{"Content-Type":"application/json","x-api-key":process.env.INTERNAL_API_KEY??""},body:JSON.stringify({changes:e,userId:t})})}}async function o(e,t,r){let n=await (0,a.V)(r),o=n?.settings?.enableAutoOrderPrefill??!1,d=n?.settings?.defaultIBAccountId??"";if(!o||!d)return JSON.stringify({status:"error",message:"Auto-order prefill is not enabled"});let u=await Promise.all(e.map(async e=>({...e,marketQuote:await (0,s.TZ)(e.symbol)}))),l=await Promise.all(t.map(async e=>({...e,marketQuote:await (0,s.TZ)(e.symbol)})));return await (0,i.k)(r,u,l),JSON.stringify({status:"success",message:"Auto-order prefill is enabled"})}let d=n},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,t,r)=>{"use strict";r.d(t,{j2:()=>u,Y9:()=>d,Jv:()=>l});var a=r(74723),s=r(89886),i=r(68941),n=r(30935);let o={...{secret:process.env.AUTH_SECRET,providers:[n.A],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}},adapter:(0,s.y)(i.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:t}){let r=await i.A.user.findUnique({where:{email:t.email},include:{user_profiles:!0}});if(!r)return e;let a=r.user_profiles?.settings;return{...e,user:{...e.user,id:r.id,role:a.role??"user"}}}}},{handlers:d,auth:u,signIn:l,signOut:c}=(0,a.Ay)(o)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22333:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});var a=r(49068);r(77048);var s=r(43806),i=r(68941);async function n(e,t,r){if(!e)throw Error("User ID is required");try{let a=await (0,s.V)(e),n=a?.settings?.enableAutoOrderPrefill??!1,o=a?.settings?.defaultIBAccountId;if(!n||!o)return[];let d=t.map(t=>({user_id:e,account_id:o,order_type:"MARKET",action:"BUY",ticker:t.symbol,quantity:1,price:t.marketQuote.price})),u=r.map(t=>({user_id:e,account_id:o,order_type:"MARKET",action:"SELL",ticker:t.symbol,quantity:1,price:t.marketQuote.price})),l=[...d,...u];return await i.A.autoOrderPrefill.createMany({data:l})}catch(e){throw console.error("[CREATE_AUTO_ORDER_PREFILL_ERROR]",e),Error(e instanceof Error?e.message:"Failed to create auto order prefill record")}}(0,r(84672).D)([n]),(0,a.A)(n,"705b4adc5d4233fa16130f30f56b1e17fe8d25b9cd",null)},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31558:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{patchFetch:()=>eU,routeModule:()=>eL,serverHooks:()=>eK,workAsyncStorage:()=>ez,workUnitAsyncStorage:()=>eV});var s,i,n,o,d={};r.r(d),r.d(d,{POST:()=>eq});var u=r(96849),l=r(59246),c=r(27109),h=r(87728),p=r(13317),m=r(32310);!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),a={};for(let e of r)a[e]=t[e];return e.objectValues(a)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(i||(i={})).mergeShapes=(e,t)=>({...e,...t});let f=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),g=e=>{switch(typeof e){case"undefined":return f.undefined;case"string":return f.string;case"number":return Number.isNaN(e)?f.nan:f.number;case"boolean":return f.boolean;case"function":return f.function;case"bigint":return f.bigint;case"symbol":return f.symbol;case"object":if(Array.isArray(e))return f.array;if(null===e)return f.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return f.promise;if("undefined"!=typeof Map&&e instanceof Map)return f.map;if("undefined"!=typeof Set&&e instanceof Set)return f.set;if("undefined"!=typeof Date&&e instanceof Date)return f.date;return f.object;default:return f.unknown}},_=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class y extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},a=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(a);else if("invalid_return_type"===s.code)a(s.returnTypeError);else if("invalid_arguments"===s.code)a(s.argumentsError);else if(0===s.path.length)r._errors.push(t(s));else{let e=r,a=0;for(;a<s.path.length;){let r=s.path[a];a===s.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(s))):e[r]=e[r]||{_errors:[]},e=e[r],a++}}};return a(this),r}static assert(e){if(!(e instanceof y))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):r.push(e(a));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}y.create=e=>new y(e);let v=(e,t)=>{let r;switch(e.code){case _.invalid_type:r=e.received===f.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case _.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case _.unrecognized_keys:r=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case _.invalid_union:r="Invalid input";break;case _.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case _.invalid_enum_value:r=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case _.invalid_arguments:r="Invalid function arguments";break;case _.invalid_return_type:r="Invalid function return type";break;case _.invalid_date:r="Invalid date";break;case _.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case _.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case _.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case _.custom:r="Invalid input";break;case _.invalid_intersection_types:r="Intersection results could not be merged";break;case _.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case _.not_finite:r="Number must be finite";break;default:r=t.defaultError,s.assertNever(e)}return{message:r}},k=e=>{let{data:t,path:r,errorMaps:a,issueData:s}=e,i=[...r,...s.path||[]],n={...s,path:i};if(void 0!==s.message)return{...s,path:i,message:s.message};let o="";for(let e of a.filter(e=>!!e).slice().reverse())o=e(n,{data:t,defaultError:o}).message;return{...s,path:i,message:o}};function b(e,t){let r=k({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,v,v==v?void 0:v].filter(e=>!!e)});e.common.issues.push(r)}class x{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let a of t){if("aborted"===a.status)return w;"dirty"===a.status&&e.dirty(),r.push(a.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,a=await e.value;r.push({key:t,value:a})}return x.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let a of t){let{key:t,value:s}=a;if("aborted"===t.status||"aborted"===s.status)return w;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||a.alwaysSet)&&(r[t.value]=s.value)}return{status:e.value,value:r}}}let w=Object.freeze({status:"aborted"}),T=e=>({status:"dirty",value:e}),C=e=>({status:"valid",value:e}),A=e=>"aborted"===e.status,O=e=>"dirty"===e.status,N=e=>"valid"===e.status,Z=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(n||(n={}));class I{constructor(e,t,r,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let S=(e,t)=>{if(N(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new y(e.common.issues);return this._error=t,this._error}}};function E(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:a,description:s}=e;if(t&&(r||a))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??s.defaultError}:void 0===s.data?{message:i??a??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:i??r??s.defaultError}},description:s}}class R{get description(){return this._def.description}_getType(e){return g(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:g(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new x,ctx:{common:e.parent.common,data:e.data,parsedType:g(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(Z(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:g(e)},a=this._parseSync({data:e,path:r.path,parent:r});return S(r,a)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:g(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return N(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>N(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:g(e)},a=this._parse({data:e,path:r.path,parent:r});return S(r,await (Z(a)?a:Promise.resolve(a)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,a)=>{let s=e(t),i=()=>a.addIssue({code:_.custom,...r(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(i(),!1)):!!s||(i(),!1)})}refinement(e,t){return this._refinement((r,a)=>!!e(r)||(a.addIssue("function"==typeof t?t(r,a):t),!1))}_refinement(e){return new eA({schema:this,typeName:o.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eO.create(this,this._def)}nullable(){return eN.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eu.create(this)}promise(){return eC.create(this,this._def)}or(e){return ec.create([this,e],this._def)}and(e){return em.create(this,e,this._def)}transform(e){return new eA({...E(this._def),schema:this,typeName:o.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eZ({...E(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:o.ZodDefault})}brand(){return new eE({typeName:o.ZodBranded,type:this,...E(this._def)})}catch(e){return new eI({...E(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:o.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eR.create(this,e)}readonly(){return ej.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let j=/^c[^\s-]{8,}$/i,P=/^[0-9a-z]+$/,M=/^[0-9A-HJKMNP-TV-Z]{26}$/i,$=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,D=/^[a-z0-9_-]{21}$/i,F=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,q=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,L=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,z=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,V=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,K=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,U=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,B=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,W=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,J="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",X=RegExp(`^${J}$`);function Y(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class H extends R{_parse(e){var t,r,i,n;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==f.string){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.string,received:t.parsedType}),w}let d=new x;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(b(o=this._getOrReturnCtx(e,o),{code:_.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),d.dirty());else if("max"===u.kind)e.data.length>u.value&&(b(o=this._getOrReturnCtx(e,o),{code:_.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),d.dirty());else if("length"===u.kind){let t=e.data.length>u.value,r=e.data.length<u.value;(t||r)&&(o=this._getOrReturnCtx(e,o),t?b(o,{code:_.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):r&&b(o,{code:_.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),d.dirty())}else if("email"===u.kind)L.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"email",code:_.invalid_string,message:u.message}),d.dirty());else if("emoji"===u.kind)a||(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:_.invalid_string,message:u.message}),d.dirty());else if("uuid"===u.kind)$.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:_.invalid_string,message:u.message}),d.dirty());else if("nanoid"===u.kind)D.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:_.invalid_string,message:u.message}),d.dirty());else if("cuid"===u.kind)j.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:_.invalid_string,message:u.message}),d.dirty());else if("cuid2"===u.kind)P.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:_.invalid_string,message:u.message}),d.dirty());else if("ulid"===u.kind)M.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:_.invalid_string,message:u.message}),d.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{b(o=this._getOrReturnCtx(e,o),{validation:"url",code:_.invalid_string,message:u.message}),d.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"regex",code:_.invalid_string,message:u.message}),d.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(b(o=this._getOrReturnCtx(e,o),{code:_.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),d.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(b(o=this._getOrReturnCtx(e,o),{code:_.invalid_string,validation:{startsWith:u.value},message:u.message}),d.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(b(o=this._getOrReturnCtx(e,o),{code:_.invalid_string,validation:{endsWith:u.value},message:u.message}),d.dirty()):"datetime"===u.kind?(function(e){let t=`${J}T${Y(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(u).test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:_.invalid_string,validation:"datetime",message:u.message}),d.dirty()):"date"===u.kind?X.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:_.invalid_string,validation:"date",message:u.message}),d.dirty()):"time"===u.kind?RegExp(`^${Y(u)}$`).test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:_.invalid_string,validation:"time",message:u.message}),d.dirty()):"duration"===u.kind?q.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"duration",code:_.invalid_string,message:u.message}),d.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(r=u.version)||!r)&&z.test(t)||("v6"===r||!r)&&K.test(t))&&1&&(b(o=this._getOrReturnCtx(e,o),{validation:"ip",code:_.invalid_string,message:u.message}),d.dirty())):"jwt"===u.kind?!function(e,t){if(!F.test(e))return!1;try{let[r]=e.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(a));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(b(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:_.invalid_string,message:u.message}),d.dirty()):"cidr"===u.kind?(i=e.data,!(("v4"===(n=u.version)||!n)&&V.test(i)||("v6"===n||!n)&&U.test(i))&&1&&(b(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:_.invalid_string,message:u.message}),d.dirty())):"base64"===u.kind?B.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"base64",code:_.invalid_string,message:u.message}),d.dirty()):"base64url"===u.kind?W.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:_.invalid_string,message:u.message}),d.dirty()):s.assertNever(u);return{status:d.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:_.invalid_string,...n.errToObj(r)})}_addCheck(e){return new H({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...n.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...n.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...n.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...n.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errToObj(t)})}nonempty(e){return this.min(1,n.errToObj(e))}trim(){return new H({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new H({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new H({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}H.create=e=>new H({checks:[],typeName:o.ZodString,coerce:e?.coerce??!1,...E(e)});class G extends R{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==f.number){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.number,received:t.parsedType}),w}let r=new x;for(let a of this._def.checks)"int"===a.kind?s.isInteger(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:_.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:_.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:_.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"multipleOf"===a.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,s=r>a?r:a;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:_.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:_.not_finite,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,a){return new G({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(a)}]})}_addCheck(e){return new G({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}G.create=e=>new G({checks:[],typeName:o.ZodNumber,coerce:e?.coerce||!1,...E(e)});class Q extends R{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==f.bigint)return this._getInvalidInput(e);let r=new x;for(let a of this._def.checks)"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:_.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:_.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(b(t=this._getOrReturnCtx(e,t),{code:_.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.bigint,received:t.parsedType}),w}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,a){return new Q({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(a)}]})}_addCheck(e){return new Q({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}Q.create=e=>new Q({checks:[],typeName:o.ZodBigInt,coerce:e?.coerce??!1,...E(e)});class ee extends R{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==f.boolean){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.boolean,received:t.parsedType}),w}return C(e.data)}}ee.create=e=>new ee({typeName:o.ZodBoolean,coerce:e?.coerce||!1,...E(e)});class et extends R{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==f.date){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.date,received:t.parsedType}),w}if(Number.isNaN(e.data.getTime()))return b(this._getOrReturnCtx(e),{code:_.invalid_date}),w;let r=new x;for(let a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(b(t=this._getOrReturnCtx(e,t),{code:_.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(b(t=this._getOrReturnCtx(e,t),{code:_.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):s.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new et({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}et.create=e=>new et({checks:[],coerce:e?.coerce||!1,typeName:o.ZodDate,...E(e)});class er extends R{_parse(e){if(this._getType(e)!==f.symbol){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.symbol,received:t.parsedType}),w}return C(e.data)}}er.create=e=>new er({typeName:o.ZodSymbol,...E(e)});class ea extends R{_parse(e){if(this._getType(e)!==f.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.undefined,received:t.parsedType}),w}return C(e.data)}}ea.create=e=>new ea({typeName:o.ZodUndefined,...E(e)});class es extends R{_parse(e){if(this._getType(e)!==f.null){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.null,received:t.parsedType}),w}return C(e.data)}}es.create=e=>new es({typeName:o.ZodNull,...E(e)});class ei extends R{constructor(){super(...arguments),this._any=!0}_parse(e){return C(e.data)}}ei.create=e=>new ei({typeName:o.ZodAny,...E(e)});class en extends R{constructor(){super(...arguments),this._unknown=!0}_parse(e){return C(e.data)}}en.create=e=>new en({typeName:o.ZodUnknown,...E(e)});class eo extends R{_parse(e){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.never,received:t.parsedType}),w}}eo.create=e=>new eo({typeName:o.ZodNever,...E(e)});class ed extends R{_parse(e){if(this._getType(e)!==f.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.void,received:t.parsedType}),w}return C(e.data)}}ed.create=e=>new ed({typeName:o.ZodVoid,...E(e)});class eu extends R{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==f.array)return b(t,{code:_.invalid_type,expected:f.array,received:t.parsedType}),w;if(null!==a.exactLength){let e=t.data.length>a.exactLength.value,s=t.data.length<a.exactLength.value;(e||s)&&(b(t,{code:e?_.too_big:_.too_small,minimum:s?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(b(t,{code:_.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(b(t,{code:_.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>a.type._parseAsync(new I(t,e,t.path,r)))).then(e=>x.mergeArray(r,e));let s=[...t.data].map((e,r)=>a.type._parseSync(new I(t,e,t.path,r)));return x.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new eu({...this._def,minLength:{value:e,message:n.toString(t)}})}max(e,t){return new eu({...this._def,maxLength:{value:e,message:n.toString(t)}})}length(e,t){return new eu({...this._def,exactLength:{value:e,message:n.toString(t)}})}nonempty(e){return this.min(1,e)}}eu.create=(e,t)=>new eu({type:e,minLength:null,maxLength:null,exactLength:null,typeName:o.ZodArray,...E(t)});class el extends R{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==f.object){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.object,received:t.parsedType}),w}let{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof eo&&"strip"===this._def.unknownKeys))for(let e in r.data)s.includes(e)||i.push(e);let n=[];for(let e of s){let t=a[e],s=r.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new I(r,s,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof eo){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)i.length>0&&(b(r,{code:_.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let a=r.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new I(r,a,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let r=await t.key,a=await t.value;e.push({key:r,value:a,alwaysSet:t.alwaysSet})}return e}).then(e=>x.mergeObjectSync(t,e)):x.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return n.errToObj,new el({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let a=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:n.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new el({...this._def,unknownKeys:"strip"})}passthrough(){return new el({...this._def,unknownKeys:"passthrough"})}extend(e){return new el({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new el({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:o.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new el({...this._def,catchall:e})}pick(e){let t={};for(let r of s.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new el({...this._def,shape:()=>t})}omit(e){let t={};for(let r of s.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new el({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof el){let r={};for(let a in t.shape){let s=t.shape[a];r[a]=eO.create(e(s))}return new el({...t._def,shape:()=>r})}if(t instanceof eu)return new eu({...t._def,type:e(t.element)});if(t instanceof eO)return eO.create(e(t.unwrap()));if(t instanceof eN)return eN.create(e(t.unwrap()));if(t instanceof ef)return ef.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of s.objectKeys(this.shape)){let a=this.shape[r];e&&!e[r]?t[r]=a:t[r]=a.optional()}return new el({...this._def,shape:()=>t})}required(e){let t={};for(let r of s.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof eO;)e=e._def.innerType;t[r]=e}return new el({...this._def,shape:()=>t})}keyof(){return ex(s.objectKeys(this.shape))}}el.create=(e,t)=>new el({shape:()=>e,unknownKeys:"strip",catchall:eo.create(),typeName:o.ZodObject,...E(t)}),el.strictCreate=(e,t)=>new el({shape:()=>e,unknownKeys:"strict",catchall:eo.create(),typeName:o.ZodObject,...E(t)}),el.lazycreate=(e,t)=>new el({shape:e,unknownKeys:"strip",catchall:eo.create(),typeName:o.ZodObject,...E(t)});class ec extends R{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new y(e.ctx.common.issues));return b(t,{code:_.invalid_union,unionErrors:r}),w});{let e,a=[];for(let s of r){let r={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:r});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:r}),r.common.issues.length&&a.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=a.map(e=>new y(e));return b(t,{code:_.invalid_union,unionErrors:s}),w}}get options(){return this._def.options}}ec.create=(e,t)=>new ec({options:e,typeName:o.ZodUnion,...E(t)});let eh=e=>{if(e instanceof ek)return eh(e.schema);if(e instanceof eA)return eh(e.innerType());if(e instanceof eb)return[e.value];if(e instanceof ew)return e.options;if(e instanceof eT)return s.objectValues(e.enum);else if(e instanceof eZ)return eh(e._def.innerType);else if(e instanceof ea)return[void 0];else if(e instanceof es)return[null];else if(e instanceof eO)return[void 0,...eh(e.unwrap())];else if(e instanceof eN)return[null,...eh(e.unwrap())];else if(e instanceof eE)return eh(e.unwrap());else if(e instanceof ej)return eh(e.unwrap());else if(e instanceof eI)return eh(e._def.innerType);else return[]};class ep extends R{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.object)return b(t,{code:_.invalid_type,expected:f.object,received:t.parsedType}),w;let r=this.discriminator,a=t.data[r],s=this.optionsMap.get(a);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(b(t,{code:_.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),w)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let a=new Map;for(let r of t){let t=eh(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(a.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);a.set(s,r)}}return new ep({typeName:o.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...E(r)})}}class em extends R{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=(e,a)=>{if(A(e)||A(a))return w;let i=function e(t,r){let a=g(t),i=g(r);if(t===r)return{valid:!0,data:t};if(a===f.object&&i===f.object){let a=s.objectKeys(r),i=s.objectKeys(t).filter(e=>-1!==a.indexOf(e)),n={...t,...r};for(let a of i){let s=e(t[a],r[a]);if(!s.valid)return{valid:!1};n[a]=s.data}return{valid:!0,data:n}}if(a===f.array&&i===f.array){if(t.length!==r.length)return{valid:!1};let a=[];for(let s=0;s<t.length;s++){let i=e(t[s],r[s]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}if(a===f.date&&i===f.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,a.value);return i.valid?((O(e)||O(a))&&t.dirty(),{status:t.value,value:i.data}):(b(r,{code:_.invalid_intersection_types}),w)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>a(e,t)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}em.create=(e,t,r)=>new em({left:e,right:t,typeName:o.ZodIntersection,...E(r)});class ef extends R{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==f.array)return b(r,{code:_.invalid_type,expected:f.array,received:r.parsedType}),w;if(r.data.length<this._def.items.length)return b(r,{code:_.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),w;!this._def.rest&&r.data.length>this._def.items.length&&(b(r,{code:_.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let a=[...r.data].map((e,t)=>{let a=this._def.items[t]||this._def.rest;return a?a._parse(new I(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(a).then(e=>x.mergeArray(t,e)):x.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new ef({...this._def,rest:e})}}ef.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ef({items:e,typeName:o.ZodTuple,rest:null,...E(t)})};class eg extends R{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==f.object)return b(r,{code:_.invalid_type,expected:f.object,received:r.parsedType}),w;let a=[],s=this._def.keyType,i=this._def.valueType;for(let e in r.data)a.push({key:s._parse(new I(r,e,r.path,e)),value:i._parse(new I(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?x.mergeObjectAsync(t,a):x.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new eg(t instanceof R?{keyType:e,valueType:t,typeName:o.ZodRecord,...E(r)}:{keyType:H.create(),valueType:e,typeName:o.ZodRecord,...E(t)})}}class e_ extends R{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==f.map)return b(r,{code:_.invalid_type,expected:f.map,received:r.parsedType}),w;let a=this._def.keyType,s=this._def.valueType,i=[...r.data.entries()].map(([e,t],i)=>({key:a._parse(new I(r,e,r.path,[i,"key"])),value:s._parse(new I(r,t,r.path,[i,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of i){let a=await r.key,s=await r.value;if("aborted"===a.status||"aborted"===s.status)return w;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of i){let a=r.key,s=r.value;if("aborted"===a.status||"aborted"===s.status)return w;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}}}}e_.create=(e,t,r)=>new e_({valueType:t,keyType:e,typeName:o.ZodMap,...E(r)});class ey extends R{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==f.set)return b(r,{code:_.invalid_type,expected:f.set,received:r.parsedType}),w;let a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&(b(r,{code:_.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&(b(r,{code:_.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());let s=this._def.valueType;function i(e){let r=new Set;for(let a of e){if("aborted"===a.status)return w;"dirty"===a.status&&t.dirty(),r.add(a.value)}return{status:t.value,value:r}}let n=[...r.data.values()].map((e,t)=>s._parse(new I(r,e,r.path,t)));return r.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new ey({...this._def,minSize:{value:e,message:n.toString(t)}})}max(e,t){return new ey({...this._def,maxSize:{value:e,message:n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ey.create=(e,t)=>new ey({valueType:e,minSize:null,maxSize:null,typeName:o.ZodSet,...E(t)});class ev extends R{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.function)return b(t,{code:_.invalid_type,expected:f.function,received:t.parsedType}),w;function r(e,r){return k({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,v,v].filter(e=>!!e),issueData:{code:_.invalid_arguments,argumentsError:r}})}function a(e,r){return k({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,v,v].filter(e=>!!e),issueData:{code:_.invalid_return_type,returnTypeError:r}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof eC){let e=this;return C(async function(...t){let n=new y([]),o=await e._def.args.parseAsync(t,s).catch(e=>{throw n.addIssue(r(t,e)),n}),d=await Reflect.apply(i,this,o);return await e._def.returns._def.type.parseAsync(d,s).catch(e=>{throw n.addIssue(a(d,e)),n})})}{let e=this;return C(function(...t){let n=e._def.args.safeParse(t,s);if(!n.success)throw new y([r(t,n.error)]);let o=Reflect.apply(i,this,n.data),d=e._def.returns.safeParse(o,s);if(!d.success)throw new y([a(o,d.error)]);return d.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ev({...this._def,args:ef.create(e).rest(en.create())})}returns(e){return new ev({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new ev({args:e||ef.create([]).rest(en.create()),returns:t||en.create(),typeName:o.ZodFunction,...E(r)})}}class ek extends R{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ek.create=(e,t)=>new ek({getter:e,typeName:o.ZodLazy,...E(t)});class eb extends R{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return b(t,{received:t.data,code:_.invalid_literal,expected:this._def.value}),w}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ex(e,t){return new ew({values:e,typeName:o.ZodEnum,...E(t)})}eb.create=(e,t)=>new eb({value:e,typeName:o.ZodLiteral,...E(t)});class ew extends R{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return b(t,{expected:s.joinValues(r),received:t.parsedType,code:_.invalid_type}),w}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return b(t,{received:t.data,code:_.invalid_enum_value,options:r}),w}return C(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ew.create(e,{...this._def,...t})}exclude(e,t=this._def){return ew.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ew.create=ex;class eT extends R{_parse(e){let t=s.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==f.string&&r.parsedType!==f.number){let e=s.objectValues(t);return b(r,{expected:s.joinValues(e),received:r.parsedType,code:_.invalid_type}),w}if(this._cache||(this._cache=new Set(s.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=s.objectValues(t);return b(r,{received:r.data,code:_.invalid_enum_value,options:e}),w}return C(e.data)}get enum(){return this._def.values}}eT.create=(e,t)=>new eT({values:e,typeName:o.ZodNativeEnum,...E(t)});class eC extends R{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==f.promise&&!1===t.common.async?(b(t,{code:_.invalid_type,expected:f.promise,received:t.parsedType}),w):C((t.parsedType===f.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eC.create=(e,t)=>new eC({type:e,typeName:o.ZodPromise,...E(t)});class eA extends R{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===o.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null,i={addIssue:e=>{b(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===a.type){let e=a.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return w;let a=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===a.status?w:"dirty"===a.status||"dirty"===t.value?T(a.value):a});{if("aborted"===t.value)return w;let a=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===a.status?w:"dirty"===a.status||"dirty"===t.value?T(a.value):a}}if("refinement"===a.type){let e=e=>{let t=a.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?w:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===a.status?w:("dirty"===a.status&&t.dirty(),e(a.value),{status:t.value,value:a.value})}}if("transform"===a.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>N(e)?Promise.resolve(a.transform(e.value,i)).then(e=>({status:t.value,value:e})):w);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!N(e))return w;let s=a.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}s.assertNever(a)}}eA.create=(e,t,r)=>new eA({schema:e,typeName:o.ZodEffects,effect:t,...E(r)}),eA.createWithPreprocess=(e,t,r)=>new eA({schema:t,effect:{type:"preprocess",transform:e},typeName:o.ZodEffects,...E(r)});class eO extends R{_parse(e){return this._getType(e)===f.undefined?C(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eO.create=(e,t)=>new eO({innerType:e,typeName:o.ZodOptional,...E(t)});class eN extends R{_parse(e){return this._getType(e)===f.null?C(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eN.create=(e,t)=>new eN({innerType:e,typeName:o.ZodNullable,...E(t)});class eZ extends R{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===f.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eZ.create=(e,t)=>new eZ({innerType:e,typeName:o.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...E(t)});class eI extends R{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return Z(a)?a.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new y(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new y(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}eI.create=(e,t)=>new eI({innerType:e,typeName:o.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...E(t)});class eS extends R{_parse(e){if(this._getType(e)!==f.nan){let t=this._getOrReturnCtx(e);return b(t,{code:_.invalid_type,expected:f.nan,received:t.parsedType}),w}return{status:"valid",value:e.data}}}eS.create=e=>new eS({typeName:o.ZodNaN,...E(e)}),Symbol("zod_brand");class eE extends R{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class eR extends R{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?w:"dirty"===e.status?(t.dirty(),T(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?w:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new eR({in:e,out:t,typeName:o.ZodPipeline})}}class ej extends R{_parse(e){let t=this._def.innerType._parse(e),r=e=>(N(e)&&(e.value=Object.freeze(e.value)),e);return Z(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}ej.create=(e,t)=>new ej({innerType:e,typeName:o.ZodReadonly,...E(t)}),el.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(o||(o={}));let eP=H.create;G.create,eS.create,Q.create,ee.create,et.create,er.create,ea.create,es.create,ei.create,en.create,eo.create,ed.create;let eM=eu.create,e$=el.create;el.strictCreate,ec.create,ep.create,em.create,ef.create,eg.create,e_.create,ey.create,ev.create,ek.create,eb.create,ew.create,eT.create,eC.create,eA.create,eO.create,eN.create,eA.createWithPreprocess,eR.create;var eD=r(1274);let eF=e$({changes:eM(e$({symbol:eP(),previousTrend:eP(),newTrend:eP(),date:eP()})),userId:eP()});async function eq(e){try{if(e.headers.get("x-api-key")!==process.env.INTERNAL_API_KEY){let e=await (0,p.j2)();if(!e?.user?.id)return h.NextResponse.json({error:"Unauthorized"},{status:401})}let t=await e.json();try{eF.parse(t)}catch(e){return console.error("Validation error:",e),h.NextResponse.json({error:"Invalid request body"},{status:400})}let{changes:r,userId:a}=t;if(!r?.length||!a)return h.NextResponse.json({error:"Missing required fields"},{status:400});let s=await (0,m.R)(a);if(!s)return h.NextResponse.json({error:"Failed to fetch holdings"},{status:500});let i=r.filter(e=>s.some(t=>t.symbol===e.symbol)).filter(e=>"bearish"===e.newTrend.toLowerCase()),n=r.filter(e=>"bullish"===e.newTrend.toLowerCase()&&!s.some(t=>t.symbol===e.symbol)),o=await (0,eD._)(n,i,a);return h.NextResponse.json(o,{status:200})}catch(e){return console.error("[AUTO_ORDER_ERROR]",e),h.NextResponse.json({error:"Internal Server Error"},{status:500})}}let eL=new u.AppRouteRouteModule({definition:{kind:l.RouteKind.APP_ROUTE,page:"/api/order/auto-order/route",pathname:"/api/order/auto-order",filename:"route",bundlePath:"app/api/order/auto-order/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/order/auto-order/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:ez,workUnitAsyncStorage:eV,serverHooks:eK}=eL;function eU(){return(0,c.patchFetch)({workAsyncStorage:ez,workUnitAsyncStorage:eV})}},32310:(e,t,r)=>{"use strict";r.d(t,{R:()=>d});var a=r(49068);r(77048);var s=r(76977),i=r(55830),n=r(91581),o=r(80322);async function d(e){try{if(!await (0,o.F)(e))return console.log("IBKR not connected"),null;let t=await (0,i.M)(e),r=[],a=new Promise((e,a)=>{let i=setTimeout(()=>{t.disconnect(),a(Error("Timeout waiting for positions data"))},1e4);t.on(s.EventName.error,(e,t,r)=>{console.error(`IBKR Error: ${e.message} - code: ${t} - reqId: ${r}`),502===t&&a(Error("Connection refused"))}),t.on(s.EventName.connected,()=>{console.log("IBKR Connected successfully"),t.reqPositions()}),t.on(s.EventName.position,(e,t,a,s)=>{console.log("Received position:",t.symbol,a),t.symbol&&r.push({symbol:t.symbol,shares:a,avgCost:s})}),t.on(s.EventName.positionEnd,async()=>{console.log("Position end received. Total IBKR positions:",r.length);try{if(0===r.length){console.log("No positions found"),clearTimeout(i),t.disconnect(),e([]);return}let a=r.map(e=>e.symbol);console.log("Fetching quotes for symbols:",a);let s=await (0,n.gO)(a);console.log("Received quotes for",Object.keys(s).length,"symbols");let o=r.map(e=>{let t=s[e.symbol];if(!t)return console.warn(`No quote found for symbol: ${e.symbol}`),null;let r=t.regularMarketPrice,a=e.shares*r,i=e.shares*e.avgCost;return{symbol:e.symbol,shares:e.shares,price:r,value:a,avgCost:e.avgCost,return:(a-i)/i*100}}).filter(e=>null!==e);console.log("All positions processed. Total valid positions:",o.length),clearTimeout(i),t.disconnect(),e(o)}catch(e){console.error("Error processing positions:",e),a(e)}}),t.on(s.EventName.disconnected,()=>{console.log("IBKR Disconnected")}),console.log("Initiating IBKR connection..."),t.connect()}),d=await a;return console.log("Retrieved positions:",d),d}catch(e){return console.error("Error fetching positions data:",e),null}}(0,r(84672).D)([d]),(0,a.A)(d,"40130103a12210564b9cc2eb00ee5bfd15ed9e9469",null)},33873:e=>{"use strict";e.exports=require("path")},43806:(e,t,r)=>{"use strict";r.d(t,{V:()=>i,e:()=>n});var a=r(49068);r(77048);var s=r(68941);async function i(e){let t=await s.A.userProfile.findUnique({where:{user_id:e}});return t?{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}:null}async function n(e,t){let r=await s.A.userProfile.update({where:{user_id:e},data:{first_name:t.firstName,last_name:t.lastName,settings:JSON.parse(JSON.stringify(t.settings)),updated_at:new Date}});return{userId:r.user_id,firstName:r.first_name||"",lastName:r.last_name||"",createdAt:r.created_at,updatedAt:r.updated_at,deletedAt:r.deleted_at,settings:r.settings}}(0,r(84672).D)([i,n]),(0,a.A)(i,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,a.A)(n,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55830:(e,t,r)=>{"use strict";r.d(t,{M:()=>i,f:()=>n});var a=r(43806),s=r(76977);async function i(e){try{let t=await (0,a.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return new s.IBApi({clientId:r,host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port})}catch(e){throw console.error("Error creating IBKR connection:",e),e}}async function n(e){try{let t=await (0,a.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=new s.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port}),i=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return{ibApiNext:r,clientId:i}}catch(e){throw console.error("Error creating IBKR connection:",e),e}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(67566);let s=globalThis.__prisma||new a.PrismaClient},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80322:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var a=r(49068);r(77048);var s=r(55830);async function i(e){try{let{ibApiNext:t,clientId:r}=await (0,s.f)(e);return new Promise((e,r)=>{let a=setTimeout(()=>r(Error("Connection timeout")),3e3);t.connect().getAccountSummary("All","NetLiquidation").subscribe({next:()=>{clearTimeout(a),e(!0),t.disconnect()},error:e=>{clearTimeout(a),r(e),t.disconnect()}}).add(()=>{t.isConnected&&(t.disconnect(),console.log("Disconnected from IBKR"))})})}catch(e){return console.error("Error testing IBKR connection:",e),!1}}(0,r(84672).D)([i]),(0,a.A)(i,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",null)},86641:()=>{},87313:()=>{},89689:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"40130103a12210564b9cc2eb00ee5bfd15ed9e9469":()=>a.R,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c":()=>i.F,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665":()=>s.V,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e":()=>s.e,"705b4adc5d4233fa16130f30f56b1e17fe8d25b9cd":()=>n.k});var a=r(32310),s=r(43806),i=r(80322),n=r(22333)},91581:(e,t,r)=>{"use strict";r.d(t,{TZ:()=>n,fR:()=>d,gO:()=>o});var a=r(91773),s=r(47524);s.A.suppressNotices(["yahooSurvey"]);let i={SPX:"^GSPC",VIX:"^VIX",DJI:"^DJI",IXIC:"^IXIC"};async function n(e,t=2){(0,a.unstable_noStore)();try{let r=i[e]||e;for(let a=0;a<=t;a++)try{a>0&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,a)));let e=await s.A.quote(r,{fields:["regularMarketPrice","regularMarketChange","regularMarketChangePercent","regularMarketTime"]});if(e&&"number"==typeof e.regularMarketPrice)return{regularMarketPrice:e.regularMarketPrice,regularMarketChange:e.regularMarketChange??0,regularMarketChangePercent:e.regularMarketChangePercent??0,regularMarketTime:new Date(e.regularMarketTime??Date.now())};throw Error("Invalid response format")}catch(r){if(console.warn(`Attempt ${a+1} failed for ${e}:`,r),a===t)throw r}throw Error("All retry attempts failed")}catch(t){return console.error(`Failed to fetch quote for ${e}:`,t),{regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date}}}async function o(e,t=2){(0,a.unstable_noStore)();try{let r=e.map(e=>i[e]||e);for(let a=0;a<=t;a++)try{let t=await s.A.quote(r,{fields:["regularMarketPrice","regularMarketChange","regularMarketChangePercent","regularMarketTime"]}),a={};return(Array.isArray(t)?t:[t]).forEach((t,r)=>{let s=e[r];s&&(t&&"number"==typeof t.regularMarketPrice?a[s]={regularMarketPrice:t.regularMarketPrice,regularMarketChange:t.regularMarketChange??0,regularMarketChangePercent:t.regularMarketChangePercent??0,regularMarketTime:new Date(t.regularMarketTime??Date.now())}:a[s]={regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date})}),a}catch(e){if(a===t)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("All retry attempts failed")}catch(t){return console.error(`Failed to fetch quotes for ${e.join(", ")}:`,t),e.reduce((e,t)=>(e[t]={regularMarketPrice:0,regularMarketChange:0,regularMarketChangePercent:0,regularMarketTime:new Date},e),{})}}function d(){s.A.setGlobalConfig({queue:{concurrency:2,timeout:5e3},validation:{logErrors:!0}})}},91645:e=>{"use strict";e.exports=require("net")},96849:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,3314],()=>r(31558));module.exports=a})();