(()=>{var e={};e.id=1014,e.ids=[1014],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,r,t)=>{"use strict";t.d(r,{j2:()=>n,Y9:()=>p,Jv:()=>c});var s=t(74723),i=t(89886),u=t(68941),o=t(30935);let a={...{secret:process.env.AUTH_SECRET,providers:[o.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,i.y)(u.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let t=await u.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!t)return e;let s=t.user_profiles?.settings;return{...e,user:{...e.user,id:t.id,role:s.role??"user"}}}}},{handlers:p,auth:n,signIn:c,signOut:d}=(0,s.Ay)(a)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(67566);let i=globalThis.__prisma||new s.PrismaClient},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},83263:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>n,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>a,POST:()=>p});var i=t(96849),u=t(59246),o=t(27109);let{GET:a,POST:p}=t(13317).Y9,n=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:x}=n;function l(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},86641:()=>{},87313:()=>{},96849:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3491,7728,7566,5045,5438],()=>t(83263));module.exports=s})();