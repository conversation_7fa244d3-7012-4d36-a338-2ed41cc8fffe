(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={348:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},1305:(e,t,r)=>{"use strict";r.d(t,{NotificationPreferences:()=>n});let n=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call NotificationPreferences() from the server but NotificationPreferences is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/NotificationPreferences.tsx","NotificationPreferences")},1708:e=>{"use strict";e.exports=require("node:process")},1899:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(81643),i=r(29294),a=r(63033),o=r(65762),s=r(75802),c=r(18435),l=r(50305),u=(r(23710),r(69166));function d(){let e=i.workAsyncStorage.getStore(),t=a.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return f(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=p.get(l);if(n)return n;let i=(0,c.makeHangingPromise)(l.renderSignal,"`headers()`");return p.set(l,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=y(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}}}),i}else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return f((0,a.getExpectedRequestStore)("headers").headers)}let p=new WeakMap;function f(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(y);function y(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3760:(e,t,r)=>{"use strict";let n=r(63033),i=r(29294),a=r(65762),o=r(50305),s=r(75802),c=r(45638);function l(){let e=i.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return u(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return u(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return p(null);default:return t}}function u(e,t){let r,n=d.get(l);return n||(r=p(e),d.set(e,r),r)}let d=new WeakMap;function p(e){let t=new f(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class f{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=i.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,a.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,a.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},8886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>l});var n=r(42585),i=r(59246),a=r(63528),o=r.n(a),s=r(83599),c={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);r.d(t,c);let l={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10763)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9558:(e,t,r)=>{"use strict";r.d(t,{IBKRConnectionForm:()=>n});let n=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call IBKRConnectionForm() from the server but IBKRConnectionForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/IBKRConnectionForm.tsx","IBKRConnectionForm")},10402:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>x,AvatarFallback:()=>S,AvatarImage:()=>E});var n=r(43197),i=r(14824),a=r(6125),o=r(21760),s=r(36392),c=r(2185),l=r(43456);function u(){return()=>{}}var d="Avatar",[p,f]=(0,a.A)(d),[h,m]=p(d),y=i.forwardRef((e,t)=>{let{__scopeAvatar:r,...a}=e,[o,s]=i.useState("idle");return(0,n.jsx)(h,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:s,children:(0,n.jsx)(c.sG.span,{...a,ref:t})})});y.displayName=d;var b="AvatarImage",w=i.forwardRef((e,t)=>{let{__scopeAvatar:r,src:a,onLoadingStatusChange:d=()=>{},...p}=e,f=m(b,r),h=function(e,{referrerPolicy:t,crossOrigin:r}){let n=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),a=i.useRef(null),o=n?(a.current||(a.current=new window.Image),a.current):null,[c,d]=i.useState(()=>_(o,e));return(0,s.N)(()=>{d(_(o,e))},[o,e]),(0,s.N)(()=>{let e=e=>()=>{d(e)};if(!o)return;let n=e("loaded"),i=e("error");return o.addEventListener("load",n),o.addEventListener("error",i),t&&(o.referrerPolicy=t),"string"==typeof r&&(o.crossOrigin=r),()=>{o.removeEventListener("load",n),o.removeEventListener("error",i)}},[o,r,t]),c}(a,p),y=(0,o.c)(e=>{d(e),f.onImageLoadingStatusChange(e)});return(0,s.N)(()=>{"idle"!==h&&y(h)},[h,y]),"loaded"===h?(0,n.jsx)(c.sG.img,{...p,ref:t,src:a}):null});w.displayName=b;var g="AvatarFallback",v=i.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:a,...o}=e,s=m(g,r),[l,u]=i.useState(void 0===a);return i.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>u(!0),a);return()=>window.clearTimeout(e)}},[a]),l&&"loaded"!==s.imageLoadingStatus?(0,n.jsx)(c.sG.span,{...o,ref:t}):null});function _(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}v.displayName=g;var k=r(51001);let x=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(y,{ref:r,className:(0,k.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));x.displayName=y.displayName;let E=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(w,{ref:r,className:(0,k.cn)("aspect-square h-full w-full",e),...t}));E.displayName=w.displayName;let S=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(v,{ref:r,className:(0,k.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));S.displayName=v.displayName},10763:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(52927),i=r(13317),a=r(65334),o=r(76658),s=r(96839),c=r(1305),l=r(9558),u=r(41653);async function d(){let e=await (0,i.j2)();if(!e?.user)return(0,n.jsx)("div",{children:"Please log in to view your profile."});let{user:t}=e;return(0,n.jsx)("div",{className:"min-h-screen bg-slate-50",children:(0,n.jsx)("div",{className:"container max-w-4xl px-4 py-8 md:py-12",children:(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsx)(o.Zp,{className:"p-6 md:p-8",children:(0,n.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:space-x-8",children:[(0,n.jsx)("div",{className:"h-24 w-24 md:h-32 md:w-32 rounded-full overflow-hidden bg-slate-200",children:(0,n.jsxs)(s.Avatar,{className:"h-full w-full object-cover",children:[(0,n.jsx)(s.AvatarImage,{src:t?.image||void 0,alt:t?.name||void 0}),(0,n.jsx)(s.AvatarFallback,{children:t?.name?.[0]})]})}),(0,n.jsxs)("div",{className:"mt-4 md:mt-0 flex-grow",children:[(0,n.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-slate-900",children:t.name}),(0,n.jsx)("p",{className:"text-slate-500 mt-1",children:t.email})]}),(0,n.jsx)(u.UserRoleDisplay,{userId:t.id})]})}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h2",{className:"text-xl md:text-2xl font-semibold text-slate-900 px-1",children:"Trading Account"}),(0,n.jsx)(o.Zp,{className:"p-6 md:p-8",children:(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-base md:text-lg font-medium text-slate-900 mb-4",children:"IBKR Connection Settings"}),(0,n.jsx)(l.IBKRConnectionForm,{userId:t.id})]})})}),(0,n.jsx)(o.Zp,{className:"p-6 md:p-8",children:(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-base md:text-lg font-medium text-slate-900 mb-2",children:"Interactive Brokers Account"}),(0,n.jsx)("div",{className:"bg-slate-50 rounded-lg p-4",children:(0,n.jsx)(a.IBAccountDetails,{})})]})})})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h2",{className:"text-xl md:text-2xl font-semibold text-slate-900 px-1",children:"Notification Settings"}),(0,n.jsx)(o.Zp,{className:"p-6 md:p-8",children:(0,n.jsx)(c.NotificationPreferences,{userId:t.id})})]})]})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12978:(e,t,r)=>{"use strict";r.d(t,{IBAccountDetails:()=>_});var n=r(43197),i=r(14824),a=r(45023),o=r(95311),s=r(89806),c=r(45806);let l=(0,r(44736).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var u=r(77703),d=r(416),p=r(91565),f=r(48763),h=r(20830),m=r(82366),y=r(26070),b=r(37534);r(33449);var w=r(24017);let g=(0,w.createServerReference)("7fefb8e30cab7d78730e8f450e790bc532ceaea9ce",w.callServer,void 0,w.findSourceMapURL,"upsertUserProfileIBAccountSettings");function v(){let{data:e}=(0,f.wV)(),[t,r]=(0,i.useState)(!1),[a,o]=(0,i.useState)([]),[l,u]=(0,i.useState)({defaultIBAccountId:"",enableAutoOrderPrefill:!1}),[d,p]=(0,i.useState)({defaultIBAccountId:"",enableAutoOrderPrefill:!1}),[w,v]=(0,i.useState)(!1),_=async()=>{if(e?.user?.id)try{r(!0),await g({defaultIBAccountId:l.defaultIBAccountId,enableAutoOrderPrefill:l.enableAutoOrderPrefill},e.user.id),p(l),(0,h.oR)({title:"Settings saved",description:"Your IBKR account settings have been updated."})}catch(e){(0,h.oR)({title:"Error",description:"Failed to save settings.",variant:"destructive"})}finally{r(!1)}};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(y.J,{htmlFor:"defaultIBAccountId",children:"Default Account"}),(0,n.jsxs)(b.l6,{value:l.defaultIBAccountId,onValueChange:e=>u({...l,defaultIBAccountId:e}),children:[(0,n.jsx)(b.bq,{className:"w-[240px]",children:(0,n.jsx)(b.yv,{placeholder:"Select default account"})}),(0,n.jsx)(b.gC,{children:a.map(e=>(0,n.jsx)(b.eb,{value:e.accountNumber,children:e.accountNumber},e.accountNumber))})]}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"This account will be used as the default for all trading operations"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,n.jsxs)("div",{className:"space-y-0.5",children:[(0,n.jsx)(y.J,{htmlFor:"autoOrderPrefill",children:"Auto Order Prefill"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically prefill order details based on risk signals"})]}),(0,n.jsx)(m.d,{id:"autoOrderPrefill",checked:l.enableAutoOrderPrefill,onCheckedChange:e=>u({...l,enableAutoOrderPrefill:e})})]})]}),(0,n.jsx)(s.$,{onClick:_,disabled:t||!w,className:"w-full sm:w-auto",children:t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Settings"})]})}function _(){let{data:e}=(0,f.wV)(),[t,r]=(0,i.useState)(!1),[m,y]=(0,i.useState)("idle"),[b,w]=(0,i.useState)(null),[g,_]=(0,i.useState)(!1),k=async()=>{try{r(!0),y("idle");let t=await (0,p.V)(e?.user?.id);if(!t?.settings?.ibkrConnectionDetail)return void(0,h.oR)({title:"Error",description:"Please configure your IBKR connection details first.",variant:"destructive"});let n=await (0,a.F)(e?.user?.id);y(n?"success":"error")}catch(e){y("error"),console.error("Failed to test IBKR connection:",e)}finally{r(!1),_(!1)}};return(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsx)("div",{className:"text-size-base md:text-size-lg space-y-4",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)(s.$,{onClick:k,disabled:t,variant:"outline",className:"w-[200px]",children:t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify IBKR Connection"}),"idle"!==m&&(0,n.jsx)("div",{className:"flex items-center gap-2",children:"success"===m?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l,{className:"h-5 w-5 text-green-600"}),(0,n.jsx)("span",{className:"text-green-600 font-weight-medium",children:"IBKR connection available"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.A,{className:"h-5 w-5 text-red-600"}),(0,n.jsx)("span",{className:"text-red-600 font-weight-medium",children:"Unable to reach IBKR"})]})}),g&&(0,n.jsx)("div",{className:"w-full max-w-md",children:(0,n.jsx)(o.E,{className:"h-[100px] w-full"})}),b&&(0,n.jsx)(d.Zp,{className:"w-full max-w-md p-4",children:(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Account ID:"}),(0,n.jsx)("span",{className:"font-weight-medium",children:b.accountId})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Available Funds:"}),(0,n.jsxs)("span",{className:"font-weight-medium",children:["$",b.availableFunds.toLocaleString()]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Net Liquidation:"}),(0,n.jsxs)("span",{className:"font-weight-medium",children:["$",b.netLiquidation.toLocaleString()]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Total Cash Value:"}),(0,n.jsxs)("span",{className:"font-weight-medium",children:["$",b.totalCashValue.toLocaleString()]})]})]})})]})}),(0,n.jsx)("div",{className:"border-t"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-size-base md:text-size-lg font-weight-medium text-heading",children:"Account Settings"}),(0,n.jsx)(v,{})]}),b&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"border-t"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-size-base md:text-size-lg font-weight-medium text-heading",children:"Account Information"}),(0,n.jsx)(d.Zp,{className:"w-full p-4",children:(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Account ID:"}),(0,n.jsx)("span",{className:"font-weight-medium",children:b.accountId})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Available Funds:"}),(0,n.jsxs)("span",{className:"font-weight-medium",children:["$",b.availableFunds.toLocaleString()]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Net Liquidation:"}),(0,n.jsxs)("span",{className:"font-weight-medium",children:["$",b.netLiquidation.toLocaleString()]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Total Cash Value:"}),(0,n.jsxs)("span",{className:"font-weight-medium",children:["$",b.totalCashValue.toLocaleString()]})]})]})})]})]})]})}},15164:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return l},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(56187),i=r(90773),a=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function s(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=i.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function l(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function u(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15560:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return n.ImageResponse},NextRequest:function(){return i.NextRequest},NextResponse:function(){return a.NextResponse},URLPattern:function(){return s.URLPattern},after:function(){return c.after},connection:function(){return l.connection},unstable_rootParams:function(){return u.unstable_rootParams},userAgent:function(){return o.userAgent},userAgentFromString:function(){return o.userAgentFromString}});let n=r(46377),i=r(77199),a=r(34937),o=r(61241),s=r(92386),c=r(42232),l=r(65379),u=r(27774)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19246:(e,t,r)=>{"use strict";r.d(t,{NotificationPreferences:()=>m});var n=r(43197),i=r(14824),a=r(82366),o=r(44736);let s=(0,o.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),c=(0,o.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),l=(0,o.A)("bell-off",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]]);var u=r(24017);let d=(0,u.createServerReference)("4051fed6790d5a5fb9d91fb6a5ad52ef1f5e8cc96c",u.callServer,void 0,u.findSourceMapURL,"saveSubscription");async function p(){try{if(!("serviceWorker"in navigator)||!("Notification"in window))throw Error("Push notifications are not supported");if("denied"===Notification.permission)throw Error("Notifications are blocked. Please enable them in your browser settings.");let e=await Notification.requestPermission();if("granted"!==e)throw Error("Notification permission was not granted.");let t=await navigator.serviceWorker.register("/service-worker.js",{scope:"/"});await navigator.serviceWorker.ready;let r=await t.pushManager.getSubscription();if(console.log("Existing subscription:",r),!r)try{console.log("Fetching VAPID key...");let e=await fetch("/api/push/vapid-key");if(!e.ok)throw Error(`Failed to fetch VAPID key: ${e.status}`);let n=await e.text();if(console.log("VAPID key received:",n),!n||0===n.length)throw Error("Invalid VAPID key received");let i=function(e){try{let t="=".repeat((4-e.length%4)%4),r=(e+t).replace(/\-/g,"+").replace(/_/g,"/"),n=window.atob(r),i=new Uint8Array(n.length);for(let e=0;e<n.length;++e)i[e]=n.charCodeAt(e);return i}catch(e){throw console.error("Error in urlBase64ToUint8Array:",e),e}}(n);console.log("Creating new subscription...");try{r=await t.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:i}),console.log("New subscription created:",r)}catch(e){if(console.error("Detailed subscription error:",e),e instanceof Error&&"AbortError"===e.name){let e=await t.pushManager.getSubscription();e&&(await e.unsubscribe(),r=await t.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:i}))}else throw e}}catch(e){throw console.error("Error during subscription creation:",e),e}if(!r)throw Error("Failed to create push subscription");try{if(!await d(r))throw Error("Failed to save subscription");return console.log("Subscription saved successfully"),r}catch(e){throw await r.unsubscribe(),e}}catch(e){throw console.error("Error in subscribeToPushNotifications:",e),e}}var f=r(66034),h=r(91213);function m({userId:e}){let[t,r]=(0,i.useState)(!1),[o,u]=(0,i.useState)(!0),[d,m]=(0,i.useState)(null),[y,b]=(0,i.useState)(!1),w=async()=>{try{if(!("Notification"in window))return void m("Notifications are not supported in this browser");let e=Notification.permission;if("granted"===e){let e=await navigator.serviceWorker.ready,t=await e.pushManager.getSubscription();r(!!t)}else r(!1)}catch(e){m("Failed to check notification status"),console.error("Error checking notification status:",e)}finally{u(!1)}},g=async()=>{let e=await navigator.serviceWorker.ready,t=await e.pushManager.getSubscription();if(t)try{await fetch("/api/push/unsubscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({endpoint:t.endpoint})}),await t.unsubscribe(),r(!1)}catch(e){throw Error("Failed to unsubscribe from notifications")}},v=async()=>{try{if(u(!0),m(null),!y)throw Error("Please install Lunar Hedge as a PWA before enabling notifications");t?await g():await p().catch(e=>{throw Error(e instanceof Error?e.message:"Subscription failed")}),await w()}catch(e){m(e instanceof Error?e.message:"Failed to manage notification subscription")}finally{u(!1)}};return o?(0,n.jsxs)("div",{className:"flex items-center justify-center p-4",children:[(0,n.jsx)(h.S,{}),(0,n.jsx)("span",{className:"ml-2",children:"Loading notification preferences..."})]}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"rounded-lg border border-blue-100 bg-blue-50 p-4",children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)(s,{className:"h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-medium text-blue-900",children:"Setting Up Push Notifications"}),(0,n.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,n.jsx)("p",{children:"Before enabling notifications, please ensure:"}),(0,n.jsxs)("ol",{className:"list-decimal ml-4 space-y-1",children:[(0,n.jsx)("li",{children:"You have installed Lunar Hedge as a PWA (Progressive Web App) on your device"}),(0,n.jsx)("li",{children:"Your browser permissions allow notifications for this website"}),(0,n.jsx)("li",{children:"You are using a supported browser (Chrome, Edge, Firefox, or Safari)"})]}),(0,n.jsx)("p",{className:"mt-2",children:"When you toggle notifications on, your browser will prompt you to allow notifications. You must accept this prompt to receive updates."})]}),"denied"===Notification.permission&&(0,n.jsx)("div",{className:"mt-2 text-sm text-red-600",children:"⚠️ Notifications are currently blocked. Please enable them in your browser settings to receive updates."}),!y&&(0,n.jsx)("div",{className:"mt-2 text-sm text-amber-600",children:"⚠️ Please install Lunar Hedge as a PWA to enable notifications. See installation instructions below."})]})]})}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("h3",{className:"text-size-lg font-weight-medium text-heading",children:"Push Notifications"}),(0,n.jsx)("p",{className:"text-size-sm text-muted",children:"Receive updates when risk signals data changes"})]}),(0,n.jsx)(a.d,{checked:t,onCheckedChange:v,disabled:o||!y})]}),d&&(0,n.jsx)(f.Fc,{variant:"destructive",children:(0,n.jsx)(f.TN,{children:d})}),(0,n.jsxs)("div",{className:"bg-muted rounded-lg p-4 space-y-4",children:[(0,n.jsx)("div",{className:"flex items-center space-x-3",children:t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c,{className:"h-5 w-5 text-green-500"}),(0,n.jsx)("span",{className:"text-size-sm text-body",children:"You will receive notifications when risk signals are updated"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l,{className:"h-5 w-5 text-muted"}),(0,n.jsx)("span",{className:"text-size-sm text-muted",children:"Enable notifications to stay updated with risk signal changes"})]})}),(0,n.jsx)("div",{className:"text-size-xs text-muted",children:t?(0,n.jsx)("p",{children:"You can disable notifications at any time by toggling the switch above or through your browser settings."}):(0,n.jsx)("p",{children:"You will need to allow notifications in your browser when prompted after enabling this setting."})})]}),!t&&(0,n.jsxs)("div",{className:"text-size-sm text-body bg-muted rounded-lg p-4",children:[(0,n.jsx)("h4",{className:"font-weight-medium mb-2",children:"How to Install Lunar Hedge:"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("p",{className:"font-weight-medium",children:"On iOS Safari:"}),(0,n.jsxs)("ol",{className:"list-decimal ml-4 space-y-1",children:[(0,n.jsx)("li",{children:"Tap the Share button"}),(0,n.jsx)("li",{children:'Scroll down and tap "Add to Home Screen"'}),(0,n.jsx)("li",{children:'Tap "Add" to confirm'})]}),(0,n.jsx)("p",{className:"font-weight-medium mt-3",children:"On Android Chrome:"}),(0,n.jsxs)("ol",{className:"list-decimal ml-4 space-y-1",children:[(0,n.jsx)("li",{children:"Tap the menu (three dots)"}),(0,n.jsx)("li",{children:'Tap "Add to Home screen"'}),(0,n.jsx)("li",{children:'Tap "Install" to confirm'})]})]})]})]})}},20165:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(18435),i=r(348),a=r(58505),o=r(49005),s=r(65762),c=r(45638);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20878:(e,t,r)=>{"use strict";let n,i,a,o,s,c,l;r.r(t),r.d(t,{"4051fed6790d5a5fb9d91fb6a5ad52ef1f5e8cc96c":()=>ow,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c":()=>b.F,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665":()=>d.V,"40fea484d196ffc03250e6fc7effd806957ac9a456":()=>w.I,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e":()=>d.e,"6072c954a2a9c4416882c333177a9672834f28ac47":()=>m,"7f2cceb82f3528bdce95a7fec1001f3e3f060119b1":()=>y,"7f76ddcec25d3e0083a5eedf540fa563cbecbb815d":()=>g.Gk,"7fa677cfc0582c3f00b593da75a535b6ed3714b542":()=>g.lu,"7fefb8e30cab7d78730e8f450e790bc532ceaea9ce":()=>g.Ph});var u=r(20349);r(71241);var d=r(31595),p=r(80336),f=r(41474),h=r(68785);async function m(e,t){try{return(0,f.revalidatePath)("/profile"),{success:!0}}catch(e){throw console.error("Failed to update user role:",e),Error("Failed to update user role")}}let y=async(e,t)=>{try{let r=await (0,d.V)(t);if(!r)throw Error("User profile not found");let n=JSON.parse(JSON.stringify({...r.settings,role:e.role})),i=await p.A.userProfile.update({where:{user_id:t},data:{settings:n}});return(0,f.revalidatePath)("/profile"),i}catch(e){throw console.error("Error upserting user profile settings",e),e}};(0,h.D)([m,y]),(0,u.A)(m,"6072c954a2a9c4416882c333177a9672834f28ac47",null),(0,u.A)(y,"7f2cceb82f3528bdce95a7fec1001f3e3f060119b1",null);var b=r(69583),w=r(65629),g=r(77418),v=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},_=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function k(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class x{constructor(e,t,r){if(rR.add(this),rP.set(this,{}),rT.set(this,void 0),rj.set(this,void 0),v(this,rj,r,"f"),v(this,rT,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(_(this,rP,"f")[e]=r)}get value(){return Object.keys(_(this,rP,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>_(this,rP,"f")[e]).join("")}chunk(e,t){let r=_(this,rR,"m",rC).call(this);for(let n of _(this,rR,"m",rO).call(this,{name:_(this,rT,"f").name,value:e,options:{..._(this,rT,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(_(this,rR,"m",rC).call(this))}}rP=new WeakMap,rT=new WeakMap,rj=new WeakMap,rR=new WeakSet,rO=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return _(this,rP,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),_(this,rP,"f")[t]=i}return _(this,rj,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rC=function(){let e={};for(let t in _(this,rP,"f"))delete _(this,rP,"f")?.[t],e[t]={name:t,value:"",options:{..._(this,rT,"f").options,maxAge:0}};return e};class E extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class S extends E{}S.kind="signIn";class A extends E{}A.type="AdapterError";class R extends E{}R.type="AccessDenied";class P extends E{}P.type="CallbackRouteError";class T extends E{}T.type="ErrorPageLoop";class j extends E{}j.type="EventError";class O extends E{}O.type="InvalidCallbackUrl";class C extends S{constructor(){super(...arguments),this.code="credentials"}}C.type="CredentialsSignin";class U extends E{}U.type="InvalidEndpoints";class N extends E{}N.type="InvalidCheck";class I extends E{}I.type="JWTSessionError";class $ extends E{}$.type="MissingAdapter";class D extends E{}D.type="MissingAdapterMethods";class H extends E{}H.type="MissingAuthorize";class L extends E{}L.type="MissingSecret";class W extends S{}W.type="OAuthAccountNotLinked";class M extends S{}M.type="OAuthCallbackError";class K extends E{}K.type="OAuthProfileParseError";class q extends E{}q.type="SessionTokenError";class J extends S{}J.type="OAuthSignInError";class B extends S{}B.type="EmailSignInError";class F extends E{}F.type="SignOutError";class z extends E{}z.type="UnknownAction";class V extends E{}V.type="UnsupportedStrategy";class G extends E{}G.type="InvalidProvider";class X extends E{}X.type="UntrustedHost";class Y extends E{}Y.type="Verification";class Z extends S{}Z.type="MissingCSRF";let Q=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class ee extends E{}ee.type="DuplicateConditionalUI";class et extends E{}et.type="MissingWebAuthnAutocomplete";class er extends E{}er.type="WebAuthnVerificationError";class en extends S{}en.type="AccountNotLinked";class ei extends E{}ei.type="ExperimentalFeatureNotEnabled";let ea=!1;function eo(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let es=!1,ec=!1,el=!1,eu=["createVerificationToken","useVerificationToken","getUserByEmail"],ed=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],ep=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var ef=r(55511);let eh=(e,t,r,n,i)=>{let a=parseInt(e.substr(3),10)>>3||20,o=(0,ef.createHmac)(e,r.byteLength?r:new Uint8Array(a)).update(t).digest(),s=Math.ceil(i/a),c=new Uint8Array(a*s+n.byteLength+1),l=0,u=0;for(let t=1;t<=s;t++)c.set(n,u),c[u+n.byteLength]=t,c.set((0,ef.createHmac)(e,o).update(c.subarray(l,u+n.byteLength+1)).digest(),u),l=u,u+=a;return c.slice(0,i)};"function"!=typeof ef.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{ef.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let em=async(e,t,r,i,a)=>(n||eh)(e,t,r,i,a);function ey(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function eb(e,t,r,n,i){return em(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=ey(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),ey(r,"salt"),function(e){let t=ey(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}var ew=r(77598);let eg=(e,t)=>(0,ew.createHash)(e).update(t).digest();var ev=r(4573);let e_=new TextEncoder,ek=new TextDecoder;function ex(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function eE(e,t){return ex(e_.encode(e),new Uint8Array([0]),t)}function eS(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function eA(e){let t=new Uint8Array(4);return eS(t,e),t}function eR(e){return ex(eA(e.length),e)}async function eP(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(eA(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await eg("sha256",n),32*t)}return i.slice(0,t>>3)}let eT=e=>ev.Buffer.from(e).toString("base64url"),ej=e=>new Uint8Array(ev.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=ek.decode(t)),t}(e),"base64url"));class eO extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class eC extends eO{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eU extends eO{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eN extends eO{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class eI extends eO{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class e$ extends eO{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class eD extends eO{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eH extends eO{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eL extends eO{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eW extends eO{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eM(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let eK=(e,t)=>{if("string"!=typeof e||!e)throw new eL(`${t} missing or invalid`)};async function eq(e,t){let r;if(!eM(e))throw TypeError("JWK must be an object");if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":eK(e.crv,'"crv" (Curve) Parameter'),eK(e.x,'"x" (X Coordinate) Parameter'),eK(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":eK(e.crv,'"crv" (Subtype of Key Pair) Parameter'),eK(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":eK(e.e,'"e" (Exponent) Parameter'),eK(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":eK(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new eI('"kty" (Key Type) Parameter missing or unsupported')}let n=e_.encode(JSON.stringify(r));return eT(await eg(t,n))}let eJ=Symbol();function eB(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new eI(`Unsupported JWE Algorithm: ${e}`)}}let eF=e=>(0,ew.randomFillSync)(new Uint8Array(eB(e)>>3)),ez=(e,t)=>{if(t.length<<3!==eB(e))throw new eD("Invalid Initialization Vector length")};var eV=r(57975);let eG=e=>eV.types.isKeyObject(e),eX=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new eI(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new eD(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if(eG(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new eD(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")};function eY(e,t,r,n,i,a){let o=ex(e,t,r,function(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return eS(r,t,0),eS(r,e%0x100000000,4),r}(e.length<<3)),s=(0,ew.createHmac)(`sha${n}`,i);return s.update(o),s.digest().slice(0,a>>3)}let eZ=ew.webcrypto,eQ=e=>eV.types.isCryptoKey(e);function e0(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function e1(e,t){return e.name===t}function e2(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!e1(e.algorithm,"AES-GCM"))throw e0("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw e0(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!e1(e.algorithm,"AES-KW"))throw e0("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw e0(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw e0("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!e1(e.algorithm,"PBKDF2"))throw e0("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!e1(e.algorithm,"RSA-OAEP"))throw e0("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw e0(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i.length&&!i.some(e=>n.usages.includes(e))){let e="CryptoKey does not support this operation, its usages must include ";if(i.length>2){let t=i.pop();e+=`one of ${i.join(", ")}, or ${t}.`}else 2===i.length?e+=`one of ${i[0]} or ${i[1]}.`:e+=`${i[0]}.`;throw TypeError(e)}}function e5(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let e6=(e,...t)=>e5("Key must be ",e,...t);function e3(e,t,...r){return e5(`Key for the ${e} algorithm must be `,t,...r)}let e4=e=>(i||=new Set((0,ew.getCiphers)())).has(e),e8=e=>eG(e)||eQ(e),e9=["KeyObject"];(globalThis.CryptoKey||eZ?.CryptoKey)&&e9.push("CryptoKey");let e7=(e,t,r,n,i)=>{let a;if(eQ(r))e2(r,e,"encrypt"),a=ew.KeyObject.from(r);else if(r instanceof Uint8Array||eG(r))a=r;else throw TypeError(e6(r,...e9,"Uint8Array"));switch(eX(e,a),n?ez(e,n):n=eF(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,n,i){let a=parseInt(e.slice(1,4),10);eG(r)&&(r=r.export());let o=r.subarray(a>>3),s=r.subarray(0,a>>3),c=`aes-${a}-cbc`;if(!e4(c))throw new eI(`alg ${e} is not supported by your javascript runtime`);let l=(0,ew.createCipheriv)(c,o,n),u=ex(l.update(t),l.final()),d=eY(i,n,u,parseInt(e.slice(-3),10),s,a);return{ciphertext:u,tag:d,iv:n}}(e,t,a,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,n,i){let a=parseInt(e.slice(1,4),10),o=`aes-${a}-gcm`;if(!e4(o))throw new eI(`alg ${e} is not supported by your javascript runtime`);let s=(0,ew.createCipheriv)(o,r,n,{authTagLength:16});i.byteLength&&s.setAAD(i,{plaintextLength:t.length});let c=s.update(t);return s.final(),{ciphertext:c,tag:s.getAuthTag(),iv:n}}(e,t,a,n,i);default:throw new eI("Unsupported JWE Content Encryption Algorithm")}};function te(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function tt(e,t,r){if(eG(e))return e;if(e instanceof Uint8Array)return(0,ew.createSecretKey)(e);if(eQ(e))return e2(e,t,r),ew.KeyObject.from(e);throw TypeError(e6(e,...e9,"Uint8Array"))}let tr=(e,t,r)=>{let n=parseInt(e.slice(1,4),10),i=`aes${n}-wrap`;if(!e4(i))throw new eI(`alg ${e} is not supported either by JOSE or your javascript runtime`);let a=tt(t,e,"wrapKey");te(a,e);let o=(0,ew.createCipheriv)(i,a,ev.Buffer.alloc(8,166));return ex(o.update(r),o.final())},tn=(e,t,r)=>{let n=parseInt(e.slice(1,4),10),i=`aes${n}-wrap`;if(!e4(i))throw new eI(`alg ${e} is not supported either by JOSE or your javascript runtime`);let a=tt(t,e,"unwrapKey");te(a,e);let o=(0,ew.createDecipheriv)(i,a,ev.Buffer.alloc(8,166));return ex(o.update(r),o.final())};function ti(e){return eM(e)&&"string"==typeof e.kty}new WeakMap;let ta=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new eI("Unsupported key curve for this operation")}},to=(e,t)=>{let r;if(eQ(e))r=ew.KeyObject.from(e);else if(eG(e))r=e;else if(ti(e))return e.crv;else throw TypeError(e6(e,...e9));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return ta(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}},ts=(0,eV.promisify)(ew.generateKeyPair);async function tc(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let o,s;if(eQ(e))e2(e,"ECDH"),o=ew.KeyObject.from(e);else if(eG(e))o=e;else throw TypeError(e6(e,...e9));if(eQ(t))e2(t,"ECDH","deriveBits"),s=ew.KeyObject.from(t);else if(eG(t))s=t;else throw TypeError(e6(t,...e9));let c=ex(eR(e_.encode(r)),eR(i),eR(a),eA(n));return eP((0,ew.diffieHellman)({privateKey:s,publicKey:o}),n,c)}async function tl(e){let t;if(eQ(e))t=ew.KeyObject.from(e);else if(eG(e))t=e;else throw TypeError(e6(e,...e9));switch(t.asymmetricKeyType){case"x25519":return ts("x25519");case"x448":return ts("x448");case"ec":return ts("ec",{namedCurve:to(t)});default:throw new eI("Invalid or unsupported EPK")}}let tu=e=>["P-256","P-384","P-521","X25519","X448"].includes(to(e));function td(e){if(!(e instanceof Uint8Array)||e.length<8)throw new eD("PBES2 Salt Input must be 8 or more octets")}let tp=(0,eV.promisify)(ew.pbkdf2);function tf(e,t){if(eG(e))return e.export();if(e instanceof Uint8Array)return e;if(eQ(e))return e2(e,t,"deriveBits","deriveKey"),ew.KeyObject.from(e).export();throw TypeError(e6(e,...e9,"Uint8Array"))}let th=async(e,t,r,n=2048,i=(0,ew.randomFillSync)(new Uint8Array(16)))=>{td(i);let a=eE(e,i),o=parseInt(e.slice(13,16),10)>>3,s=tf(t,e),c=await tp(s,a,n,o,`sha${e.slice(8,11)}`);return{encryptedKey:await tr(e.slice(-6),c,r),p2c:n,p2s:eT(i)}},tm=async(e,t,r,n,i)=>{td(i);let a=eE(e,i),o=parseInt(e.slice(13,16),10)>>3,s=tf(t,e),c=await tp(s,a,n,o,`sha${e.slice(8,11)}`);return tn(e.slice(-6),c,r)},ty=(e,t)=>{let r;try{r=e instanceof ew.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)},tb=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");ty(e,t)},tw=(0,eV.deprecate)(()=>ew.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),tg=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return ew.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return tw();default:return}},tv=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function t_(e,t,...r){if(eG(e))return e;if(eQ(e))return e2(e,t,...r),ew.KeyObject.from(e);throw TypeError(e6(e,...e9))}let tk=(e,t,r)=>{let n=tg(e),i=tv(e),a=t_(t,e,"wrapKey","encrypt");return tb(a,e),(0,ew.publicEncrypt)({key:a,oaepHash:i,padding:n},r)},tx=(e,t,r)=>{let n=tg(e),i=tv(e),a=t_(t,e,"unwrapKey","decrypt");return tb(a,e),(0,ew.privateDecrypt)({key:a,oaepHash:i,padding:n},r)},tE={};function tS(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new eI(`Unsupported JWE Algorithm: ${e}`)}}let tA=e=>(0,ew.randomFillSync)(new Uint8Array(tS(e)>>3)),tR=e=>{let t;if(eQ(e)){if(!e.extractable)throw TypeError("CryptoKey is not extractable");t=ew.KeyObject.from(e)}else if(eG(e))t=e;else if(e instanceof Uint8Array)return{kty:"oct",k:eT(e)};else throw TypeError(e6(e,...e9,"Uint8Array"));if("secret"!==t.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(t.asymmetricKeyType))throw new eI("Unsupported key asymmetricKeyType");return t.export({format:"jwk"})};async function tP(e){return tR(e)}let tT=e=>e?.[Symbol.toStringTag],tj=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},tO=(e,t,r,n)=>{if(!(t instanceof Uint8Array)){if(n&&ti(t)){if(function(e){return ti(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&tj(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!e8(t))throw TypeError(e3(e,t,...e9,"Uint8Array",n?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${tT(t)} instances for symmetric algorithms must be of type "secret"`)}},tC=(e,t,r,n)=>{if(n&&ti(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tj(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tj(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!e8(t))throw TypeError(e3(e,t,...e9,n?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${tT(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${tT(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${tT(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${tT(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${tT(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function tU(e,t,r,n){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?tO(t,r,n,e):tC(t,r,n,e)}let tN=tU.bind(void 0,!1);tU.bind(void 0,!0);let tI=ew.timingSafeEqual,t$=(e,t,r,n,i,a)=>{let o;if(eQ(t))e2(t,e,"decrypt"),o=ew.KeyObject.from(t);else if(t instanceof Uint8Array||eG(t))o=t;else throw TypeError(e6(t,...e9,"Uint8Array"));if(!n)throw new eD("JWE Initialization Vector missing");if(!i)throw new eD("JWE Authentication Tag missing");switch(eX(e,o),ez(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,n,i,a){let o,s,c=parseInt(e.slice(1,4),10);eG(t)&&(t=t.export());let l=t.subarray(c>>3),u=t.subarray(0,c>>3),d=parseInt(e.slice(-3),10),p=`aes-${c}-cbc`;if(!e4(p))throw new eI(`alg ${e} is not supported by your javascript runtime`);let f=eY(a,n,r,d,u,c);try{o=tI(i,f)}catch{}if(!o)throw new e$;try{let e=(0,ew.createDecipheriv)(p,l,n);s=ex(e.update(r),e.final())}catch{}if(!s)throw new e$;return s}(e,o,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,n,i,a){let o=parseInt(e.slice(1,4),10),s=`aes-${o}-gcm`;if(!e4(s))throw new eI(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,ew.createDecipheriv)(s,t,n,{authTagLength:16});e.setAuthTag(i),a.byteLength&&e.setAAD(a,{plaintextLength:r.length});let o=e.update(r);return e.final(),o}catch{throw new e$}}(e,o,r,n,i,a);default:throw new eI("Unsupported JWE Content Encryption Algorithm")}};async function tD(e,t,r,n){let i=e.slice(0,7),a=await e7(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:eT(a.iv),tag:eT(a.tag)}}async function tH(e,t,r,n,i){return t$(e.slice(0,7),t,r,n,i,new Uint8Array(0))}async function tL(e,t,r,n,i={}){let a,o,s;switch(tN(e,r,"encrypt"),r=await tE.normalizePublicKey?.(r,e)||r,e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!tu(r))throw new eI("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:l}=i,{epk:u}=i;u||=(await tl(r)).privateKey;let{x:d,y:p,crv:f,kty:h}=await tP(u),m=await tc(r,u,"ECDH-ES"===e?t:e,"ECDH-ES"===e?tS(t):parseInt(e.slice(-5,-2),10),c,l);if(o={epk:{x:d,crv:f,kty:h}},"EC"===h&&(o.epk.y=p),c&&(o.apu=eT(c)),l&&(o.apv=eT(l)),"ECDH-ES"===e){s=m;break}s=n||tA(t);let y=e.slice(-6);a=await tr(y,m,s);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||tA(t),a=await tk(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||tA(t);let{p2c:c,p2s:l}=i;({encryptedKey:a,...o}=await th(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||tA(t),a=await tr(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||tA(t);let{iv:c}=i;({encryptedKey:a,...o}=await tD(e,r,s,c));break}default:throw new eI('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:a,parameters:o}}let tW=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tM=function(e,t,r,n,i){let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(o))throw new eI(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)};class tK{_plaintext;_protectedHeader;_sharedUnprotectedHeader;_unprotectedHeader;_aad;_cek;_iv;_keyManagementParameters;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,n,i,a,o;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new eD("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tW(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new eD("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(tM(eD,new Map,t?.crit,this._protectedHeader,s),void 0!==s.zip)throw new eI('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new eD('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new eD('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);{let i;({cek:n,encryptedKey:r,parameters:i}=await tL(c,l,e,this._cek,this._keyManagementParameters)),i&&(t&&eJ in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...i}:this.setUnprotectedHeader(i):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...i}:this.setProtectedHeader(i))}a=this._protectedHeader?e_.encode(eT(JSON.stringify(this._protectedHeader))):e_.encode(""),this._aad?(o=eT(this._aad),i=ex(a,e_.encode("."),e_.encode(o))):i=a;let{ciphertext:u,tag:d,iv:p}=await e7(l,this._plaintext,n,this._iv,i),f={ciphertext:eT(u)};return p&&(f.iv=eT(p)),d&&(f.tag=eT(d)),r&&(f.encrypted_key=eT(r)),o&&(f.aad=o),this._protectedHeader&&(f.protected=ek.decode(a)),this._sharedUnprotectedHeader&&(f.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(f.header=this._unprotectedHeader),f}}class tq{_flattened;constructor(e){this._flattened=new tK(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tJ=e=>Math.floor(e.getTime()/1e3),tB=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tF=e=>{let t,r=tB.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tz(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class tV{_payload;constructor(e={}){if(!eM(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:tz("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:tz("setNotBefore",tJ(e))}:this._payload={...this._payload,nbf:tJ(new Date)+tF(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:tz("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:tz("setExpirationTime",tJ(e))}:this._payload={...this._payload,exp:tJ(new Date)+tF(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:tJ(new Date)}:e instanceof Date?this._payload={...this._payload,iat:tz("setIssuedAt",tJ(e))}:"string"==typeof e?this._payload={...this._payload,iat:tz("setIssuedAt",tJ(new Date)+tF(e))}:this._payload={...this._payload,iat:tz("setIssuedAt",e)},this}}class tG extends tV{_cek;_iv;_keyManagementParameters;_protectedHeader;_replicateIssuerAsHeader;_replicateSubjectAsHeader;_replicateAudienceAsHeader;setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new tq(e_.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let tX=e=>e.d?(0,ew.createPrivateKey)({format:"jwk",key:e}):(0,ew.createPublicKey)({format:"jwk",key:e});async function tY(e,t){if(!eM(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return ej(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new eI('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return tX({...e,alg:t});default:throw new eI('Unsupported "kty" (Key Type) Parameter value')}}async function tZ(e,t,r,n,i){switch(tN(e,t,"decrypt"),t=await tE.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new eD("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new eD("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!eM(n.epk))throw new eD('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!tu(t))throw new eI("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tY(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new eD('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=ej(n.apu)}catch{throw new eD("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new eD('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=ej(n.apv)}catch{throw new eD("Failed to base64url decode the apv")}}let s=await tc(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?tS(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return s;if(void 0===r)throw new eD("JWE Encrypted Key missing");return tn(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new eD("JWE Encrypted Key missing");return tx(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new eD("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new eD('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(n.p2c>o)throw new eD('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new eD('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=ej(n.p2s)}catch{throw new eD("Failed to base64url decode the p2s")}return tm(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new eD("JWE Encrypted Key missing");return tn(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new eD("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new eD('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new eD('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=ej(n.iv)}catch{throw new eD("Failed to base64url decode the iv")}try{a=ej(n.tag)}catch{throw new eD("Failed to base64url decode the tag")}return tH(e,t,r,i,a)}default:throw new eI('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let tQ=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function t0(e,t,r){let n,i,a,o,s,c,l;if(!eM(e))throw new eD("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new eD("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new eD("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new eD("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new eD("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eD("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new eD("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new eD("JWE AAD incorrect type");if(void 0!==e.header&&!eM(e.header))throw new eD("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eM(e.unprotected))throw new eD("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=ej(e.protected);n=JSON.parse(ek.decode(t))}catch{throw new eD("JWE Protected Header is invalid")}if(!tW(n,e.header,e.unprotected))throw new eD("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(tM(eD,new Map,r?.crit,n,u),void 0!==u.zip)throw new eI('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new eD("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new eD("missing JWE Encryption Algorithm (enc) in JWE Header");let f=r&&tQ("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tQ("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(f&&!f.has(d)||!f&&d.startsWith("PBES2"))throw new eN('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(p))throw new eN('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=ej(e.encrypted_key)}catch{throw new eD("Failed to base64url decode the encrypted_key")}let m=!1;"function"==typeof t&&(t=await t(n,e),m=!0);try{a=await tZ(d,t,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof eD||e instanceof eI)throw e;a=tA(p)}if(void 0!==e.iv)try{o=ej(e.iv)}catch{throw new eD("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=ej(e.tag)}catch{throw new eD("Failed to base64url decode the tag")}let y=e_.encode(e.protected??"");c=void 0!==e.aad?ex(y,e_.encode("."),e_.encode(e.aad)):y;try{l=ej(e.ciphertext)}catch{throw new eD("Failed to base64url decode the ciphertext")}let b={plaintext:await t$(p,a,l,o,s,c)};if(void 0!==e.protected&&(b.protectedHeader=n),void 0!==e.aad)try{b.additionalAuthenticatedData=ej(e.aad)}catch{throw new eD("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(b.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(b.unprotectedHeader=e.header),m)?{...b,key:t}:b}async function t1(e,t,r){if(e instanceof Uint8Array&&(e=ek.decode(e)),"string"!=typeof e)throw new eD("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new eD("Invalid Compact JWE");let l=await t0({ciphertext:o,iv:a||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}let t2=e=>e.toLowerCase().replace(/^application\//,""),t5=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),t6=(e,t,r={})=>{let n,i;try{n=JSON.parse(ek.decode(t))}catch{}if(!eM(n))throw new eH("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||t2(e.typ)!==t2(a)))throw new eC('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new eC(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new eC('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new eC('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!t5(n.aud,"string"==typeof l?[l]:l))throw new eC('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=tF(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,f=tJ(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new eC('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new eC('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>f+i)throw new eC('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new eC('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=f-i)throw new eU('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=f-n.iat;if(e-i>("number"==typeof u?u:tF(u)))throw new eU('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new eC('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n};async function t3(e,t,r){let n=await t1(e,t,r),i=t6(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new eC('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new eC('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new eC('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:a};return"function"==typeof t?{...o,key:n.key}:o}var t4=r(27986);let t8=()=>Date.now()/1e3|0,t9="A256CBC-HS512";async function t7(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],o=await rt(t9,a[0],i),s=await eq({kty:"oct",k:eT(o)},`sha${o.byteLength<<3}`);return await new tG(t).setProtectedHeader({alg:"dir",enc:t9,kid:s}).setIssuedAt().setExpirationTime(t8()+n).setJti(crypto.randomUUID()).encrypt(o)}async function re(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await t3(t,async({kid:e,enc:t})=>{for(let r of i){let i=await rt(t,r,n);if(void 0===e||e===await eq({kty:"oct",k:eT(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[t9,"A256GCM"]});return a}async function rt(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await eb("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function rr({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let rn="\x1b[31m",ri="\x1b[0m",ra={error(e){let t=e instanceof E?e.type:e.name;if(console.error(`${rn}[auth][error]${ri} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${rn}[auth][cause]${ri}:`,t.stack),r&&console.error(`${rn}[auth][details]${ri}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){let t=`https://warnings.authjs.dev#${e}`;console.warn(`\x1b[33m[auth][warn][${e}]${ri}`,`Read more: ${t}`)},debug(e,t){console.log(`\x1b[90m[auth][debug]:${ri} ${e}`,JSON.stringify(t,null,2))}};function ro(e){let t={...ra};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let rs=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function rc(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function rl(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new z("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new z(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new z(`Cannot parse action at ${e}`);let[i,a]=n;if(!rs.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new z(`Cannot parse action at ${e}`);return{action:i,providerId:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await rc(e):void 0,cookies:(0,t4.q)(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=ro(t);r.error(n),r.debug("request",e)}}function ru(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=(0,t4.l)(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function rd(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function rp(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function rf({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await rd(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=rp(32),a=await rd(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function rh(e,t){if(!t)throw new Z(`CSRF token was missing during an action ${e}`)}function rm(e){return null!==e&&"object"==typeof e}function ry(e,...t){if(!t.length)return e;let r=t.shift();if(rm(e)&&rm(r))for(let t in r)rm(r[t])?(rm(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),ry(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return ry(e,...t)}let rb=Symbol("skip-csrf-check"),rw=Symbol("return-type-raw"),rg=Symbol("custom-fetch"),rv=Symbol("conform-internal"),r_=e=>rx({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rk=e=>rx({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rx(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rE(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rS={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rA({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:o,csrfDisabled:s,isPost:c}){var l,u;let d=ro(e),{providers:p,provider:f}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,o=i?.id??a.id,s=ry(a,i,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rE(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rE(e.token,e.issuer),n=rE(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??r_,account:e.account??rk}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rg]??(e[rg]=i?.[rg]),e}return s});return{providers:i,provider:i.find(({id:e})=>e===t)}}({url:n,providerId:t,config:e}),h=!1;if((f?.type==="oauth"||f?.type==="oidc")&&f.redirectProxyUrl)try{h=new URL(f.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${f.redirectProxyUrl}`)}let m={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:f,cookies:ry(k(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:t7,decode:re,...e.jwt},events:(l=e.events??{},u=d,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new j(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new A(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...rS,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:h,experimental:{...e.experimental}},y=[];if(s)m.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await rf({options:m,cookieValue:i?.[m.cookies.csrfToken.name],isPost:c,bodyValue:o});m.csrfToken=e,m.csrfTokenVerified=r,t&&y.push({name:m.cookies.csrfToken.name,value:t,options:m.cookies.csrfToken.options})}let{callbackUrl:b,callbackUrlCookie:w}=await rr({options:m,cookieValue:i?.[m.cookies.callbackUrl.name],paramValue:a});return m.callbackUrl=b,w&&y.push({name:m.cookies.callbackUrl.name,value:w,options:m.cookies.callbackUrl.options}),{options:m,cookies:y}}var rR,rP,rT,rj,rO,rC,rU,rN,rI,r$,rD,rH={},rL=[],rW=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function rM(e,t){for(var r in t)e[r]=t[r];return e}function rK(e){var t=e.parentNode;t&&t.removeChild(e)}function rq(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==i?++rI:i};return null==i&&null!=rN.vnode&&rN.vnode(a),a}function rJ(e){return e.children}function rB(e,t){this.props=e,this.context=t}function rF(e,t){if(null==t)return e.__?rF(e.__,e.__.__k.indexOf(e)+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rF(e):null}function rz(e){(!e.__d&&(e.__d=!0)&&r$.push(e)&&!rV.__r++||rD!==rN.debounceRendering)&&((rD=rN.debounceRendering)||setTimeout)(rV)}function rV(){for(var e;rV.__r=r$.length;)e=r$.sort(function(e,t){return e.__v.__b-t.__v.__b}),r$=[],e.some(function(e){var t,r,n,i,a;e.__d&&(i=(n=e.__v).__e,(a=e.__P)&&(t=[],(r=rM({},n)).__v=n.__v+1,r1(a,n,r,e.__n,void 0!==a.ownerSVGElement,null!=n.__h?[i]:null,t,null==i?rF(n):i,n.__h),r2(t,n),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)))})}function rG(e,t,r,n,i,a,o,s,c,l){var u,d,p,f,h,m,y,b=n&&n.__k||rL,w=b.length;for(r.__k=[],u=0;u<t.length;u++)if(null!=(f=r.__k[u]=null==(f=t[u])||"boolean"==typeof f?null:"string"==typeof f||"number"==typeof f||"bigint"==typeof f?rq(null,f,null,null,f):Array.isArray(f)?rq(rJ,{children:f},null,null,null):f.__b>0?rq(f.type,f.props,f.key,f.ref?f.ref:null,f.__v):f)){if(f.__=r,f.__b=r.__b+1,null===(p=b[u])||p&&f.key==p.key&&f.type===p.type)b[u]=void 0;else for(d=0;d<w;d++){if((p=b[d])&&f.key==p.key&&f.type===p.type){b[d]=void 0;break}p=null}r1(e,f,p=p||rH,i,a,o,s,c,l),h=f.__e,(d=f.ref)&&p.ref!=d&&(y||(y=[]),p.ref&&y.push(p.ref,null,f),y.push(d,f.__c||h,f)),null!=h?(null==m&&(m=h),"function"==typeof f.type&&f.__k===p.__k?f.__d=c=function e(t,r,n){for(var i,a=t.__k,o=0;a&&o<a.length;o++)(i=a[o])&&(i.__=t,r="function"==typeof i.type?e(i,r,n):rX(n,i,i,a,i.__e,r));return r}(f,c,e):c=rX(e,f,p,b,h,c),"function"==typeof r.type&&(r.__d=c)):c&&p.__e==c&&c.parentNode!=e&&(c=rF(p))}for(r.__e=m,u=w;u--;)null!=b[u]&&function e(t,r,n){var i,a;if(rN.unmount&&rN.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||r5(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){rN.__e(e,r)}i.base=i.__P=null,t.__c=void 0}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||null==t.__e||rK(t.__e),t.__=t.__e=t.__d=void 0}(b[u],b[u]);if(y)for(u=0;u<y.length;u++)r5(y[u],y[++u],y[++u])}function rX(e,t,r,n,i,a){var o,s,c;if(void 0!==t.__d)o=t.__d,t.__d=void 0;else if(null==r||i!=a||null==i.parentNode)e:if(null==a||a.parentNode!==e)e.appendChild(i),o=null;else{for(s=a,c=0;(s=s.nextSibling)&&c<n.length;c+=1)if(s==i)break e;e.insertBefore(i,a),o=a}return void 0!==o?o:i.nextSibling}function rY(e,t,r){"-"===t[0]?e.setProperty(t,r):e[t]=null==r?"":"number"!=typeof r||rW.test(t)?r:r+"px"}function rZ(e,t,r,n,i){var a;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rY(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rY(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n||e.addEventListener(t,a?r0:rQ,a):e.removeEventListener(t,a?r0:rQ,a);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,r))}}function rQ(e){this.l[e.type+!1](rN.event?rN.event(e):e)}function r0(e){this.l[e.type+!0](rN.event?rN.event(e):e)}function r1(e,t,r,n,i,a,o,s,c){var l,u,d,p,f,h,m,y,b,w,g,v,_,k,x,E=t.type;if(void 0!==t.constructor)return null;null!=r.__h&&(c=r.__h,s=t.__e=r.__e,t.__h=null,a=[s]),(l=rN.__b)&&l(t);try{e:if("function"==typeof E){if(y=t.props,b=(l=E.contextType)&&n[l.__c],w=l?b?b.props.value:l.__:n,r.__c?m=(u=t.__c=r.__c).__=u.__E:("prototype"in E&&E.prototype.render?t.__c=u=new E(y,w):(t.__c=u=new rB(y,w),u.constructor=E,u.render=r6),b&&b.sub(u),u.props=y,u.state||(u.state={}),u.context=w,u.__n=n,d=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=E.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=rM({},u.__s)),rM(u.__s,E.getDerivedStateFromProps(y,u.__s))),p=u.props,f=u.state,d)null==E.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==E.getDerivedStateFromProps&&y!==p&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(y,w),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(y,u.__s,w)||t.__v===r.__v){for(u.props=y,u.state=u.__s,t.__v!==r.__v&&(u.__d=!1),u.__v=t,t.__e=r.__e,t.__k=r.__k,t.__k.forEach(function(e){e&&(e.__=t)}),g=0;g<u._sb.length;g++)u.__h.push(u._sb[g]);u._sb=[],u.__h.length&&o.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(y,u.__s,w),null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(p,f,h)})}if(u.context=w,u.props=y,u.__v=t,u.__P=e,v=rN.__r,_=0,"prototype"in E&&E.prototype.render){for(u.state=u.__s,u.__d=!1,v&&v(t),l=u.render(u.props,u.state,u.context),k=0;k<u._sb.length;k++)u.__h.push(u._sb[k]);u._sb=[]}else do u.__d=!1,v&&v(t),l=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++_<25);u.state=u.__s,null!=u.getChildContext&&(n=rM(rM({},n),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(h=u.getSnapshotBeforeUpdate(p,f)),x=null!=l&&l.type===rJ&&null==l.key?l.props.children:l,rG(e,Array.isArray(x)?x:[x],t,r,n,i,a,o,s,c),u.base=t.__e,t.__h=null,u.__h.length&&o.push(u),m&&(u.__E=u.__=null),u.__e=!1}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,o,s){var c,l,u,d=r.props,p=t.props,f=t.type,h=0;if("svg"===f&&(i=!0),null!=a){for(;h<a.length;h++)if((c=a[h])&&"setAttribute"in c==!!f&&(f?c.localName===f:3===c.nodeType)){e=c,a[h]=null;break}}if(null==e){if(null===f)return document.createTextNode(p);e=i?document.createElementNS("http://www.w3.org/2000/svg",f):document.createElement(f,p.is&&p),a=null,s=!1}if(null===f)d===p||s&&e.data===p||(e.data=p);else{if(a=a&&rU.call(e.childNodes),l=(d=r.props||rH).dangerouslySetInnerHTML,u=p.dangerouslySetInnerHTML,!s){if(null!=a)for(d={},h=0;h<e.attributes.length;h++)d[e.attributes[h].name]=e.attributes[h].value;(u||l)&&(u&&(l&&u.__html==l.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,r,n,i){var a;for(a in r)"children"===a||"key"===a||a in t||rZ(e,a,null,r[a],n);for(a in t)i&&"function"!=typeof t[a]||"children"===a||"key"===a||"value"===a||"checked"===a||r[a]===t[a]||rZ(e,a,t[a],r[a],n)}(e,p,d,i,s),u)t.__k=[];else if(rG(e,Array.isArray(h=t.props.children)?h:[h],t,r,n,i&&"foreignObject"!==f,a,o,a?a[0]:r.__k&&rF(r,0),s),null!=a)for(h=a.length;h--;)null!=a[h]&&rK(a[h]);s||("value"in p&&void 0!==(h=p.value)&&(h!==e.value||"progress"===f&&!h||"option"===f&&h!==d.value)&&rZ(e,"value",h,d.value,!1),"checked"in p&&void 0!==(h=p.checked)&&h!==e.checked&&rZ(e,"checked",h,d.checked,!1))}return e}(r.__e,t,r,n,i,a,o,c);(l=rN.diffed)&&l(t)}catch(e){t.__v=null,(c||null!=a)&&(t.__e=s,t.__h=!!c,a[a.indexOf(s)]=null),rN.__e(e,t,r)}}function r2(e,t){rN.__c&&rN.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rN.__e(e,t.__v)}})}function r5(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){rN.__e(e,r)}}function r6(e,t,r){return this.constructor(e,r)}function r3(e,t){var r,n,i,a;r=e,rN.__&&rN.__(r,t),i=(n="function"==typeof r3)?null:r3&&r3.__k||t.__k,a=[],r1(t,r=(!n&&r3||t).__k=function(e,t,r){var n,i,a,o={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?rU.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return rq(e,o,n,i,null)}(rJ,null,[r]),i||rH,rH,void 0!==t.ownerSVGElement,!n&&r3?[r3]:i?null:t.firstChild?rU.call(t.childNodes):null,a,!n&&r3?r3:i?i.__e:t.firstChild,n),r2(a,r)}rU=rL.slice,rN={__e:function(e,t,r,n){for(var i,a,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},rI=0,rB.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rM({},this.state),"function"==typeof e&&(e=e(rM({},r),this.props)),e&&rM(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rz(this))},rB.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rz(this))},rB.prototype.render=rJ,r$=[],rV.__r=0;var r4=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,r8=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,r9=/[\s\n\\/='"\0<>]/,r7=/^xlink:?./,ne=/["&<]/;function nt(e){if(!1===ne.test(e+=""))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var nr=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},nn=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},ni={},na=/([A-Z])/g;function no(e){var t="";for(var r in e){var n=e[r];null!=n&&""!==n&&(t&&(t+=" "),t+="-"==r[0]?r:ni[r]||(ni[r]=r.replace(na,"-$1").toLowerCase()),t="number"==typeof n&&!1===r4.test(r)?t+": "+n+"px;":t+": "+n+";")}return t||void 0}function ns(e,t){return Array.isArray(t)?t.reduce(ns,e):null!=t&&!1!==t&&e.push(t),e}function nc(){this.__d=!0}function nl(e,t){return{__v:e,context:t,props:e.props,setState:nc,forceUpdate:nc,__d:!0,__h:[]}}function nu(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var nd=[],np={shallow:!0};nh.render=nh;var nf=[];function nh(e,t,r){t=t||{};var n,i=rN.__s;return rN.__s=!0,n=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?function e(t,r,n,i,a,o){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return nt(t);var s=n.pretty,c=s&&"string"==typeof s?s:"	";if(Array.isArray(t)){for(var l="",u=0;u<t.length;u++)s&&u>0&&(l+="\n"),l+=e(t[u],r,n,i,a,o);return l}var d,p=t.type,f=t.props,h=!1;if("function"==typeof p){if(h=!0,!n.shallow||!i&&!1!==n.renderRootComponent){if(p===rJ){var m=[];return ns(m,t.props.children),e(m,r,n,!1!==n.shallowHighOrder,a,o)}var y,b=t.__c=nl(t,r);rN.__b&&rN.__b(t);var w=rN.__r;if(p.prototype&&"function"==typeof p.prototype.render){var g=nu(p,r);(b=t.__c=new p(f,g)).__v=t,b._dirty=b.__d=!0,b.props=f,null==b.state&&(b.state={}),null==b._nextState&&null==b.__s&&(b._nextState=b.__s=b.state),b.context=g,p.getDerivedStateFromProps?b.state=Object.assign({},b.state,p.getDerivedStateFromProps(b.props,b.state)):b.componentWillMount&&(b.componentWillMount(),b.state=b._nextState!==b.state?b._nextState:b.__s!==b.state?b.__s:b.state),w&&w(t),y=b.render(b.props,b.state,b.context)}else for(var v=nu(p,r),_=0;b.__d&&_++<25;)b.__d=!1,w&&w(t),y=p.call(t.__c,f,v);return b.getChildContext&&(r=Object.assign({},r,b.getChildContext())),rN.diffed&&rN.diffed(t),e(y,r,n,!1!==n.shallowHighOrder,a,o)}p=(d=p).displayName||d!==Function&&d.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=nd.length;n--;)if(nd[n]===e){r=n;break}r<0&&(r=nd.push(e)-1),t="UnnamedComponent"+r}return t}(d)}var k,x,E="<"+p;if(f){var S=Object.keys(f);n&&!0===n.sortAttributes&&S.sort();for(var A=0;A<S.length;A++){var R=S[A],P=f[R];if("children"!==R){if(!r9.test(R)&&(n&&n.allAttributes||"key"!==R&&"ref"!==R&&"__self"!==R&&"__source"!==R)){if("defaultValue"===R)R="value";else if("defaultChecked"===R)R="checked";else if("defaultSelected"===R)R="selected";else if("className"===R){if(void 0!==f.class)continue;R="class"}else a&&r7.test(R)&&(R=R.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===R){if(f.for)continue;R="for"}"style"===R&&P&&"object"==typeof P&&(P=no(P)),"a"===R[0]&&"r"===R[1]&&"boolean"==typeof P&&(P=String(P));var T=n.attributeHook&&n.attributeHook(R,P,r,n,h);if(T||""===T)E+=T;else if("dangerouslySetInnerHTML"===R)x=P&&P.__html;else if("textarea"===p&&"value"===R)k=P;else if((P||0===P||""===P)&&"function"!=typeof P){if(!(!0!==P&&""!==P||(P=R,n&&n.xml))){E=E+" "+R;continue}if("value"===R){if("select"===p){o=P;continue}"option"===p&&o==P&&void 0===f.selected&&(E+=" selected")}E=E+" "+R+'="'+nt(P)+'"'}}}else k=P}}if(s){var j=E.replace(/\n\s*/," ");j===E||~j.indexOf("\n")?s&&~E.indexOf("\n")&&(E+="\n"):E=j}if(E+=">",r9.test(p))throw Error(p+" is not a valid HTML tag name in "+E);var O,C=r8.test(p)||n.voidElements&&n.voidElements.test(p),U=[];if(x)s&&nn(x)&&(x="\n"+c+nr(x,c)),E+=x;else if(null!=k&&ns(O=[],k).length){for(var N=s&&~E.indexOf("\n"),I=!1,$=0;$<O.length;$++){var D=O[$];if(null!=D&&!1!==D){var H=e(D,r,n,!0,"svg"===p||"foreignObject"!==p&&a,o);if(s&&!N&&nn(H)&&(N=!0),H)if(s){var L=H.length>0&&"<"!=H[0];I&&L?U[U.length-1]+=H:U.push(H),I=L}else U.push(H)}}if(s&&N)for(var W=U.length;W--;)U[W]="\n"+c+nr(U[W],c)}if(U.length||x)E+=U.join("");else if(n&&n.xml)return E.substring(0,E.length-1)+" />";return!C||O||x?(s&&~E.indexOf("\n")&&(E+="\n"),E=E+"</"+p+">"):E=E.replace(/>$/," />"),E}(e,t,r):function e(t,r,n,i){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return nt(t);if(nm(t)){for(var a="",o=0;o<t.length;o++)a+=e(t[o],r,n,i);return a}rN.__b&&rN.__b(t);var s=t.type,c=t.props;if("function"==typeof s){if(s===rJ)return e(t.props.children,r,n,i);var l,u,d,p,f,h=s.prototype&&"function"==typeof s.prototype.render?(l=r,d=nu(u=t.type,l),p=new u(t.props,d),t.__c=p,p.__v=t,p.__d=!0,p.props=t.props,null==p.state&&(p.state={}),null==p.__s&&(p.__s=p.state),p.context=d,u.getDerivedStateFromProps?p.state=ny({},p.state,u.getDerivedStateFromProps(p.props,p.state)):p.componentWillMount&&(p.componentWillMount(),p.state=p.__s!==p.state?p.__s:p.state),(f=rN.__r)&&f(t),p.render(p.props,p.state,p.context)):function(e,t){var r,n=nl(e,t),i=nu(e.type,t);e.__c=n;for(var a=rN.__r,o=0;n.__d&&o++<25;)n.__d=!1,a&&a(e),r=e.type.call(n,e.props,i);return r}(t,r),m=t.__c;m.getChildContext&&(r=ny({},r,m.getChildContext()));var y=e(h,r,n,i);return rN.diffed&&rN.diffed(t),y}var b,w,g="<";if(g+=s,c)for(var v in b=c.children,c){var _,k,x,E=c[v];if(!("key"===v||"ref"===v||"__self"===v||"__source"===v||"children"===v||"className"===v&&"class"in c||"htmlFor"===v&&"for"in c||r9.test(v))){if(k=v="className"===(_=v)?"class":"htmlFor"===_?"for":"defaultValue"===_?"value":"defaultChecked"===_?"checked":"defaultSelected"===_?"selected":n&&r7.test(_)?_.toLowerCase().replace(/^xlink:?/,"xlink:"):_,x=E,E="style"===k&&null!=x&&"object"==typeof x?no(x):"a"===k[0]&&"r"===k[1]&&"boolean"==typeof x?String(x):x,"dangerouslySetInnerHTML"===v)w=E&&E.__html;else if("textarea"===s&&"value"===v)b=E;else if((E||0===E||""===E)&&"function"!=typeof E){if(!0===E||""===E){E=v,g=g+" "+v;continue}if("value"===v){if("select"===s){i=E;continue}"option"!==s||i!=E||"selected"in c||(g+=" selected")}g=g+" "+v+'="'+nt(E)+'"'}}}var S=g;if(g+=">",r9.test(s))throw Error(s+" is not a valid HTML tag name in "+g);var A="",R=!1;if(w)A+=w,R=!0;else if("string"==typeof b)A+=nt(b),R=!0;else if(nm(b))for(var P=0;P<b.length;P++){var T=b[P];if(null!=T&&!1!==T){var j=e(T,r,"svg"===s||"foreignObject"!==s&&n,i);j&&(A+=j,R=!0)}}else if(null!=b&&!1!==b&&!0!==b){var O=e(b,r,"svg"===s||"foreignObject"!==s&&n,i);O&&(A+=O,R=!0)}if(rN.diffed&&rN.diffed(t),R)g+=A;else if(r8.test(s))return S+" />";return g+"</"+s+">"}(e,t,!1,void 0),rN.__c&&rN.__c(e,nf),rN.__s=i,nf.length=0,n}var nm=Array.isArray,ny=Object.assign;nh.shallowRender=function(e,t){return nh(e,t,np)};var nb=0;function nw(e,t,r,n,i){var a,o,s={};for(o in t)"ref"==o?a=t[o]:s[o]=t[o];var c={type:e,props:s,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--nb,__source:i,__self:n};if("function"==typeof e&&(a=e.defaultProps))for(o in a)void 0===s[o]&&(s[o]=a[o]);return rN.vnode&&rN.vnode(c),c}async function ng(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let nv={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},n_=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nk({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${n_}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${nh(e)}</div></body></html>`}}function nx(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,s){if(t)throw new z("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nk({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:o}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=o&&(nv[o]??nv.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return nw("div",{className:"signin",children:[i?.brandColor&&nw("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&nw("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),nw("div",{className:"card",children:[s&&nw("div",{className:"error",children:nw("p",{children:s})}),i?.logo&&nw("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return nw("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?nw("form",{action:e.signinUrl,method:"POST",children:[nw("input",{type:"hidden",name:"csrfToken",value:t}),n&&nw("input",{type:"hidden",name:"callbackUrl",value:n}),nw("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${l} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${l} 30%, #000)`},tabIndex:0,children:[nw("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&nw("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&nw("hr",{}),"email"===e.type&&nw("form",{action:e.signinUrl,method:"POST",children:[nw("input",{type:"hidden",name:"csrfToken",value:t}),nw("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),nw("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),nw("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&nw("form",{action:e.callbackUrl,method:"POST",children:[nw("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>nw("div",{children:[nw("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),nw("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),nw("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&nw("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[nw("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>nw("div",{children:[nw("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),nw("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),nw("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&nw("hr",{})]},e.id)})]}),c&&nw(rJ,{children:nw("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${ng})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:nk({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return nw("div",{className:"signout",children:[n?.brandColor&&nw("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&nw("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),nw("div",{className:"card",children:[n?.logo&&nw("img",{src:n.logo,alt:"Logo",className:"logo"}),nw("h1",{children:"Signout"}),nw("p",{children:"Are you sure you want to sign out?"}),nw("form",{action:t?.toString(),method:"POST",children:[nw("input",{type:"hidden",name:"csrfToken",value:r}),nw("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:a.verifyRequest,cookies:i}:nk({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return nw("div",{className:"verify-request",children:[r.brandColor&&nw("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),nw("div",{className:"card",children:[r.logo&&nw("img",{src:r.logo,alt:"Logo",className:"logo"}),nw("h1",{children:"Check your email"}),nw("p",{children:"A sign in link has been sent to your email address."}),nw("p",{children:nw("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:nk({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:nw("p",{children:nw("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:nw("div",{children:[nw("p",{children:"There is a problem with the server configuration."}),nw("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:nw("div",{children:[nw("p",{children:"You do not have permission to sign in."}),nw("p",{children:nw("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:nw("div",{children:[nw("p",{children:"The sign in link is no longer valid."}),nw("p",{children:"It may have been used already or it may have expired."})]}),signin:nw("a",{className:"button",href:i,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=a[r]??a.default;return{status:o,html:nw("div",{className:"error",children:[n?.brandColor&&nw("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),nw("div",{className:"card",children:[n?.logo&&nw("img",{src:n?.logo,alt:"Logo",className:"logo"}),nw("h1",{children:s}),nw("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nE(e,t=Date.now()){return new Date(t+1e3*e)}async function nS(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:f,getUserByEmail:h,linkAccount:m,createSession:y,getSessionAndUser:b,deleteSession:w}=i,g=null,v=null,_=!1,k="jwt"===s;if(e)if(k)try{let t=n.cookies.sessionToken.name;(g=await a.decode({...a,token:e,salt:t}))&&"sub"in g&&g.sub&&(v=await p(g.sub))}catch{}else{let t=await b(e);t&&(g=t.session,v=t.user)}if("email"===l.type){let r=await h(t.email);return r?(v?.id!==r.id&&!k&&e&&await w(e),v=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:v})):(v=await u({...t,emailVerified:new Date}),await o.createUser?.({user:v}),_=!0),{session:g=k?{}:await y({sessionToken:c(),userId:v.id,expires:nE(n.session.maxAge)}),user:v,isNewUser:_}}if("webauthn"===l.type){let e=await f({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(v){if(e.id===v.id){let e={...l,userId:v.id};return{session:g,user:v,isNewUser:_,account:e}}throw new en("The account is already associated with another user",{provider:l.provider})}g=k?{}:await y({sessionToken:c(),userId:e.id,expires:nE(n.session.maxAge)});let t={...l,userId:e.id};return{session:g,user:e,isNewUser:_,account:t}}{if(v){await m({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t});let e={...l,userId:v.id};return{session:g,user:v,isNewUser:_,account:e}}if(t.email?await h(t.email):null)throw new en("Another account already exists with the same e-mail address",{provider:l.provider});v=await u({...t}),await o.createUser?.({user:v}),await m({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),g=k?{}:await y({sessionToken:c(),userId:v.id,expires:nE(n.session.maxAge)});let e={...l,userId:v.id};return{session:g,user:v,isNewUser:!0,account:e}}}let x=await f({providerAccountId:l.providerAccountId,provider:l.provider});if(x){if(v){if(x.id===v.id)return{session:g,user:v,isNewUser:_};throw new W("The account is already associated with another user",{provider:l.provider})}return{session:g=k?{}:await y({sessionToken:c(),userId:x.id,expires:nE(n.session.maxAge)}),user:x,isNewUser:_}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:s}),v)return await m({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:g,user:v,isNewUser:_};let p=t.email?await h(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)v=p,_=!1;else throw new W("Another account already exists with the same e-mail address",{provider:l.provider})}else v=await u({...t,emailVerified:null}),_=!0;return await o.createUser?.({user:v}),await m({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:g=k?{}:await y({sessionToken:c(),userId:v.id,expires:nE(n.session.maxAge)}),user:v,isNewUser:_}}}function nA(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.5.2");let nR="ERR_INVALID_ARG_VALUE",nP="ERR_INVALID_ARG_TYPE";function nT(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nj=Symbol(),nO=Symbol(),nC=Symbol(),nU=Symbol(),nN=Symbol(),nI=Symbol(),n$=Symbol(),nD=new TextEncoder,nH=new TextDecoder;function nL(e){return"string"==typeof e?nD.encode(e):nH.decode(e)}function nW(e){return"string"==typeof e?s(e):o(e)}o=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},s=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw nT("The input to be decoded is not correctly encoded.",nR,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nT("The input to be decoded is not correctly encoded.",nR,e)}};class nM extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iZ,Error.captureStackTrace?.(this,this.constructor)}}class nK extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nq(e,t,r){return new nK(e,{code:t,cause:r})}function nJ(e,t){if(!(e instanceof CryptoKey))throw nT(`${t} must be a CryptoKey`,nP)}function nB(e,t){if(nJ(e,t),"private"!==e.type)throw nT(`${t} must be a private CryptoKey`,nR)}function nF(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nz(e){nA(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw nT('"options.headers" must not include the "authorization" header name',nR);return t}function nV(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nT('"options.signal" must return or be an instance of AbortSignal',nP);return e}function nG(e){return e.includes("//")?e.replace("//","/"):e}async function nX(e,t,r,n){if(!(e instanceof URL))throw nT(`"${t}" must be an instance of URL`,nP);is(e,n?.[nj]!==!0);let i=r(new URL(e.href)),a=nz(n?.headers);return a.set("accept","application/json"),(n?.[nU]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:n?.signal?nV(n.signal):void 0})}async function nY(e,t){return nX(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nG(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=nG(`${n}/${r.pathname}`);break;default:throw nT('"options.algorithm" must be "oidc" (default), or "oauth2"',nR)}return e},t)}function nZ(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw nT(`${r} must be a number`,nP,i);if(e>0)return;if(t){if(0!==e)throw nT(`${r} must be a non-negative number`,nR,i);return}throw nT(`${r} must be a positive number`,nR,i)}catch(e){if(n)throw nq(e.message,n,i);throw e}}function nQ(e,t,r,n){try{if("string"!=typeof e)throw nT(`${t} must be a string`,nP,n);if(0===e.length)throw nT(`${t} must not be empty`,nR,n)}catch(e){if(r)throw nq(e.message,r,n);throw e}}async function n0(e,t){if(!(e instanceof URL)&&e!==aA)throw nT('"expectedIssuerIdentifier" must be an instance of URL',nP);if(!nA(t,Response))throw nT('"response" must be an instance of Response',nP);if(200!==t.status)throw nq('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',i3,t);aa(t);let r=await aE(t);if(nQ(r.issuer,'"response" body "issuer" property',i2,{body:r}),e!==aA&&new URL(r.issuer).href!==e.href)throw nq('"response" body "issuer" property does not match the expected value',ae,{expected:e.href,body:r,attribute:"issuer"});return r}function n1(e){var t=e,r="application/json";if(iP(t)!==r)throw n2(t,r)}function n2(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nq(r,i6,e)}function n5(){return nW(crypto.getRandomValues(new Uint8Array(32)))}async function n6(e){return nQ(e,"codeVerifier"),nW(await crypto.subtle.digest("SHA-256",nL(e)))}function n3(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nM("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nM("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nM("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nM("unsupported CryptoKey algorithm name",{cause:e})}}function n4(e){let t=e?.[nO];return"number"==typeof t&&Number.isFinite(t)?t:0}function n8(e){let t=e?.[nC];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n9(){return Math.floor(Date.now()/1e3)}function n7(e){if("object"!=typeof e||null===e)throw nT('"as" must be an object',nP);nQ(e.issuer,'"as.issuer"')}function ie(e){if("object"!=typeof e||null===e)throw nT('"client" must be an object',nP);nQ(e.client_id,'"client.client_id"')}function it(e,t){let r=n9()+n4(t);return{jti:n5(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function ir(e,t,r){if(!r.usages.includes("sign"))throw nT('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nR);let n=`${nW(nL(JSON.stringify(e)))}.${nW(nL(JSON.stringify(t)))}`,i=nW(await crypto.subtle.sign(au(r),r,nL(n)));return`${n}.${i}`}async function ii(e){let{kty:t,e:r,n,x:i,y:a,crv:o}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:i,y:a,crv:o};return c.set(e,s),s}async function ia(e){return(c||=new WeakMap).get(e)||ii(e)}let io=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function is(e,t){if(t&&"https:"!==e.protocol)throw nq("only requests to HTTPS are allowed",i4,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nq("only HTTP and HTTPS requests are allowed",i8,e)}function ic(e,t,r,n){let i;if("string"!=typeof e||!(i=io(e)))throw nq(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?ar:an,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return is(i,n),i}function il(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?ic(e.mtls_endpoint_aliases[t],t,r,n):ic(e[t],t,r,n)}class iu extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iY,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class id extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iQ,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class ip extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iX,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let ih="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",im=RegExp("^[,\\s]*("+ih+")\\s(.*)"),iy=RegExp("^[,\\s]*("+ih+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ib=RegExp("^[,\\s]*"+("("+ih+")\\s*=\\s*(")+ih+")[,\\s]*(.*)"),iw=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function ig(e){if(e.status>399&&e.status<500){aa(e),n1(e);try{let t=await e.clone().json();if(nF(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function iv(e,t,r){if(e.status!==t){let t;if(t=await ig(e))throw await e.body?.cancel(),new iu("server responded with an error in the response body",{cause:t,response:e});throw nq(`"response" is not a conform ${r} response (unexpected HTTP status code)`,i3,e)}}function i_(e){if(!iM.has(e))throw nT('"options.DPoP" is not a valid DPoPHandle',nR)}async function ik(e,t,r,n,i,a){if(nQ(e,'"accessToken"'),!(r instanceof URL))throw nT('"url" must be an instance of URL',nP);is(r,a?.[nj]!==!0),n=nz(n),a?.DPoP&&(i_(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (a?.[nU]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:a?.signal?nV(a.signal):void 0});return a?.DPoP?.cacheNonce(o),o}async function ix(e,t,r,n){n7(e),ie(t);let i=il(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nj]!==!0),a=nz(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),ik(r,"GET",i,a,null,{...n,[nO]:n4(t)})}function iE(e,t,r,n){(l||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n9()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function iS(e,t){l?.delete(e),delete t?.jwks,delete t?.uat}async function iA(e,t,r){var n;let i,a,o,{alg:s,kid:c}=r;if(function(e){if(!ac(e.alg))throw new nM('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!l?.has(e)&&!("object"!=typeof(n=t?.[n$])||null===n||!("uat"in n)||"number"!=typeof n.uat||n9()-n.uat>=300)&&"jwks"in n&&nF(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nF)&&iE(e,t?.[n$].jwks,t?.[n$].uat),l?.has(e)){if({jwks:i,age:a}=l.get(e),a>=300)return iS(e,t?.[n$]),iA(e,t,r)}else i=await ao(e,t).then(as),a=0,iE(e,i,n9(),t?.[n$]);switch(s.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new nM("unsupported JWS algorithm",{cause:{alg:s}})}let u=i.keys.filter(e=>{if(e.kty!==o||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=u;if(!p){if(a>=60)return iS(e,t?.[n$]),iA(e,t,r);throw nq("error when selecting a JWT verification key, no applicable keys found",at,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw nq('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',at,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return ak(s,d)}let iR=Symbol();function iP(e){return e.headers.get("content-type")?.split(";")[0]}async function iT(e,t,r,n,i){let a;if(n7(e),ie(t),!nA(n,Response))throw nT('"response" must be an instance of Response',nP);if(i$(n),200!==n.status)throw nq('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',i3,n);if(aa(n),"application/jwt"===iP(n)){let{claims:r,jwt:o}=await ap(await n.text(),ab.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n4(t),n8(t),i?.[nI]).then(iD.bind(void 0,t.client_id)).then(iL.bind(void 0,e));iU.set(n,o),a=r}else{if(t.userinfo_signed_response_alg)throw nq("JWT UserInfo Response expected",i0,n);a=await aE(n)}if(nQ(a.sub,'"response" body "sub" property',i2,{body:a}),r===iR);else if(nQ(r,'"expectedSubject"'),a.sub!==r)throw nq('unexpected "response" body "sub" property value',ae,{expected:r,body:a,attribute:"sub"});return a}async function ij(e,t,r,n,i,a,o){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[nU]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:o?.signal?nV(o.signal):void 0})}async function iO(e,t,r,n,i,a){let o=il(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[nj]!==!0);i.set("grant_type",n);let s=nz(a?.headers);s.set("accept","application/json"),a?.DPoP!==void 0&&(i_(a.DPoP),await a.DPoP.addProof(o,s,"POST"));let c=await ij(e,t,r,o,i,s,a);return a?.DPoP?.cacheNonce(c),c}let iC=new WeakMap,iU=new WeakMap;function iN(e){if(!e.id_token)return;let t=iC.get(e);if(!t)throw nT('"ref" was already garbage collected or did not resolve from the proper sources',nR);return t}async function iI(e,t,r,n,i){if(n7(e),ie(t),!nA(r,Response))throw nT('"response" must be an instance of Response',nP);i$(r),await iv(r,200,"Token Endpoint"),aa(r);let a=await aE(r);if(nQ(a.access_token,'"response" body "access_token" property',i2,{body:a}),nQ(a.token_type,'"response" body "token_type" property',i2,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new nM("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;nZ(e,!1,'"response" body "expires_in" property',i2,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&nQ(a.refresh_token,'"response" body "refresh_token" property',i2,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw nq('"response" body "scope" property must be a string',i2,{body:a});if(void 0!==a.id_token){nQ(a.id_token,'"response" body "id_token" property',i2,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nZ(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await ap(a.id_token,ab.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n4(t),n8(t),i?.[nI]).then(iJ.bind(void 0,o)).then(iW.bind(void 0,e)).then(iH.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nq('ID Token "aud" (audience) claim includes additional untrusted audiences',i7,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nq('unexpected ID Token "azp" (authorized party) claim value',i7,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nZ(s.auth_time,!1,'ID Token "auth_time" (authentication time)',i2,{claims:s}),iU.set(r,c),iC.set(a,s)}return a}function i$(e){let t;if(t=function(e){if(!nA(e,Response))throw nT('"response" must be an instance of Response',nP);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(im),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(iy)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(ib)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(iw)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let o={scheme:i,parameters:a};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new ip("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function iD(e,t){return void 0!==t.claims.aud?iH(e,t):t}function iH(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nq('unexpected JWT "aud" (audience) claim value',i7,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nq('unexpected JWT "aud" (audience) claim value',i7,{expected:e,claims:t.claims,claim:"aud"});return t}function iL(e,t){return void 0!==t.claims.iss?iW(e,t):t}function iW(e,t){let r=e[aR]?.(t)??e.issuer;if(t.claims.iss!==r)throw nq('unexpected JWT "iss" (issuer) claim value',i7,{expected:r,claims:t.claims,claim:"iss"});return t}let iM=new WeakSet;async function iK(e,t,r,n,i,a,o){if(n7(e),ie(t),!iM.has(n))throw nT('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nR);nQ(i,'"redirectUri"');let s=aw(n,"code");if(!s)throw nq('no authorization code in "callbackParameters"',i2);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),a!==aS&&(nQ(a,'"codeVerifier"'),c.set("code_verifier",a)),iO(e,t,r,"authorization_code",c,o)}let iq={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function iJ(e,t){for(let r of e)if(void 0===t.claims[r])throw nq(`JWT "${r}" (${iq[r]}) claim missing`,i2,{claims:t.claims});return t}let iB=Symbol(),iF=Symbol();async function iz(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?iV(e,t,r,n.expectedNonce,n.maxAge,{[nI]:n[nI]}):iG(e,t,r,n)}async function iV(e,t,r,n,i,a){let o=[];switch(n){case void 0:n=iB;break;case iB:break;default:nQ(n,'"expectedNonce" argument'),o.push("nonce")}switch(i??=t.default_max_age){case void 0:i=iF;break;case iF:break;default:nZ(i,!1,'"maxAge" argument'),o.push("auth_time")}let s=await iI(e,t,r,o,a);nQ(s.id_token,'"response" body "id_token" property',i2,{body:s});let c=iN(s);if(i!==iF){let e=n9()+n4(t),r=n8(t);if(c.auth_time+i<e-r)throw nq("too much time has elapsed since the last End-User authentication",i9,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===iB){if(void 0!==c.nonce)throw nq('unexpected ID Token "nonce" claim value',i7,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nq('unexpected ID Token "nonce" claim value',i7,{expected:n,claims:c,claim:"nonce"});return s}async function iG(e,t,r,n){let i=await iI(e,t,r,void 0,n),a=iN(i);if(a){if(void 0!==t.default_max_age){nZ(t.default_max_age,!1,'"client.default_max_age"');let e=n9()+n4(t),r=n8(t);if(a.auth_time+t.default_max_age<e-r)throw nq("too much time has elapsed since the last End-User authentication",i9,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw nq('unexpected ID Token "nonce" claim value',i7,{expected:void 0,claims:a,claim:"nonce"})}return i}let iX="OAUTH_WWW_AUTHENTICATE_CHALLENGE",iY="OAUTH_RESPONSE_BODY_ERROR",iZ="OAUTH_UNSUPPORTED_OPERATION",iQ="OAUTH_AUTHORIZATION_RESPONSE_ERROR",i0="OAUTH_JWT_USERINFO_EXPECTED",i1="OAUTH_PARSE_ERROR",i2="OAUTH_INVALID_RESPONSE",i5="OAUTH_INVALID_REQUEST",i6="OAUTH_RESPONSE_IS_NOT_JSON",i3="OAUTH_RESPONSE_IS_NOT_CONFORM",i4="OAUTH_HTTP_REQUEST_FORBIDDEN",i8="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",i9="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",i7="OAUTH_JWT_CLAIM_COMPARISON_FAILED",ae="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",at="OAUTH_KEY_SELECTION_FAILED",ar="OAUTH_MISSING_SERVER_METADATA",an="OAUTH_INVALID_SERVER_METADATA";function ai(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nq('unexpected JWT "typ" header parameter value',i2,{header:t.header});return t}function aa(e){if(e.bodyUsed)throw nT('"response" body has been used already',nR)}async function ao(e,t){n7(e);let r=il(e,"jwks_uri",!1,t?.[nj]!==!0),n=nz(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nU]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nV(t.signal):void 0})}async function as(e){if(!nA(e,Response))throw nT('"response" must be an instance of Response',nP);if(200!==e.status)throw nq('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',i3,e);aa(e);let t=await aE(e,e=>(function(e,...t){if(!t.includes(iP(e)))throw n2(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nq('"response" body "keys" property must be an array',i2,{body:t});if(!Array.prototype.every.call(t.keys,nF))throw nq('"response" body "keys" property members must be JWK formatted objects',i2,{body:t});return t}function ac(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function al(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nM(`unsupported ${t.name} modulusLength`,{cause:e})}function au(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nM("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(al(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nM("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return al(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nM("unsupported CryptoKey algorithm name",{cause:e})}async function ad(e,t,r,n){let i=nL(`${e}.${t}`),a=au(r);if(!await crypto.subtle.verify(a,r,n,i))throw nq("JWT signature verification failed",i2,{key:r,data:i,signature:n,algorithm:a})}async function ap(e,t,r,n,i){let a,o,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new nM("JWE decryption is not configured",{cause:e});if(3!==l)throw nq("Invalid JWT",i2,e);try{a=JSON.parse(nL(nW(s)))}catch(e){throw nq("failed to parse JWT Header body as base64url encoded JSON",i1,e)}if(!nF(a))throw nq("JWT Header must be a top level object",i2,e);if(t(a),void 0!==a.crit)throw new nM('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{o=JSON.parse(nL(nW(c)))}catch(e){throw nq("failed to parse JWT Payload body as base64url encoded JSON",i1,e)}if(!nF(o))throw nq("JWT Payload must be a top level object",i2,e);let u=n9()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nq('unexpected JWT "exp" (expiration time) claim type',i2,{claims:o});if(o.exp<=u-n)throw nq('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',i9,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nq('unexpected JWT "iat" (issued at) claim type',i2,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nq('unexpected JWT "iss" (issuer) claim type',i2,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nq('unexpected JWT "nbf" (not before) claim type',i2,{claims:o});if(o.nbf>u+n)throw nq('unexpected JWT "nbf" (not before) claim value',i9,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nq('unexpected JWT "aud" (audience) claim type',i2,{claims:o});return{header:a,claims:o,jwt:e}}async function af(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nM(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,nL(e));return nW(i.slice(0,i.byteLength/2))}async function ah(e,t,r,n){return t===await af(e,r,n)}async function am(e){if(e.bodyUsed)throw nT("form_post Request instances must contain a readable body",nR,{cause:e});return e.text()}async function ay(e){if("POST"!==e.method)throw nT("form_post responses are expected to use the POST method",nR,{cause:e});if("application/x-www-form-urlencoded"!==iP(e))throw nT("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nR,{cause:e});return am(e)}function ab(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nq('unexpected JWT "alg" header parameter',i2,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nq('unexpected JWT "alg" header parameter',i2,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nq('unexpected JWT "alg" header parameter',i2,{header:n,expected:r,reason:"default value"});return}throw nq('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function aw(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nq(`"${t}" parameter must be provided only once`,i2);return r}let ag=Symbol(),av=Symbol();function a_(e,t,r,n){var i;if(n7(e),ie(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nT('"parameters" must be an instance of URLSearchParams, or URL',nP);if(aw(r,"response"))throw nq('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',i2,{parameters:r});let a=aw(r,"iss"),o=aw(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw nq('response parameter "iss" (issuer) missing',i2,{parameters:r});if(a&&a!==e.issuer)throw nq('unexpected "iss" (issuer) response parameter value',i2,{expected:e.issuer,parameters:r});switch(n){case void 0:case av:if(void 0!==o)throw nq('unexpected "state" response parameter encountered',i2,{expected:void 0,parameters:r});break;case ag:break;default:if(nQ(n,'"expectedState" argument'),o!==n)throw nq(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',i2,{expected:n,parameters:r})}if(aw(r,"error"))throw new id("authorization response from the server is an error",{cause:r});let s=aw(r,"id_token"),c=aw(r,"token");if(void 0!==s||void 0!==c)throw new nM("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),iM.add(i),i}async function ak(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nM("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function ax(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function aE(e,t=n1){let r;try{r=await e.json()}catch(r){throw t(e),nq('failed to parse "response" body as JSON',i1,r)}if(!nF(r))throw nq('"response" body must be a top level object',i2,{body:r});return r}let aS=Symbol(),aA=Symbol(),aR=Symbol();async function aP(e,t,r){let{cookies:n,logger:i}=r,a=n[e],o=new Date;o.setTime(o.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:o});let s=await t7({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:o};return{name:a.name,value:s,options:c}}async function aT(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new N(`${e} cookie was missing`);let o=await re({...a,token:t,salt:i[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new N(`${e} value could not be parsed`,{cause:t})}}function aj(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function aO(e,t){return async function(r,n,i){let{provider:a,logger:o}=i;if(!a?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await aT(t,s,i);return aj(t,i,n),c}}let aC={async create(e){let t=n5(),r=await n6(t);return{cookie:await aP("pkceCodeVerifier",t,e),value:r}},use:aO("pkce","pkceCodeVerifier")},aU="encodedState",aN={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new N("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n5()},i=await t7({secret:e.jwt.secret,token:n,salt:aU,maxAge:900});return{cookie:await aP("state",i,e),value:i}},use:aO("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await re({secret:t.jwt.secret,token:e,salt:aU});if(r)return r;throw Error("Invalid state")}catch(e){throw new N("State could not be decoded",{cause:e})}}},aI={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n5();return{cookie:await aP("nonce",t,e),value:t}},use:aO("nonce","nonce")},a$="encodedWebauthnChallenge",aD={create:async(e,t,r)=>({cookie:await aP("webauthnChallenge",await t7({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:a$,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await aT("webauthnChallenge",n,e),a=await re({secret:e.jwt.secret,token:i,salt:a$});if(aj("webauthnChallenge",e,r),!a)throw new N("WebAuthn challenge was missing");return a}};function aH(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function aL(e,t,r){let n,i,a,{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await nY(e,{[nj]:!0,[nU]:s[rg]});if(!(n=await n0(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=aH(e),n=aH(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nQ(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;nQ(e,'"clientSecret"');let n=void 0;return async(t,i,a,o)=>{r||=await crypto.subtle.importKey("raw",nL(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=it(t,i);n?.(s,c);let l=`${nW(nL(JSON.stringify(s)))}.${nW(nL(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,nL(l));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${l}.${nW(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){var r;let{key:n,kid:i}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&nQ(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nB(n,'"clientPrivateKey.key"'),async(e,r,a,o)=>{let s={alg:n3(n),kid:i},c=it(e,r);t?.[nN]?.(s,c),a.set("client_id",r.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await ir(s,c,n))}}(s.token.clientPrivateKey,{[nN](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],f=await aN.use(t,p,r);try{a=a_(n,u,new URLSearchParams(e),s.checks.includes("state")?f:ag)}catch(e){if(e instanceof id){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new M("OAuth Provider returned an error",t)}throw e}let h=await aC.use(t,p,r),m=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(m=s.redirectProxyUrl);let y=await iK(n,u,i,a,m,h??"decoy",{[nj]:!0,[nU]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rg]??fetch)(...e))});s.token?.conform&&(y=await s.token.conform(y.clone())??y);let b={},w="oidc"===s.type;if(s[rv])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let{tid:e}=function(e){let t,r;if("string"!=typeof e)throw new eH("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new eH("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new eH("Invalid JWT");if(!n)throw new eH("JWTs must contain a payload");try{t=ej(n)}catch{throw new eH("Failed to base64url decode the payload")}try{r=JSON.parse(ek.decode(t))}catch{throw new eH("Failed to parse the decoded payload as JSON")}if(!eM(r))throw new eH("Invalid JWT Claims Set");return r}((await y.clone().json()).id_token);if("string"==typeof e){let t=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(t,e)),i=await nY(r,{[nU]:s[rg]});n=await n0(r,i)}}}let g=await iz(n,u,y,{expectedNonce:await aI.use(t,p,r),requireIdToken:w});if(w){let t=iN(g);if(b=t,s[rv]&&"apple"===s.id)try{b.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await ix(n,u,g.access_token,{[nU]:s[rg],[nj]:!0});b=await iT(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:g,provider:s});e instanceof Object&&(b=e)}else if(l?.url){let e=await ix(n,u,g.access_token,{[nU]:s[rg]});b=await e.json()}else throw TypeError("No userinfo endpoint configured");return g.expires_in&&(g.expires_at=Math.floor(Date.now()/1e3)+Number(g.expires_in)),{...await aW(b,s,g,o),profile:b,cookies:p}}async function aW(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new K(r,{provider:t.id}))}}async function aM(e,t,r,n){let i=await aF(e,t,r),{cookie:a}=await aD.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function aK(e,t,r,n){let i=await aB(e,t,r),{cookie:a}=await aD.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function aq(e,t,r){let n,{adapter:i,provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new E("Invalid WebAuthn Authentication response");let s=aG(aV(o.id)),c=await i.getAuthenticator(s);if(!c)throw new E(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await aD.use(e,t.cookies,r);try{var u;let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:aX(u.transports),credentialID:aV(u.credentialID),credentialPublicKey:aV(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new er(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new er("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new A(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let f=await i.getAccount(c.providerAccountId,a.id);if(!f)throw new E(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await i.getUser(f.userId);if(!h)throw new E(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:h}}async function aJ(e,t,r){var n;let i,{provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new E("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await aD.use(e,t.cookies,r);if(!c)throw new E("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new er(e)}if(!i.verified||!i.registrationInfo)throw new er("WebAuthn registration response could not be verified");let l={providerAccountId:aG(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:aG(i.registrationInfo.credentialID),credentialPublicKey:aG(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function aB(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:a?.map(e=>({id:aV(e.credentialID),type:"public-key",transports:aX(e.transports)}))})}async function aF(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,o=rp(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:a?.map(e=>({id:aV(e.credentialID),type:"public-key",transports:aX(e.transports)}))})}function az(e){let{provider:t,adapter:r}=e;if(!r)throw new $("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new G("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function aV(e){return new Uint8Array(Buffer.from(e,"base64"))}function aG(e){return Buffer.from(e).toString("base64")}function aX(e){return e?e.split(","):void 0}async function aY(e,t,r,n){if(!t.provider)throw new G("Callback route called without provider");let{query:i,body:a,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:f,events:h,callbacks:m,session:{strategy:y,maxAge:b},logger:w}=t,g="jwt"===y;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&s?.state){let e=await aN.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return w.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let y=await aL(s,e.cookies,t);y.cookies.length&&n.push(...y.cookies),w.debug("authorization result",y);let{user:v,account:_,profile:k}=y;if(!v||!_||!k)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:_.providerAccountId,provider:c.id})}let x=await aZ({user:o??v,account:_,profile:k},t);if(x)return{redirect:x,cookies:n};let{user:E,session:S,isNewUser:A}=await nS(r.value,v,_,t);if(g){let e={name:E.name,email:E.email,picture:E.image,sub:E.id?.toString()},i=await m.jwt({token:e,user:E,account:_,profile:k,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await h.signIn?.({user:E,account:_,profile:k,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:a,token:await rd(`${e}${o}`)}),u=!!s,y=u&&s.expires.valueOf()<Date.now();if(!u||y||a&&s.identifier!==a)throw new Y({hasInvite:u,expired:y});let{identifier:w}=s,v=await l.getUserByEmail(w)??{id:crypto.randomUUID(),email:w,emailVerified:null},_={providerAccountId:v.email,userId:v.id,type:"email",provider:c.id},k=await aZ({user:v,account:_},t);if(k)return{redirect:k,cookies:n};let{user:x,session:E,isNewUser:S}=await nS(r.value,v,_,t);if(g){let e={name:x.name,email:x.email,picture:x.image,sub:x.id?.toString()},i=await m.jwt({token:e,user:x,account:_,isNewUser:S,trigger:S?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:E.sessionToken,options:{...t.cookies.sessionToken.options,expires:E.expires}});if(await h.signIn?.({user:x,account:_,isNewUser:S}),S&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(a)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new C;let p={providerAccountId:l.id,type:"credentials",provider:c.id},y=await aZ({user:l,account:p,credentials:e},t);if(y)return{redirect:y,cookies:n};let w={name:l.name,email:l.email,picture:l.image,sub:l.id},g=await m.jwt({token:w,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===g)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:g,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let o=r.chunk(i,{expires:a});n.push(...o)}return await h.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let i,a,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new E("Invalid action parameter");let c=az(t);switch(s){case"authenticate":{let t=await aq(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await aJ(t,e,n);i=r.user,a=r.account,o=r.authenticator}}await aZ({user:i,account:a},t);let{user:l,isNewUser:u,session:y,account:w}=await nS(r.value,i,a,t);if(!w)throw new E("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),g){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await m.jwt({token:e,user:l,account:w,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:y.sessionToken,options:{...t.cookies.sessionToken.options,expires:y.expires}});if(await h.signIn?.({user:l,account:w,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new G(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof E)throw t;let e=new P(t,{provider:c.id});throw w.debug("callback route error details",{method:o,query:i,body:a}),e}}async function aZ(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof E)throw e;throw new R(e)}if(!r)throw new R("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function aQ(e,t,r,n,i){let{adapter:a,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json"},cookies:r},f=t.value;if(!f)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,a=await o.decode({...o,token:f,salt:r});if(!a)throw Error("Invalid JWT");let l=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),u=nE(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let i=await o.encode({...o,token:l,salt:r}),a=t.chunk(i,{expires:u});p.cookies?.push(...a),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new I(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=a,u=await r(f);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(f),u=null),u){let{user:t,session:r}=u,a=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*a,h=nE(d);o<=Date.now()&&await l({sessionToken:f,expires:h});let m=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=m,p.cookies?.push({name:e.cookies.sessionToken.name,value:f,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:m})}else f&&p.cookies?.push(...t.clean())}catch(e){l.error(new q(e))}return p}async function a0(e,t){let r,n,{logger:i,provider:a}=t,o=a.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(a.issuer),t=await nY(e,{[nU]:a[rg],[nj]:!0}),r=await n0(e,t);if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await aN.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),a.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await aC.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let p=await aI.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==a.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:o,cookies:u,provider:a}),{redirect:o.toString(),cookies:u}}async function a1(e,t){let r,{body:n}=e,{provider:i,callbacks:a,adapter:o}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:i.id};try{r=await a.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new R(e)}if(!r)throw new R("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,f=await i.generateVerificationToken?.()??rp(32),h=new Date(Date.now()+(i.maxAge??86400)*1e3),m=i.secret??t.secret,y=new URL(t.basePath,t.url.origin),b=i.sendVerificationRequest({identifier:s,token:f,expires:h,url:`${y}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:f,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),w=o.createVerificationToken?.({identifier:s,token:await rd(`${f}${m}`),expires:h});return await Promise.all([b,w]),{redirect:`${y}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function a2(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await a0(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await a1(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function a5(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){o.error(new F(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function a6(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function a3(e,t,r,n){let i=az(t),{provider:a}=i,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await a6(t,r),c=s?{user:s,exists:!0}:await a.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return aK(i,e,l,n);case"register":if("string"==typeof l?.email)return aM(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function a4(e,t){let{action:r,providerId:n,error:i,method:a}=e,o=t.skipCSRFCheck===rb,{options:s,cookies:c}=await rA({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:o}),l=new x(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===a){let t=nx({...s,query:e.query,cookies:c});switch(r){case"callback":return await aY(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await aQ(s,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await a3(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&rh(r,t),await aY(e,s,l,c);case"session":return rh(r,t),await aQ(s,l,c,!0,e.body?.data);case"signin":return rh(r,t),await a2(e,c,s);case"signout":return rh(r,t),await a5(c,l,s)}}throw new z(`Cannot handle action: ${r}`)}function a8(e,t,r,n,i){let a,o=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)a=new URL(s),o&&"/"!==o&&"/"!==a.pathname&&(a.pathname!==o&&ro(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function a9(e,t){let r=ro(t),n=await rl(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!ea&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new X(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new L("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!eo(i,r.origin))return new O(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=k(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(o&&!eo(o,r.origin))return new O(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new U(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)es=!0;else if("email"===t.type)ec=!0;else if("webauthn"===t.type){var c;if(el=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new E(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new ee("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new et(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(es){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new V("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new H("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(ec||u?.strategy==="database"||!u?.strategy&&l)if(ec){if(!l)return new $("Email login requires an adapter");d.push(...eu)}else{if(!l)return new $("Database session requires an adapter");d.push(...ed)}if(el){if(!t.experimental?.enableWebAuthn)return new ei("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new $("WebAuthn requires an adapter");d.push(...ep)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new D(`Required adapter methods were missing: ${e.join(", ")}`)}return ea||(ea=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new T(`The error page ${e?.error} should not require authentication`)),ru(nx({theme:a}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let a=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rw;try{let e=await a4(n,t);if(o)return e;let r=ru(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof E;if(i&&o&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof E&&Q.has(d.type)?d.type:"Configuration"});d instanceof C&&s.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(a)return Response.json({url:u});return Response.redirect(u)}}var a7=r(15560);function oe(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new a7.NextRequest(n.replace(i,r),e)}function ot(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||ro(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var or=r(47304),on=r(1899);async function oi(e,t){return a9(new Request(a8("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function oa(e){return"function"==typeof e}function oo(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,on.b)(),n=await e(void 0);return t?.(n),oi(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),os([n,i],a)}if(oa(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),os(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),oi(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,on.b)()).then(t=>oi(t,e).then(e=>e.json()));if(t[0]instanceof Request)return os([t[0],t[1]],e);if(oa(t[0])){let r=t[0];return async(...t)=>os(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return oi(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function os(e,t,r){let n=oe(e[0]),i=await oi(n.headers,t),a=await i.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:a}));let s=a7.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(oc.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=a,s=await r(n,e[1])??a7.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=a7.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(3760);let oc=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ol=r(34359);async function ou(e,t={},r,n){let i=new Headers(await (0,on.b)()),{redirect:a=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??i.get("Referer")??"/",l=a8("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),a&&(0,ol.redirect)(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return a&&(0,ol.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),f=await a9(p,{...n,raw:rw,skipCSRFCheck:rb}),h=await (0,or.U)();for(let e of f?.cookies??[])h.set(e.name,e.value,e.options);let m=(f instanceof Response?f.headers.get("Location"):f.redirect)??u;return a?(0,ol.redirect)(m):m}async function od(e,t){let r=new Headers(await (0,on.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=a8("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),o=await a9(a,{...t,raw:rw,skipCSRFCheck:rb}),s=await (0,or.U)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,ol.redirect)(o.redirect):o}async function op(e,t){let r=new Headers(await (0,on.b)());r.set("Content-Type","application/json");let n=new Request(a8("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await a9(n,{...t,raw:rw,skipCSRFCheck:rb}),a=await (0,or.U)();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}var of=r(70859);let{handlers:oh,auth:om,signIn:oy,signOut:ob}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return ot(r),a9(oe(t),r)};return{handlers:{GET:t,POST:t},auth:oo(e,e=>ot(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return ot(i),ou(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return ot(r),od(t,r)},unstable_update:async t=>{let r=await e(void 0);return ot(r),op(t,r)}}}ot(e);let t=t=>a9(oe(t),e);return{handlers:{GET:t,POST:t},auth:oo(e),signIn:(t,r,n)=>ou(t,r,n,e),signOut:t=>od(t,e),unstable_update:t=>op(t,e)}}({...{secret:process.env.AUTH_SECRET,providers:[function(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}}],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}},adapter:(0,of.y)(p.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:t}){let r=await p.A.user.findUnique({where:{email:t.email},include:{user_profiles:!0}});if(!r)return e;let n=r.user_profiles?.settings;return{...e,user:{...e.user,id:r.id,role:n.role??"user"}}}}});async function ow(e){let t=null;try{if(t=await om(),!t?.user?.id)throw Error("Unauthorized: No valid session found");if(!e?.endpoint||!e?.keys?.p256dh||!e?.keys?.auth)throw Error("Invalid subscription data");let{endpoint:r,keys:n}=e,{p256dh:i,auth:a}=n,o=await p.A.pushSubscription.findFirst({where:{endpoint:r,user_id:t.user.id,deleted_at:null}});if(o)return o;return await p.A.pushSubscription.create({data:{endpoint:r,p256dh:i,auth:a,user_id:t.user.id}})}catch(e){throw console.error("Error in saveSubscription:",e),Error("Failed to save push subscription. Please try again later.")}}(0,h.D)([ow]),(0,u.A)(ow,"4051fed6790d5a5fb9d91fb6a5ad52ef1f5e8cc96c",null)},21820:e=>{"use strict";e.exports=require("os")},24545:(e,t,r)=>{Promise.resolve().then(r.bind(r,41653)),Promise.resolve().then(r.bind(r,65334)),Promise.resolve().then(r.bind(r,9558)),Promise.resolve().then(r.bind(r,1305)),Promise.resolve().then(r.bind(r,96839))},25625:(e,t,r)=>{"use strict";r.d(t,{IBKRConnectionForm:()=>m});var n=r(43197),i=r(14824),a=r(43182),o=r(32748),s=r(44311),c=r(89806),l=r(12444),u=r(98100),d=r(20830),p=r(24017);let f=(0,p.createServerReference)("7fa677cfc0582c3f00b593da75a535b6ed3714b542",p.callServer,void 0,p.findSourceMapURL,"upsertUserProfileIBKRConnectionDetail");r(91565);let h=s.Ik({host:s.Yj().min(1,"Host is required"),port:s.ai().min(0,"Port must be a positive number"),clientId:s.ai().optional()});function m({userId:e}){let[t,r]=(0,i.useState)(!1),[s,p]=(0,i.useState)(!0),m=(0,o.mN)({resolver:(0,a.u)(h),defaultValues:{host:"",port:8888,clientId:void 0}});async function y(t){r(!0);try{await f({ibkrConnectionDetail:t},e),(0,d.oR)({title:"Settings updated",description:"Your IBKR connection details have been saved."})}catch(e){(0,d.oR)({title:"Error",description:"Failed to update IBKR connection details.",variant:"destructive"})}finally{r(!1)}}return s?(0,n.jsx)("div",{children:"Loading saved settings..."}):(0,n.jsx)(l.lV,{...m,children:(0,n.jsxs)("form",{onSubmit:m.handleSubmit(y),className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid gap-4",children:[(0,n.jsx)(l.zB,{control:m.control,name:"host",render:({field:e})=>(0,n.jsxs)(l.eI,{children:[(0,n.jsx)(l.lR,{children:"Host"}),(0,n.jsx)(l.MJ,{children:(0,n.jsx)(u.p,{placeholder:"localhost",...e})}),(0,n.jsx)(l.C5,{})]})}),(0,n.jsx)(l.zB,{control:m.control,name:"port",render:({field:e})=>(0,n.jsxs)(l.eI,{children:[(0,n.jsx)(l.lR,{children:"Port"}),(0,n.jsx)(l.MJ,{children:(0,n.jsx)(u.p,{type:"number",placeholder:"7496",...e,onChange:t=>e.onChange(Number(t.target.value))})}),(0,n.jsx)(l.C5,{})]})}),(0,n.jsx)(l.zB,{control:m.control,name:"clientId",render:({field:e})=>(0,n.jsxs)(l.eI,{children:[(0,n.jsx)(l.lR,{children:"Client ID (Optional)"}),(0,n.jsx)(l.MJ,{children:(0,n.jsx)(u.p,{type:"number",placeholder:"1",...e,value:e.value??"",onChange:t=>{let r=t.target.value;e.onChange(""===r?null:Number(r))}})}),(0,n.jsx)(l.C5,{})]})})]}),(0,n.jsx)(c.$,{type:"submit",disabled:t,children:t?"Saving...":"Save Connection Details"})]})})}},27774:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rootParams",{enumerable:!0,get:function(){return u}});let n=r(63620),i=r(65762),a=r(29294),o=r(63033),s=r(18435),c=r(34871),l=new WeakMap;async function u(){let e=a.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new n.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=o.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let n=t.fallbackRouteParams;if(n){let p=!1;for(let t in e)if(n.has(t)){p=!0;break}if(p){if("prerender"===r.type){let t=l.get(e);if(t)return t;let n=(0,s.makeHangingPromise)(r.renderSignal,"`unstable_rootParams`");return l.set(e,n),n}var a=e,o=n,u=t,d=r;let p=l.get(a);if(p)return p;let f={...a},h=Promise.resolve(f);return l.set(a,h),Object.keys(a).forEach(e=>{c.wellKnownProperties.has(e)||(o.has(e)?Object.defineProperty(f,e,{get(){let t=(0,c.describeStringPropertyAccess)("unstable_rootParams",e);"prerender-ppr"===d.type?(0,i.postponeWithTracking)(u.route,t,d.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(t,u,d)},enumerable:!0}):h[e]=a[e])}),h}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},27986:(e,t)=>{"use strict";t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},n=e.length;if(n<2)return r;var i=t&&t.decode||l,a=0,o=0,u=0;do{if(-1===(o=e.indexOf("=",a)))break;if(-1===(u=e.indexOf(";",a)))u=n;else if(o>u){a=e.lastIndexOf(";",o-1)+1;continue}var d=s(e,a,o),p=c(e,o,d),f=e.slice(d,p);if(!r.hasOwnProperty(f)){var h=s(e,o+1,u),m=c(e,u,h);34===e.charCodeAt(h)&&34===e.charCodeAt(m-1)&&(h++,m--);var y=e.slice(h,m);r[f]=function(e,t){try{return t(e)}catch(t){return e}}(y,i)}a=u+1}while(a<n);return r},t.l=function(e,t,s){var c=s&&s.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var l=c(t);if(!i.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(!s)return u;if(null!=s.maxAge){var d=Math.floor(s.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(s.domain){if(!a.test(s.domain))throw TypeError("option domain is invalid");u+="; Domain="+s.domain}if(s.path){if(!o.test(s.path))throw TypeError("option path is invalid");u+="; Path="+s.path}if(s.expires){var p,f=s.expires;if(p=f,"[object Date]"!==r.call(p)||isNaN(f.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+f.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():s.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,n=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/;function s(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function l(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u},RedirectType:function(){return i.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(15164),i=r(90773),a=r(70556),o=r(77539),s=r(68294),c=r(43754);class l extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class u extends URLSearchParams{append(){throw new l}delete(){throw new l}set(){throw new l}sort(){throw new l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34871:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return a}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},34937:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return d}});let n=r(6121),i=r(29303),a=r(54247),o=r(71370),s=r(6121),c=Symbol("internal response"),l=new Set([301,302,303,307,308]);function u(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,l=new Proxy(new s.ResponseCookies(r),{get(e,i,a){switch(i){case"delete":case"set":return(...a)=>{let o=Reflect.apply(e[i],e,a),c=new Headers(r);return o instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,n.stringifyCookie)(e)).join(",")),u(t,c),o};default:return o.ReflectAdapter.get(e,i,a)}}});this[c]={cookies:l,url:t.url?new i.NextURL(t.url,{headers:(0,a.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[c].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!l.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",(0,a.validateURL)(e)),new d(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,a.validateURL)(e)),u(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),u(e,t),new d(null,{...e,headers:t})}}},36635:(e,t,r)=>{"use strict";r.d(t,{UserRoleDisplay:()=>d});var n=r(43197),i=r(14824),a=r(64357),o=r(89806),s=r(38042),c=r(37534),l=r(24017);let u=(0,l.createServerReference)("7f2cceb82f3528bdce95a7fec1001f3e3f060119b1",l.callServer,void 0,l.findSourceMapURL,"upsertUserRole");function d({userId:e}){let[t,r]=(0,i.useState)(!1),[l,d]=(0,i.useState)("user"),p=async t=>{try{await u({role:t},e),d(t),r(!1)}catch(e){console.error("Failed to update role:",e)}};return(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(a.E,{variant:"admin"===l?"default":"secondary",className:"text-sm",children:l.charAt(0).toUpperCase()+l.slice(1)}),"admin"===l&&(0,n.jsxs)(s.lG,{open:t,onOpenChange:r,children:[(0,n.jsx)(s.zM,{asChild:!0,children:(0,n.jsx)(o.$,{variant:"outline",size:"sm",children:"Change Role"})}),(0,n.jsxs)(s.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsx)(s.c7,{children:(0,n.jsx)(s.L3,{children:"Change User Role"})}),(0,n.jsx)("div",{className:"grid gap-4 py-4",children:(0,n.jsxs)(c.l6,{value:l,onValueChange:e=>p(e),children:[(0,n.jsx)(c.bq,{className:"w-full",children:(0,n.jsx)(c.yv,{placeholder:"Select role"})}),(0,n.jsxs)(c.gC,{children:[(0,n.jsx)(c.eb,{value:"user",children:"User"}),(0,n.jsx)(c.eb,{value:"admin",children:"Admin"})]})]})})]})]})]})}r(91565)},37697:(e,t,r)=>{Promise.resolve().then(r.bind(r,36635)),Promise.resolve().then(r.bind(r,12978)),Promise.resolve().then(r.bind(r,25625)),Promise.resolve().then(r.bind(r,19246)),Promise.resolve().then(r.bind(r,10402))},41653:(e,t,r)=>{"use strict";r.d(t,{UserRoleDisplay:()=>n});let n=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call UserRoleDisplay() from the server but UserRoleDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/_components/user-role-display.tsx","UserRoleDisplay")},42232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(47394),t)},43456:(e,t,r)=>{"use strict";e.exports=r(79229)},43754:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(20165).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45023:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(24017);let i=(0,n.createServerReference)("40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",n.callServer,void 0,n.findSourceMapURL,"testIBKRConnection")},46377:(e,t)=>{"use strict";function r(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},47304:(e,t,r)=>{"use strict";Object.defineProperty(t,"U",{enumerable:!0,get:function(){return p}});let n=r(90776),i=r(6121),a=r(29294),o=r(63033),s=r(65762),c=r(75802),l=r(18435),u=r(50305),d=(r(23710),r(69166));function p(){let e="cookies",t=a.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new i.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var u=t.route,p=r;let e=f.get(p);if(e)return e;let n=(0,l.makeHangingPromise)(p.renderSignal,"`cookies()`");return f.set(p,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},size:{get(){let e="`cookies().size`",t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${m(arguments[0])})\``;let t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${m(arguments[0])})\``;let t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${m(arguments[0])})\``;let t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${m(t)}, ...)\``:"`cookies().set(...)`"}let t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${m(arguments[0])})\``:`\`cookies().delete(${m(arguments[0])}, ...)\``;let t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},clear:{value:function(){let e="`cookies().clear()`",t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}},toString:{value:function(){let e="`cookies().toString()`",t=b(u,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,p)}}}),n}else"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r);(0,s.trackDynamicDataInDynamicRender)(t,r)}let y=(0,o.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(y)?y.userspaceMutableCookies:y.cookies)}let f=new WeakMap;function h(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):w.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):g.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function m(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let y=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(b);function b(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function w(){return this.getAll().map(e=>[e.name,e]).values()}function g(e){for(let e of this.getAll())this.delete(e.name);return e}},47394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"after",{enumerable:!0,get:function(){return i}});let n=r(29294);function i(e){let t=n.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}},48161:e=>{"use strict";e.exports=require("node:os")},49005:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(60187),i=r(90773);function a(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50305:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(79975));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let a={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}o(e=>{try{s(a.current)}finally{a.current=null}})},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},56187:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57975:e=>{"use strict";e.exports=require("node:util")},58505:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},60187:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61241:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return i},userAgent:function(){return o},userAgentFromString:function(){return a}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(75648));function i(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function a(e){return{...(0,n.default)(e),isBot:void 0!==e&&i(e)}}function o({headers:e}){return a(e.get("user-agent")||void 0)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63843:(e,t,r)=>{"use strict";r.d(t,{M:()=>a,f:()=>o});var n=r(31595),i=r(90742);async function a(e){try{let t=await (0,n.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return new i.IBApi({clientId:r,host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port})}catch(e){throw console.error("Error creating IBKR connection:",e),e}}async function o(e){try{let t=await (0,n.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=new i.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port}),a=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return{ibApiNext:r,clientId:a}}catch(e){throw console.error("Error creating IBKR connection:",e),e}}},65334:(e,t,r)=>{"use strict";r.d(t,{IBAccountDetails:()=>n});let n=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call IBAccountDetails() from the server but IBAccountDetails is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/IBAccountDetails.tsx","IBAccountDetails")},65379:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"connection",{enumerable:!0,get:function(){return l}});let n=r(29294),i=r(63033),a=r(65762),o=r(75802),s=r(18435),c=r(69166);function l(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return(0,s.makeHangingPromise)(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("connection",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return Promise.resolve(void 0)}},66034:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>c,TN:()=>u,XL:()=>l});var n=r(43197),i=r(14824),a=r(71001),o=r(51001);let s=(0,a.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=i.forwardRef(({className:e,variant:t,...r},i)=>(0,n.jsx)("div",{ref:i,role:"alert",className:(0,o.cn)(s({variant:t}),e),...r}));c.displayName="Alert";let l=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...t}));l.displayName="AlertTitle";let u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...t}));u.displayName="AlertDescription"},68294:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(60187).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let n=r(75802),i=r(3295);function a(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},69583:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(20349);r(71241);var i=r(63843);async function a(e){try{let{ibApiNext:t,clientId:r}=await (0,i.f)(e);return new Promise((e,r)=>{let n=setTimeout(()=>r(Error("Connection timeout")),3e3);t.connect().getAccountSummary("All","NetLiquidation").subscribe({next:()=>{clearTimeout(n),e(!0),t.disconnect()},error:e=>{clearTimeout(n),r(e),t.disconnect()}}).add(()=>{t.isConnected&&(t.disconnect(),console.log("Disconnected from IBKR"))})})}catch(e){return console.error("Error testing IBKR connection:",e),!1}}(0,r(68785).D)([a]),(0,n.A)(a,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",null)},70556:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(60187).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70859:(e,t)=>{"use strict";t.y=void 0,t.y=function(e){return{createUser:t=>e.user.create({data:t}),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){var r;let n=await e.account.findUnique({where:{provider_providerAccountId:t},select:{user:!0}});return null!=(r=null==n?void 0:n.user)?r:null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},data:r}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...i}=r;return{user:n,session:i}},createSession:t=>e.session.create({data:t}),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},data:t}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create({data:t});return r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return r.id&&delete r.id,r}catch(e){if("P2025"===e.code)return null;throw e}}}}},71370:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},73024:e=>{"use strict";e.exports=require("node:fs")},75648:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(o,s){"use strict";var c="function",l="undefined",u="object",d="string",p="major",f="model",h="name",m="type",y="vendor",b="version",w="architecture",g="console",v="mobile",_="tablet",k="smarttv",x="wearable",E="embedded",S="Amazon",A="Apple",R="ASUS",P="BlackBerry",T="Browser",j="Chrome",O="Firefox",C="Google",U="Huawei",N="Microsoft",I="Motorola",$="Opera",D="Samsung",H="Sharp",L="Sony",W="Xiaomi",M="Zebra",K="Facebook",q="Chromium OS",J="Mac OS",B=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},F=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},z=function(e,t){return typeof e===d&&-1!==V(t).indexOf(V(e))},V=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,a,o,l,d=0;d<t.length&&!o;){var p=t[d],f=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(i=0;i<f.length;i++)l=o[++n],typeof(a=f[i])===u&&a.length>0?2===a.length?typeof a[1]==c?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==c||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):void 0:this[a[0]]=l?a[1].call(this,l,a[2]):void 0:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):s):this[a]=l||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(z(t[r][n],e))return"?"===r?s:r}else if(z(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,b],[/opios[\/ ]+([\w\.]+)/i],[b,[h,$+" Mini"]],[/\bopr\/([\w\.]+)/i],[b,[h,$]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,b],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[h,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[b,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[b,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+T],b],[/\bfocus\/([\w\.]+)/i],[b,[h,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[h,$+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[h,$+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[h,"MIUI "+T]],[/fxios\/([-\w\.]+)/i],[b,[h,O]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+T]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+T],b],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,b],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,K],b],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[h,j+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,j+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[h,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[b,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[h,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,b],[/(cobalt)\/([\w\.]+)/i],[h,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[w,"amd64"]],[/(ia32(?=;))/i],[[w,V]],[/((?:i[346]|x)86)[;\)]/i],[[w,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[w,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[w,"armhf"]],[/windows (ce|mobile); ppc;/i],[[w,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[w,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[w,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[w,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[y,D],[m,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[y,D],[m,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[y,A],[m,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[y,A],[m,_]],[/(macintosh);/i],[f,[y,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[y,H],[m,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[y,U],[m,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[y,U],[m,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[y,W],[m,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[y,W],[m,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[y,"OPPO"],[m,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[y,"Vivo"],[m,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[y,"Realme"],[m,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[y,I],[m,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[y,I],[m,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[y,"LG"],[m,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[y,"LG"],[m,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[y,"Lenovo"],[m,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[y,"Nokia"],[m,v]],[/(pixel c)\b/i],[f,[y,C],[m,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[y,C],[m,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[y,L],[m,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[y,L],[m,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[y,"OnePlus"],[m,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[y,S],[m,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[y,S],[m,v]],[/(playbook);[-\w\),; ]+(rim)/i],[f,y,[m,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[y,P],[m,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[y,R],[m,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[y,R],[m,v]],[/(nexus 9)/i],[f,[y,"HTC"],[m,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[f,/_/g," "],[m,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[y,"Acer"],[m,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[y,"Meizu"],[m,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,f,[m,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,f,[m,_]],[/(surface duo)/i],[f,[y,N],[m,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[y,"Fairphone"],[m,v]],[/(u304aa)/i],[f,[y,"AT&T"],[m,v]],[/\bsie-(\w*)/i],[f,[y,"Siemens"],[m,v]],[/\b(rct\w+) b/i],[f,[y,"RCA"],[m,_]],[/\b(venue[\d ]{2,7}) b/i],[f,[y,"Dell"],[m,_]],[/\b(q(?:mv|ta)\w+) b/i],[f,[y,"Verizon"],[m,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[y,"Barnes & Noble"],[m,_]],[/\b(tm\d{3}\w+) b/i],[f,[y,"NuVision"],[m,_]],[/\b(k88) b/i],[f,[y,"ZTE"],[m,_]],[/\b(nx\d{3}j) b/i],[f,[y,"ZTE"],[m,v]],[/\b(gen\d{3}) b.+49h/i],[f,[y,"Swiss"],[m,v]],[/\b(zur\d{3}) b/i],[f,[y,"Swiss"],[m,_]],[/\b((zeki)?tb.*\b) b/i],[f,[y,"Zeki"],[m,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],f,[m,_]],[/\b(ns-?\w{0,9}) b/i],[f,[y,"Insignia"],[m,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[y,"NextBook"],[m,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],f,[m,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],f,[m,v]],[/\b(ph-1) /i],[f,[y,"Essential"],[m,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[y,"Envizen"],[m,_]],[/\b(trio[-\w\. ]+) b/i],[f,[y,"MachSpeed"],[m,_]],[/\btu_(1491) b/i],[f,[y,"Rotor"],[m,_]],[/(shield[\w ]+) b/i],[f,[y,"Nvidia"],[m,_]],[/(sprint) (\w+)/i],[y,f,[m,v]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[y,N],[m,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[y,M],[m,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[y,M],[m,v]],[/smart-tv.+(samsung)/i],[y,[m,k]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[y,D],[m,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[m,k]],[/(apple) ?tv/i],[y,[f,A+" TV"],[m,k]],[/crkey/i],[[f,j+"cast"],[y,C],[m,k]],[/droid.+aft(\w)( bui|\))/i],[f,[y,S],[m,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[y,H],[m,k]],[/(bravia[\w ]+)( bui|\))/i],[f,[y,L],[m,k]],[/(mitv-\w{5}) bui/i],[f,[y,W],[m,k]],[/Hbbtv.*(technisat) (.*);/i],[y,f,[m,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,G],[f,G],[m,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,f,[m,g]],[/droid.+; (shield) bui/i],[f,[y,"Nvidia"],[m,g]],[/(playstation [345portablevi]+)/i],[f,[y,L],[m,g]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[y,N],[m,g]],[/((pebble))app/i],[y,f,[m,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[y,A],[m,x]],[/droid.+; (glass) \d/i],[f,[y,C],[m,x]],[/droid.+; (wt63?0{2,3})\)/i],[f,[y,M],[m,x]],[/(quest( 2| pro)?)/i],[f,[y,K],[m,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[m,E]],[/(aeobc)\b/i],[f,[y,S],[m,E]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[m,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[m,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,v]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,b],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[b,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[b,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,J],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,b],[/\(bb(10);/i],[b,[h,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[h,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[h,j+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,q],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,b],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,b]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==l&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,a=t?B(Q,t):Q,g=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[b]=s,X.call(t,n,a.browser),t[p]=typeof(e=t[b])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,g&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[w]=s,X.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[f]=s,e[m]=s,X.call(e,n,a.device),g&&!e[m]&&i&&i.mobile&&(e[m]=v),g&&"Macintosh"==e[f]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[m]=_),e},this.getEngine=function(){var e={};return e[h]=s,e[b]=s,X.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[h]=s,e[b]=s,X.call(e,n,a.os),g&&!e[h]&&i&&"Unknown"!=i.platform&&(e[h]=i.platform.replace(/chrome os/i,q).replace(/macos/i,J)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=F([h,b,p]),ee.CPU=F([w]),ee.DEVICE=F([f,y,m,g,v,k,_,x,E]),ee.ENGINE=ee.OS=F([h,b]),typeof a!==l?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==l&&(o.UAParser=ee);var et=typeof o!==l&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete a[e]}return r.exports}o.ab=__dirname+"/",e.exports=o(226)})()},76658:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>u,ZB:()=>c,Zp:()=>o,aR:()=>s});var n=r(52927),i=r(60154),a=r(83687);let o=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let s=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));s.displayName="CardHeader";let c=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let l=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent",i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},76760:e=>{"use strict";e.exports=require("node:path")},77418:(e,t,r)=>{"use strict";r.d(t,{Gk:()=>c,Ph:()=>l,lu:()=>s});var n=r(20349);r(71241);var i=r(80336),a=r(31595),o=r(68785);let s=async(e,t)=>{try{let r=await (0,a.V)(t);if(!r)return await i.A.userProfile.create({data:{user_id:t,settings:JSON.parse(JSON.stringify({ibkrConnectionDetail:e.ibkrConnectionDetail}))}});let n=JSON.parse(JSON.stringify({...r.settings,ibkrConnectionDetail:e.ibkrConnectionDetail}));return await i.A.userProfile.update({where:{user_id:t},data:{settings:n}})}catch(e){throw console.error("Error upserting user profile settings",e),e}},c=async(e,t)=>{try{let r=await (0,a.V)(t);if(!r)throw Error("User profile not found");let n=JSON.parse(JSON.stringify({...r.settings,riskSignalSettings:e.riskSignalSettings}));return await i.A.userProfile.update({where:{user_id:t},data:{settings:n}})}catch(e){throw console.error("Error upserting user profile settings",e),e}},l=async(e,t)=>{try{let r=await (0,a.V)(t);if(!r)throw Error("User profile not found");let n=JSON.parse(JSON.stringify({...r.settings,defaultIBAccountId:e.defaultIBAccountId,enableAutoOrderPrefill:e.enableAutoOrderPrefill}));return await i.A.userProfile.update({where:{user_id:t},data:{settings:n}})}catch(e){throw console.error("Error upserting user profile settings",e),e}};(0,o.D)([s,c,l]),(0,n.A)(s,"7fa677cfc0582c3f00b593da75a535b6ed3714b542",null),(0,n.A)(c,"7f76ddcec25d3e0083a5eedf540fa563cbecbb815d",null),(0,n.A)(l,"7fefb8e30cab7d78730e8f450e790bc532ceaea9ce",null)},77539:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(60187).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77598:e=>{"use strict";e.exports=require("node:crypto")},77703:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(44736).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},78474:e=>{"use strict";e.exports=require("node:events")},79229:(e,t,r)=>{"use strict";var n=r(14824),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,c=n.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,u=n[1];return s(function(){i.value=r,i.getSnapshot=t,l(i)&&u({inst:i})},[e,r,t]),o(function(){return l(i)&&u({inst:i}),e(function(){l(i)&&u({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},79551:e=>{"use strict";e.exports=require("url")},81643:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return a},ReadonlyHeadersError:function(){return i}});let n=r(71370);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==o)return n.ReflectAdapter.get(t,o,i)},set(t,r,i,a){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,a);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,s??r,i,a)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==a&&n.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===a||n.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},82366:(e,t,r)=>{"use strict";r.d(t,{d:()=>s});var n=r(43197),i=r(14824),a=r(59537),o=r(51001);let s=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)(a.bL,{className:(0,o.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:(0,n.jsx)(a.zi,{className:(0,o.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));s.displayName=a.bL.displayName},83687:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(21749),i=r(66501);function a(...e){return(0,i.QP)((0,n.$)(e))}},90773:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return a},isRedirectError:function(){return o}});let n=r(56187),i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return p},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return u},responseCookiesToRequestCookies:function(){return y},wrapWithMutableAccessCheck:function(){return f}});let n=r(6121),i=r(71370),a=r(29294),o=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),a=i.getAll();for(let e of r)i.set(e);for(let e of a)i.set(e);return!0}class p{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],s=new Set,c=()=>{let e=a.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case l:return o;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{c()}};default:return i.ReflectAdapter.get(e,t,r)}}});return u}}function f(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return i.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function m(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new s}function y(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},91565:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var n=r(24017);let i=(0,n.createServerReference)("40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",n.callServer,void 0,n.findSourceMapURL,"getUserProfile")},91645:e=>{"use strict";e.exports=require("net")},92386:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},95311:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var n=r(43197),i=r(51001);function a({className:e,...t}){return(0,n.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},96839:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>i,AvatarFallback:()=>o,AvatarImage:()=>a});var n=r(21601);let i=(0,n.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/avatar.tsx","Avatar"),a=(0,n.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/avatar.tsx","AvatarImage"),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/avatar.tsx","AvatarFallback")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,4923,9292,575,6765,1841,3265,7216,4515,5669],()=>r(8886));module.exports=n})();