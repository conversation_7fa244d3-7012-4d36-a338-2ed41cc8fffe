(()=>{var e={};e.id=17,e.ids=[17],e.modules={416:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>o,wL:()=>u});var a=r(43197),s=r(14824),n=r(51001);let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent";let u=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,t,r)=>{"use strict";r.d(t,{j2:()=>c,Y9:()=>d,Jv:()=>l});var a=r(74723),s=r(89886),n=r(68941),i=r(30935);let o={...{secret:process.env.AUTH_SECRET,providers:[i.A],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}},adapter:(0,s.y)(n.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:t}){let r=await n.A.user.findUnique({where:{email:t.email},include:{user_profiles:!0}});if(!r)return e;let a=r.user_profiles?.settings;return{...e,user:{...e.user,id:r.id,role:a.role??"user"}}}}},{handlers:d,auth:c,signIn:l,signOut:u}=(0,a.Ay)(o)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31595:(e,t,r)=>{"use strict";r.d(t,{V:()=>n,e:()=>i});var a=r(20349);r(71241);var s=r(80336);async function n(e){let t=await s.A.userProfile.findUnique({where:{user_id:e}});return t?{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}:null}async function i(e,t){let r=await s.A.userProfile.update({where:{user_id:e},data:{first_name:t.firstName,last_name:t.lastName,settings:JSON.parse(JSON.stringify(t.settings)),updated_at:new Date}});return{userId:r.user_id,firstName:r.first_name||"",lastName:r.last_name||"",createdAt:r.created_at,updatedAt:r.updated_at,deletedAt:r.deleted_at,settings:r.settings}}(0,r(68785).D)([n,i]),(0,a.A)(n,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,a.A)(i,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},33873:e=>{"use strict";e.exports=require("path")},34108:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/_components/account-details.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/_components/account-details.tsx","default")},36729:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"60dd8e3768323d7f825691f04bb4127d9750fa06af":()=>u});var a=r(20349);r(71241);var s=r(63843),n=r(90742);class i extends Error{constructor(e="Operation timed out"){super(e),this.name="TimeoutError"}}let o={API_CALL:1e4},d=(e,t=o.API_CALL,r)=>Promise.race([e,new Promise((e,a)=>setTimeout(()=>a(new i(`Operation ${r?`'${r}'`:""} timed out after ${t}ms`)),t))]),c=(e,t,r=o.API_CALL)=>d(e,r,`IBKR ${t}`);class l extends Error{constructor(e){super(e),this.name="IBConnectionError"}}async function u(e,t){return c((async()=>new Promise(async(r,a)=>{let i=[],o=0;try{let d=await (0,s.M)(t);d.once(n.EventName.connected,()=>{d.reqAccountUpdates(!0,e)}),d.on(n.EventName.error,(e,t,r)=>{d.disconnect(),a(new l(`IB API Error ${t}: ${e.message}`))}),d.on(n.EventName.updateAccountValue,(t,r,a,s)=>{if(s===e)switch(console.log(`Received key: ${t}, value: ${r}, currency: ${a}`),t){case"CashBalance":case"AvailableFunds":case"NetLiquidation":case"TotalCashValue":let n=parseFloat(r);if(o&&n!==o){let e=n-o;i.push({id:`${Date.now()}-${i.length}`,type:e>=0?"deposit":"withdrawal",amount:Math.abs(e),date:new Date().toISOString(),status:"completed",currency:a||"USD"})}o||(o=n);break;case"Deposits":case"Payment":case"Transfer":let d=parseFloat(r);isNaN(d)||0===d||i.push({id:`${Date.now()}-${i.length}`,type:d>=0?"deposit":"withdrawal",amount:Math.abs(d),date:new Date().toISOString(),status:"completed",currency:a||"USD"})}}),d.on(n.EventName.accountDownloadEnd,t=>{if(t===e){d.disconnect();let e=i.filter((e,t,r)=>t===r.findIndex(t=>t.amount===e.amount&&t.type===e.type&&1e3>Math.abs(new Date(t.date).getTime()-new Date(e.date).getTime())));r(e)}}),d.connect()}catch(e){a(new l(e instanceof Error?e.message:"Unknown error occurred"))}}))(),"Get Account Transactions",1e4)}(0,r(68785).D)([u]),(0,a.A)(u,"60dd8e3768323d7f825691f04bb4127d9750fa06af",null)},39832:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>d,Hj:()=>c,XI:()=>i,nA:()=>u,nd:()=>l});var a=r(43197),s=r(14824),n=r(51001);let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));d.displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));l.displayName="TableHead";let u=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},43169:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(99292);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},51948:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(14824);function s(e){let[t,r]=(0,a.useState)(!1);return t}},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},62655:(e,t,r)=>{Promise.resolve().then(r.bind(r,34108))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63843:(e,t,r)=>{"use strict";r.d(t,{M:()=>n,f:()=>i});var a=r(31595),s=r(90742);async function n(e){try{let t=await (0,a.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return new s.IBApi({clientId:r,host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port})}catch(e){throw console.error("Error creating IBKR connection:",e),e}}async function i(e){try{let t=await (0,a.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=new s.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port}),n=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return{ibApiNext:r,clientId:n}}catch(e){throw console.error("Error creating IBKR connection:",e),e}}},66034:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>l,XL:()=>c});var a=r(43197),s=r(14824),n=r(71001),i=r(51001);let o=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=s.forwardRef(({className:e,variant:t,...r},s)=>(0,a.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(o({variant:t}),e),...r}));d.displayName="Alert";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t}));c.displayName="AlertTitle";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));l.displayName="AlertDescription"},68941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(67566);let s=globalThis.__prisma||new a.PrismaClient},73024:e=>{"use strict";e.exports=require("node:fs")},76658:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>o});var a=r(52927),s=r(60154),n=r(83687);let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(51291);let s=globalThis.__prisma||new a.PrismaClient},83687:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(21749),s=r(66501);function n(...e){return(0,s.QP)((0,a.$)(e))}},90590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var a=r(42585),s=r(59246),n=r(63528),i=r.n(n),o=r(83599),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["trading-account",{children:["[accountId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90874)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/[accountId]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/[accountId]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/trading-account/[accountId]/page",pathname:"/trading-account/[accountId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},90874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(52927),s=r(52058),n=r(34108),i=r(13317),o=r(76658);async function d({params:e}){let{accountId:t}=await e,r=await (0,i.j2)();return r?(0,a.jsx)("div",{className:"container mx-auto py-6 px-4",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Trading Account"}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Account ID: ",t]})]}),(0,a.jsx)(o.Zp,{className:"p-6",children:(0,a.jsx)(n.default,{accountId:t,userId:r.user.id})})]})}):(0,s.redirect)("/")}},91645:e=>{"use strict";e.exports=require("net")},97223:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var a=r(43197),s=r(416),n=r(39832),i=r(51948),o=r(24017);let d=(0,o.createServerReference)("60dd8e3768323d7f825691f04bb4127d9750fa06af",o.callServer,void 0,o.findSourceMapURL,"getAccountTransactions");var c=r(14824);class l extends Error{constructor(e){super(e),this.name="IBConnectionError"}}var u=r(45806),p=r(44736);let m=(0,p.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),f=(0,p.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var x=r(66034),h=r(60880),g=r.n(h);let v=({accountId:e,userId:t})=>{let[r,o]=(0,c.useState)([]),[p,h]=(0,c.useState)(!0),[v,y]=(0,c.useState)(null),w=(0,i.U)("(min-width: 768px)");return((0,c.useEffect)(()=>{(async()=>{try{h(!0),y(null);let r=await d(e,t);o(r)}catch(t){let e="Failed to fetch transactions";t instanceof l?e=`IB Connection Error: ${t.message}`:t instanceof Error&&(e=t.message),y(e)}finally{h(!1)}})()},[e,t]),p)?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(u.A,{className:"h-8 w-8 animate-spin text-primary"})}):v?(0,a.jsxs)(x.Fc,{variant:"destructive",children:[(0,a.jsx)(m,{className:"h-4 w-4"}),(0,a.jsx)(x.XL,{children:"Error"}),(0,a.jsx)(x.TN,{children:v})]}):0===r.length?(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No transactions found for this account."}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)(g(),{href:"/portfolio",className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,a.jsx)(f,{className:"h-4 w-4 mr-1"}),"Back to Portfolio"]})}),(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Transaction History"})]})}),w?(0,a.jsxs)(n.XI,{children:[(0,a.jsx)(n.A0,{children:(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nd,{children:"Date"}),(0,a.jsx)(n.nd,{children:"Type"}),(0,a.jsx)(n.nd,{children:"Amount"}),(0,a.jsx)(n.nd,{children:"Currency"}),(0,a.jsx)(n.nd,{children:"Status"})]})}),(0,a.jsx)(n.BF,{children:r.map(e=>(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nA,{children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)(n.nA,{className:"capitalize",children:e.type}),(0,a.jsx)(n.nA,{children:(0,a.jsxs)("span",{className:"withdrawal"===e.type?"text-red-500":"text-green-500",children:["withdrawal"===e.type?"-":"+","$",e.amount.toFixed(2)]})}),(0,a.jsx)(n.nA,{children:e.currency}),(0,a.jsx)(n.nA,{className:"capitalize",children:e.status})]},e.id))})]}):(0,a.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,a.jsxs)(s.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"capitalize font-medium",children:e.type}),(0,a.jsxs)("span",{className:`font-bold ${"withdrawal"===e.type?"text-red-500":"text-green-500"}`,children:["withdrawal"===e.type?"-":"+","$",e.amount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)("span",{children:e.currency}),(0,a.jsx)("span",{className:"capitalize",children:e.status})]})]},e.id))})]})}},99607:(e,t,r)=>{Promise.resolve().then(r.bind(r,97223))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,4923,9292,575,6765,3265,4515],()=>r(90590));module.exports=a})();