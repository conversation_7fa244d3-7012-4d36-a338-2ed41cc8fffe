(()=>{var e={};e.id=6478,e.ids=[6478],e.modules={416:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>u});var s=r(43197),a=r(14824),o=r(51001);let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},10013:(e,t,r)=>{"use strict";r.d(t,{Lt:()=>I,Rx:()=>z,Zr:()=>W,EO:()=>q,$v:()=>$,ck:()=>U,wd:()=>O,r7:()=>L,tv:()=>M});var s=r(43197),a=r(14824),o=r(6125),i=r(15167),n=r(65264),l=r(57535),d=r(65443),c="AlertDialog",[u,f]=(0,o.A)(c,[n.Hs]),p=(0,n.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,s.jsx)(n.bL,{...a,...r,modal:!0})};m.displayName=c;var h=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,o=p(r);return(0,s.jsx)(n.l9,{...o,...a,ref:t})});h.displayName="AlertDialogTrigger";var x=e=>{let{__scopeAlertDialog:t,...r}=e,a=p(t);return(0,s.jsx)(n.ZL,{...a,...r})};x.displayName="AlertDialogPortal";var g=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,o=p(r);return(0,s.jsx)(n.hJ,{...o,...a,ref:t})});g.displayName="AlertDialogOverlay";var b="AlertDialogContent",[v,y]=u(b),w=(0,d.Dc)("AlertDialogContent"),N=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:o,...d}=e,c=p(r),u=a.useRef(null),f=(0,i.s)(t,u),m=a.useRef(null);return(0,s.jsx)(n.G$,{contentName:b,titleName:j,docsSlug:"alert-dialog",children:(0,s.jsx)(v,{scope:r,cancelRef:m,children:(0,s.jsxs)(n.UC,{role:"alertdialog",...c,...d,ref:f,onOpenAutoFocus:(0,l.m)(d.onOpenAutoFocus,e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(w,{children:o}),(0,s.jsx)(S,{contentRef:u})]})})})});N.displayName=b;var j="AlertDialogTitle",k=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,o=p(r);return(0,s.jsx)(n.hE,{...o,...a,ref:t})});k.displayName=j;var A="AlertDialogDescription",R=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,o=p(r);return(0,s.jsx)(n.VY,{...o,...a,ref:t})});R.displayName=A;var _=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,o=p(r);return(0,s.jsx)(n.bm,{...o,...a,ref:t})});_.displayName="AlertDialogAction";var C="AlertDialogCancel",E=a.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...a}=e,{cancelRef:o}=y(C,r),l=p(r),d=(0,i.s)(t,o);return(0,s.jsx)(n.bm,{...l,...a,ref:d})});E.displayName=C;var S=({contentRef:e})=>{let t=`\`${b}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${b}\` by passing a \`${A}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${b}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return a.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},D=r(51001),T=r(89806);let I=m,M=h,P=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(g,{className:(0,D.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));P.displayName=g.displayName;let q=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(x,{children:[(0,s.jsx)(P,{}),(0,s.jsx)(N,{ref:r,className:(0,D.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));q.displayName=N.displayName;let O=({className:e,...t})=>(0,s.jsx)("div",{className:(0,D.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});O.displayName="AlertDialogHeader";let U=({className:e,...t})=>(0,s.jsx)("div",{className:(0,D.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});U.displayName="AlertDialogFooter";let L=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(k,{ref:r,className:(0,D.cn)("text-lg font-semibold",e),...t}));L.displayName=k.displayName;let $=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(R,{ref:r,className:(0,D.cn)("text-sm text-muted-foreground",e),...t}));$.displayName=R.displayName;let z=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(_,{ref:r,className:(0,D.cn)((0,T.r)(),e),...t}));z.displayName=_.displayName;let W=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(E,{ref:r,className:(0,D.cn)((0,T.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));W.displayName=E.displayName},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"40070bd324e71428f5da05bccd7818bdf7d2e9fb50":()=>s.Q2,"40130103a12210564b9cc2eb00ee5bfd15ed9e9469":()=>a.R,"4076ae6917a9197faf33016ff9b25083685fba3edd":()=>s.CP,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c":()=>i.F,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665":()=>o.V,"604b184f55fb3c880747ad4d6ee2ff556c780bbbca":()=>s.Qv,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e":()=>o.e,"60fe6244bf31f4bd3601e492f7ad4652260e84cac1":()=>s.w7});var s=r(38625),a=r(32310),o=r(43806),i=r(80322)},13317:(e,t,r)=>{"use strict";r.d(t,{j2:()=>d,Y9:()=>l,Jv:()=>c});var s=r(74723),a=r(89886),o=r(68941),i=r(30935);let n={...{secret:process.env.AUTH_SECRET,providers:[i.A],callbacks:{authorized:({auth:e,request:{nextUrl:t}})=>!!e?.user}},adapter:(0,a.y)(o.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:t}){let r=await o.A.user.findUnique({where:{email:t.email},include:{user_profiles:!0}});if(!r)return e;let s=r.user_profiles?.settings;return{...e,user:{...e.user,id:r.id,role:s.role??"user"}}}}},{handlers:l,auth:d,signIn:c,signOut:u}=(0,s.Ay)(n)},16386:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(60154);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:o="",children:i,iconNode:c,...u},f)=>(0,s.createElement)("svg",{ref:f,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",o),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...o},l)=>(0,s.createElement)(c,{ref:l,iconNode:t,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,r),...o}));return r.displayName=i(e),r}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19647:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var s=r(43197);let a=(0,r(44736).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var o=r(89806),i=r(14824),n=r(38042),l=r(98100),d=r(86389),c=r(20830);function u({open:e,onOpenChange:t,userId:r}){let[a,u]=(0,i.useState)(""),[f,p]=(0,i.useState)(!1),{toast:m}=(0,c.dj)(),h=async e=>{e.preventDefault(),p(!0);try{await (0,d.w)(a.toUpperCase(),r),t(!1),u(""),m({title:"Success",description:`${a.toUpperCase()} has been added to your watchlist`})}catch(e){m({title:"Error",description:e instanceof Error?e.message:"Failed to add stock to watchlist",variant:"destructive"})}finally{p(!1)}};return(0,s.jsx)(n.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(n.Cf,{children:[(0,s.jsx)(n.c7,{children:(0,s.jsx)(n.L3,{children:"Add Stock to Watchlist"})}),(0,s.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,s.jsx)(l.p,{placeholder:"Enter stock ticker (e.g., AAPL)",value:a,onChange:e=>u(e.target.value.trim()),className:"uppercase",minLength:1,maxLength:5,required:!0}),(0,s.jsx)(o.$,{type:"submit",className:"w-full",disabled:!a.trim()||f,children:f?"Adding...":"Add to Watchlist"})]})]})})}function f({userId:e}){let[t,r]=(0,i.useState)(!1);return(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Watchlist"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Track your favorite stocks"})]}),(0,s.jsxs)(o.$,{onClick:()=>r(!0),className:"w-full sm:w-auto",children:[(0,s.jsx)(a,{className:"w-4 h-4 mr-2"}),"Add Stock"]}),(0,s.jsx)(u,{open:t,onOpenChange:r,userId:e})]})}},20830:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f,oR:()=>u});var s=r(14824);let a=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=n(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",...void 0!==e&&{toastId:e}})}}},21820:e=>{"use strict";e.exports=require("os")},22525:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistGrid.tsx","default")},24017:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return s.callServer},createServerReference:function(){return o},findSourceMapURL:function(){return a.findSourceMapURL}});let s=r(89562),a=r(41890),o=r(54183).createServerReference},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32310:(e,t,r)=>{"use strict";r.d(t,{R:()=>l});var s=r(49068);r(77048);var a=r(76977),o=r(55830),i=r(91581),n=r(80322);async function l(e){try{if(!await (0,n.F)(e))return console.log("IBKR not connected"),null;let t=await (0,o.M)(e),r=[],s=new Promise((e,s)=>{let o=setTimeout(()=>{t.disconnect(),s(Error("Timeout waiting for positions data"))},1e4);t.on(a.EventName.error,(e,t,r)=>{console.error(`IBKR Error: ${e.message} - code: ${t} - reqId: ${r}`),502===t&&s(Error("Connection refused"))}),t.on(a.EventName.connected,()=>{console.log("IBKR Connected successfully"),t.reqPositions()}),t.on(a.EventName.position,(e,t,s,a)=>{console.log("Received position:",t.symbol,s),t.symbol&&r.push({symbol:t.symbol,shares:s,avgCost:a})}),t.on(a.EventName.positionEnd,async()=>{console.log("Position end received. Total IBKR positions:",r.length);try{if(0===r.length){console.log("No positions found"),clearTimeout(o),t.disconnect(),e([]);return}let s=r.map(e=>e.symbol);console.log("Fetching quotes for symbols:",s);let a=await (0,i.gO)(s);console.log("Received quotes for",Object.keys(a).length,"symbols");let n=r.map(e=>{let t=a[e.symbol];if(!t)return console.warn(`No quote found for symbol: ${e.symbol}`),null;let r=t.regularMarketPrice,s=e.shares*r,o=e.shares*e.avgCost;return{symbol:e.symbol,shares:e.shares,price:r,value:s,avgCost:e.avgCost,return:(s-o)/o*100}}).filter(e=>null!==e);console.log("All positions processed. Total valid positions:",n.length),clearTimeout(o),t.disconnect(),e(n)}catch(e){console.error("Error processing positions:",e),s(e)}}),t.on(a.EventName.disconnected,()=>{console.log("IBKR Disconnected")}),console.log("Initiating IBKR connection..."),t.connect()}),l=await s;return console.log("Retrieved positions:",l),l}catch(e){return console.error("Error fetching positions data:",e),null}}(0,r(84672).D)([l]),(0,s.A)(l,"40130103a12210564b9cc2eb00ee5bfd15ed9e9469",null)},33873:e=>{"use strict";e.exports=require("path")},38042:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>p,HM:()=>u,L3:()=>h,c7:()=>m,lG:()=>l,zM:()=>d});var s=r(43197),a=r(14824),o=r(65264),i=r(7676),n=r(51001);let l=o.bL,d=o.l9,c=o.ZL,u=o.bm,f=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.hJ,{ref:r,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));f.displayName=o.hJ.displayName;let p=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(c,{children:[(0,s.jsx)(f,{}),(0,s.jsxs)(o.UC,{ref:a,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,s.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=o.UC.displayName;let m=({className:e,...t})=>(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let h=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.hE,{ref:r,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=o.hE.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.VY,{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})).displayName=o.VY.displayName},38625:(e,t,r)=>{"use strict";r.d(t,{CP:()=>l,Q2:()=>c,Qv:()=>d,w7:()=>n});var s=r(49068);r(77048);var a=r(68941),o=r(91581),i=r(91773);async function n(e,t){try{if(!e||!t)throw Error("Missing required fields");if(await a.A.watchlist.findFirst({where:{user_id:t,ticker:e.toUpperCase(),deleted_at:null}}))throw Error("Stock already in watchlist");return await a.A.watchlist.create({data:{user_id:t,ticker:e.toUpperCase()}}),(0,i.revalidatePath)("/watchlist"),{success:!0}}catch(e){if(console.error("[WATCHLIST_ADD_ERROR]",e),e instanceof Error)throw Error(e.message);throw Error("Failed to add stock to watchlist")}}async function l(e){try{if(!e)throw Error("User ID is required");return await a.A.watchlist.findMany({where:{user_id:e,deleted_at:null},orderBy:{created_at:"desc"}})}catch(e){return console.error("[WATCHLIST_GET_ERROR]",e),[]}}async function d(e,t){try{if(!e||!t)throw Error("Missing required fields");if(!await a.A.watchlist.findFirst({where:{id:e,user_id:t,deleted_at:null}}))throw Error("Stock not found in watchlist");return await a.A.watchlist.update({where:{id:e},data:{deleted_at:new Date}}),(0,i.revalidatePath)("/watchlist"),{success:!0}}catch(e){if(console.error("[WATCHLIST_REMOVE_ERROR]",e),e instanceof Error)throw Error(e.message);throw Error("Failed to remove stock from watchlist")}}async function c(e){try{let t={};for(let r of e)try{let e=await (0,o.TZ)(r);t[r]=e}catch(e){console.error(`[QUOTE_FETCH_ERROR] ${r}:`,e),t[r]=null}return t}catch(e){return console.error("[QUOTES_FETCH_ERROR]",e),{}}}(0,r(84672).D)([n,l,d,c]),(0,s.A)(n,"60fe6244bf31f4bd3601e492f7ad4652260e84cac1",null),(0,s.A)(l,"4076ae6917a9197faf33016ff9b25083685fba3edd",null),(0,s.A)(d,"604b184f55fb3c880747ad4d6ee2ff556c780bbbca",null),(0,s.A)(c,"40070bd324e71428f5da05bccd7818bdf7d2e9fb50",null)},41696:(e,t,r)=>{Promise.resolve().then(r.bind(r,53588)),Promise.resolve().then(r.bind(r,19647))},43169:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(99292);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},43806:(e,t,r)=>{"use strict";r.d(t,{V:()=>o,e:()=>i});var s=r(49068);r(77048);var a=r(68941);async function o(e){let t=await a.A.userProfile.findUnique({where:{user_id:e}});return t?{userId:t.user_id,firstName:t.first_name||"",lastName:t.last_name||"",createdAt:t.created_at,updatedAt:t.updated_at,deletedAt:t.deleted_at,settings:t.settings}:null}async function i(e,t){let r=await a.A.userProfile.update({where:{user_id:e},data:{first_name:t.firstName,last_name:t.lastName,settings:JSON.parse(JSON.stringify(t.settings)),updated_at:new Date}});return{userId:r.user_id,firstName:r.first_name||"",lastName:r.last_name||"",createdAt:r.created_at,updatedAt:r.updated_at,deletedAt:r.deleted_at,settings:r.settings}}(0,r(84672).D)([o,i]),(0,s.A)(o,"40ee0f9f0aef4631dabda29c7785bb1a0e6f2ca665",null),(0,s.A)(i,"60605285d0d5a121c2c9eef58992e0bf1d3a0c9b0e",null)},47190:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(21601).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistHeader.tsx","default")},48161:e=>{"use strict";e.exports=require("node:os")},50809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(52927),a=r(52058),o=r(38625),i=r(13317),n=r(47190),l=r(22525);let d=(0,r(16386).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function c(){return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[300px] text-center p-4",children:[(0,s.jsx)(d,{className:"w-10 h-10 text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Your watchlist is empty"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground max-w-sm",children:"Start adding stocks to your watchlist to track their performance and get insights"})]})}var u=r(32310);async function f(){let e=await (0,i.j2)();e?.user||(0,a.redirect)("/api/auth/signin");let[t,r]=await Promise.all([(0,o.CP)(e.user.id),(0,u.R)(e.user.id)]),d=[...t].sort((e,t)=>{let s=r?.some(t=>t.symbol===e.ticker),a=r?.some(e=>e.symbol===t.ticker);return s&&!a?-1:!s&&a?1:0});return(0,s.jsxs)("div",{className:"container max-w-7xl mx-auto px-4 py-4 sm:py-6 md:py-8",children:[(0,s.jsx)(n.default,{userId:e.user.id}),0===d.length?(0,s.jsx)(c,{}):(0,s.jsx)(l.default,{items:d,userId:e.user.id,holdings:r})]})}},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53588:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(43197),a=r(14824),o=r(416),i=r(95311);let n=(0,r(44736).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var l=r(89806),d=r(10013),c=r(20830),u=r(90772);function f({open:e,onOpenChange:t,stockId:r,ticker:o,userId:i}){let{toast:n}=(0,c.dj)(),[l,f]=(0,a.useState)(!1),p=async()=>{try{f(!0),await (0,u.Q)(r,i),n({title:"Success",description:`${o} has been removed from your watchlist`}),t(!1)}catch(e){n({title:"Error",description:e instanceof Error?e.message:"Failed to remove stock from watchlist",variant:"destructive"})}finally{f(!1)}};return(0,s.jsx)(d.Lt,{open:e,onOpenChange:t,children:(0,s.jsxs)(d.EO,{children:[(0,s.jsxs)(d.wd,{children:[(0,s.jsx)(d.r7,{children:"Remove Stock"}),(0,s.jsxs)(d.$v,{children:["Are you sure you want to remove ",o," from your watchlist? This action cannot be undone."]})]}),(0,s.jsxs)(d.ck,{children:[(0,s.jsx)(d.Zr,{disabled:l,children:"Cancel"}),(0,s.jsx)(d.Rx,{onClick:p,disabled:l,className:"bg-destructive hover:bg-destructive/90",children:l?"Removing...":"Remove"})]})]})})}var p=r(24017);p.callServer,p.findSourceMapURL;var m=r(84600);function h({items:e,userId:t,holdings:r}){let[d,c]=(0,a.useState)({}),[u,p]=(0,a.useState)(!0),[h,x]=(0,a.useState)(null);return u?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:[1,2,3].map(e=>(0,s.jsxs)(o.Zp,{className:"p-4",children:[(0,s.jsx)(i.E,{className:"h-4 w-20 mb-3"}),(0,s.jsx)(i.E,{className:"h-6 w-24 mb-2"}),(0,s.jsx)(i.E,{className:"h-4 w-full"})]},e))}):(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:[e.map(e=>{let t=d[e.ticker],a=t?.regularMarketChange||0,i=t?.regularMarketChangePercent||0;function c(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}return(0,s.jsxs)(o.Zp,{className:"p-4 hover:shadow-lg transition-shadow relative",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("h3",{className:"text-base font-semibold",children:e.ticker}),(0,s.jsx)(m.K,{symbol:e.ticker,holdings:r})]}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground truncate ml-2 max-w-[150px]",children:t?.shortName})]}),(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsx)("div",{className:"text-size-xl font-weight-bold",children:c(t?.regularMarketPrice||0)}),(0,s.jsx)("div",{className:`text-size-xs ${a>=0?"text-green-600":"text-red-600"}`,children:(0,s.jsxs)("span",{className:"inline-flex items-center gap-1",children:[(0,s.jsxs)("span",{children:[a>=0?"+":"",c(a)]}),(0,s.jsxs)("span",{children:["(",i.toFixed(2),"%)"]})]})})]}),(0,s.jsx)("div",{className:"absolute bottom-3 right-3",children:(0,s.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8 text-muted-foreground hover:text-destructive transition-colors",onClick:()=>x({id:e.id,ticker:e.ticker}),children:(0,s.jsx)(n,{className:"h-4 w-4"})})})]},e.id)}),h&&(0,s.jsx)(f,{open:!!h,onOpenChange:e=>!e&&x(null),stockId:h.id,ticker:h.ticker,userId:t})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55830:(e,t,r)=>{"use strict";r.d(t,{M:()=>o,f:()=>i});var s=r(43806),a=r(76977);async function o(e){try{let t=await (0,s.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return new a.IBApi({clientId:r,host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port})}catch(e){throw console.error("Error creating IBKR connection:",e),e}}async function i(e){try{let t=await (0,s.V)(e);if(!t?.settings?.ibkrConnectionDetail)throw Error("IBKR connection details not found");let r=new a.IBApiNext({host:t.settings.ibkrConnectionDetail.host,port:t.settings.ibkrConnectionDetail.port}),o=parseInt(t?.settings?.ibkrConnectionDetail?.clientId?.toString()??Math.floor(100*Math.random()).toString());return{ibApiNext:r,clientId:o}}catch(e){throw console.error("Error creating IBKR connection:",e),e}}},57975:e=>{"use strict";e.exports=require("node:util")},61013:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>d,m_:()=>l});var s=r(43197),a=r(14824),o=r(6292),i=r(51001);let n=o.Kq,l=o.bL,d=o.l9,c=a.forwardRef(({className:e,sideOffset:t=4,...r},a)=>(0,s.jsx)(o.UC,{ref:a,sideOffset:t,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));c.displayName=o.UC.displayName},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66030:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var s=r(42585),a=r(59246),o=r(63528),i=r.n(o),n=r(83599),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["watchlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50809)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/watchlist/page",pathname:"/watchlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68941:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(67566);let a=globalThis.__prisma||new s.PrismaClient},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78144:(e,t,r)=>{Promise.resolve().then(r.bind(r,22525)),Promise.resolve().then(r.bind(r,47190))},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80322:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var s=r(49068);r(77048);var a=r(55830);async function o(e){try{let{ibApiNext:t,clientId:r}=await (0,a.f)(e);return new Promise((e,r)=>{let s=setTimeout(()=>r(Error("Connection timeout")),3e3);t.connect().getAccountSummary("All","NetLiquidation").subscribe({next:()=>{clearTimeout(s),e(!0),t.disconnect()},error:e=>{clearTimeout(s),r(e),t.disconnect()}}).add(()=>{t.isConnected&&(t.disconnect(),console.log("Disconnected from IBKR"))})})}catch(e){return console.error("Error testing IBKR connection:",e),!1}}(0,r(84672).D)([o]),(0,s.A)(o,"40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",null)},84600:(e,t,r)=>{"use strict";r.d(t,{K:()=>i});var s=r(43197);let a=(0,r(44736).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var o=r(61013);function i({symbol:e,holdings:t=null}){if(!t)return null;let r=t.find(t=>t.symbol.toUpperCase()===e.toUpperCase());return r?(0,s.jsx)(o.Bc,{children:(0,s.jsxs)(o.m_,{children:[(0,s.jsx)(o.k$,{children:(0,s.jsx)(a,{className:"h-4 w-4 fill-primary text-primary"})}),(0,s.jsx)(o.ZI,{children:(0,s.jsxs)("div",{className:"text-size-sm",children:[(0,s.jsx)("p",{children:"Current Position"}),(0,s.jsxs)("p",{className:"text-size-xs text-muted-foreground",children:["Shares: ",r.shares.toLocaleString()," | Value: $",r.value.toLocaleString()]})]})})]})}):null}},86389:(e,t,r)=>{"use strict";r.d(t,{w:()=>a});var s=r(24017);let a=(0,s.createServerReference)("60fe6244bf31f4bd3601e492f7ad4652260e84cac1",s.callServer,void 0,s.findSourceMapURL,"addToWatchlist")},90772:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var s=r(24017);let a=(0,s.createServerReference)("604b184f55fb3c880747ad4d6ee2ff556c780bbbca",s.callServer,void 0,s.findSourceMapURL,"removeFromWatchlist")},91645:e=>{"use strict";e.exports=require("net")},95311:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(43197),a=r(51001);function o({className:e,...t}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}},98100:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(43197),a=r(14824),o=r(51001);let i=a.forwardRef(({className:e,type:t,...r},a)=>(0,s.jsx)("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));i.displayName="Input"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,4923,9292,3314,4515],()=>r(66030));module.exports=s})();