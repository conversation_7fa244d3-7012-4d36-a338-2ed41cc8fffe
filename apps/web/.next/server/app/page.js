(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13317:(e,r,t)=>{"use strict";t.d(r,{j2:()=>u,Y9:()=>l,Jv:()=>c});var n=t(74723),i=t(89886),o=t(68941),s=t(30935);let a={...{secret:process.env.AUTH_SECRET,providers:[s.A],callbacks:{authorized:({auth:e,request:{nextUrl:r}})=>!!e?.user}},adapter:(0,i.y)(o.A),basePath:"/api/auth",session:{strategy:"jwt"},callbacks:{async session({session:e,token:r}){let t=await o.A.user.findUnique({where:{email:r.email},include:{user_profiles:!0}});if(!t)return e;let n=t.user_profiles?.settings;return{...e,user:{...e.user,id:t.id,role:n.role??"user"}}}}},{handlers:l,auth:u,signIn:c,signOut:d}=(0,n.Ay)(a)},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},43169:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(99292);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},48161:e=>{"use strict";e.exports=require("node:os")},49068:(e,r,t)=>{"use strict";Object.defineProperty(r,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=t(21601)},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var n=t(67566);let i=globalThis.__prisma||new n.PrismaClient},69433:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$RSC_SERVER_ACTION_0:()=>a,default:()=>l});var n=t(52927),i=t(49068);t(77048);var o=t(13317),s=t(52058);let a=async function(){await (0,o.Jv)("Google")};async function l(){let e=await (0,o.j2)();if(!e?.user)return(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[(0,n.jsx)("div",{className:"text-size-lg mb-4",children:"Please log in to view your profile."}),(0,n.jsx)("form",{action:(0,i.A)(a,"008c456793b8f928c926a46a00c819c151008d27dc",null),children:(0,n.jsx)("button",{className:"px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90",children:"Sign in with Google"})})]});let{user:r}=e;e&&(0,s.redirect)("/risk-signals")}},73024:e=>{"use strict";e.exports=require("node:fs")},73788:(e,r,t)=>{"use strict";let n;Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{arrayBufferToString:function(){return a},decrypt:function(){return c},encrypt:function(){return u},getActionEncryptionKey:function(){return y},getClientReferenceManifestForRsc:function(){return g},getServerModuleMap:function(){return f},setReferenceManifestsSingleton:function(){return p},stringToUint8Array:function(){return l}});let i=t(86715),o=t(64404),s=t(29294);function a(e){let r=new Uint8Array(e),t=r.byteLength;if(t<65535)return String.fromCharCode.apply(null,r);let n="";for(let e=0;e<t;e++)n+=String.fromCharCode(r[e]);return n}function l(e){let r=e.length,t=new Uint8Array(r);for(let n=0;n<r;n++)t[n]=e.charCodeAt(n);return t}function u(e,r,t){return crypto.subtle.encrypt({name:"AES-GCM",iv:r},e,t)}function c(e,r,t){return crypto.subtle.decrypt({name:"AES-GCM",iv:r},e,t)}let d=Symbol.for("next.server.action-manifests");function p({page:e,clientReferenceManifest:r,serverActionsManifest:t,serverModuleMap:n}){var i;let s=null==(i=globalThis[d])?void 0:i.clientReferenceManifestsPerPage;globalThis[d]={clientReferenceManifestsPerPage:{...s,[(0,o.normalizeAppPath)(e)]:r},serverActionsManifest:t,serverModuleMap:n}}function f(){let e=globalThis[d];if(!e)throw Object.defineProperty(new i.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function g(){let e=globalThis[d];if(!e)throw Object.defineProperty(new i.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:r}=e,t=s.workAsyncStorage.getStore();if(!t){var n=r;let e=Object.values(n),t={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let r of e)t.clientModules={...t.clientModules,...r.clientModules},t.edgeRscModuleMapping={...t.edgeRscModuleMapping,...r.edgeRscModuleMapping},t.rscModuleMapping={...t.rscModuleMapping,...r.rscModuleMapping};return t}let o=r[t.route];if(!o)throw Object.defineProperty(new i.InvariantError(`Missing Client Reference Manifest for ${t.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return o}async function y(){if(n)return n;let e=globalThis[d];if(!e)throw Object.defineProperty(new i.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let r=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===r)throw Object.defineProperty(new i.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",l(atob(r)),"AES-GCM",!0,["encrypt","decrypt"])}},76760:e=>{"use strict";e.exports=require("node:path")},77048:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{decryptActionBoundArgs:function(){return y},encryptActionBoundArgs:function(){return g}}),t(40188);let n=t(21601),i=t(59175),o=t(95933),s=t(73788),a=t(63033),l=t(349),u=function(e){return e&&e.__esModule?e:{default:e}}(t(60154)),c=new TextEncoder,d=new TextDecoder;async function p(e,r){let t=await (0,s.getActionEncryptionKey)();if(void 0===t)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(r),i=n.slice(0,16),o=n.slice(16),a=d.decode(await (0,s.decrypt)(t,(0,s.stringToUint8Array)(i),(0,s.stringToUint8Array)(o)));if(!a.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return a.slice(e.length)}async function f(e,r){let t=await (0,s.getActionEncryptionKey)();if(void 0===t)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);a.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let i=(0,s.arrayBufferToString)(n.buffer),o=await (0,s.encrypt)(t,n,c.encode(e+r));return btoa(i+(0,s.arrayBufferToString)(o))}let g=u.default.cache(async function e(r,...t){let{clientModules:i}=(0,s.getClientReferenceManifestForRsc)(),u=Error();Error.captureStackTrace(u,e);let c=!1,d=a.workUnitAsyncStorage.getStore(),p=(null==d?void 0:d.type)==="prerender"?(0,l.createHangingInputAbortSignal)(d):void 0,g=await (0,o.streamToString)((0,n.renderToReadableStream)(t,i,{signal:p,onError(e){(null==p||!p.aborted)&&(c||(c=!0,u.message=e instanceof Error?e.message:String(e)))}}),p);if(c)throw u;if(!d)return f(r,g);let y=(0,a.getPrerenderResumeDataCache)(d),m=(0,a.getRenderResumeDataCache)(d),v=r+g,b=(null==y?void 0:y.encryptedBoundArgs.get(v))??(null==m?void 0:m.encryptedBoundArgs.get(v));if(b)return b;let _="prerender"===d.type?d.cacheSignal:void 0;null==_||_.beginRead();let h=await f(r,g);return null==_||_.endRead(),null==y||y.encryptedBoundArgs.set(v,h),h});async function y(e,r){let t,n=await r,o=a.workUnitAsyncStorage.getStore();if(o){let r="prerender"===o.type?o.cacheSignal:void 0,i=(0,a.getPrerenderResumeDataCache)(o),s=(0,a.getRenderResumeDataCache)(o);(t=(null==i?void 0:i.decryptedBoundArgs.get(n))??(null==s?void 0:s.decryptedBoundArgs.get(n)))||(null==r||r.beginRead(),t=await p(e,n),null==r||r.endRead(),null==i||i.decryptedBoundArgs.set(n,t))}else t=await p(e,n);let{edgeRscModuleMapping:l,rscModuleMapping:u}=(0,s.getClientReferenceManifestForRsc)();return await (0,i.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(c.encode(t)),(null==o?void 0:o.type)==="prerender"?o.renderSignal.aborted?e.close():o.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:u,serverModuleMap:(0,s.getServerModuleMap)()}})}},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},86641:()=>{},87313:()=>{},97744:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"008c456793b8f928c926a46a00c819c151008d27dc":()=>n.$$RSC_SERVER_ACTION_0});var n=t(69433)},99402:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var n=t(42585),i=t(59246),o=t(63528),s=t.n(o),a=t(83599),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69433)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,67657)),"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,45924,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,37797,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,46066,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,43169))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[3491,7728,6631,7566,1773,5045,5438,7524,4923,9292,4515],()=>t(99402));module.exports=n})();