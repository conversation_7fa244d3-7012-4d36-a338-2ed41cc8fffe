(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[12],{1432:(e,t,s)=>{"use strict";s.d(t,{w:()=>r});var a=s(2007);let r=(0,a.createServerReference)("60fe6244bf31f4bd3601e492f7ad4652260e84cac1",a.callServer,void 0,a.findSourceMapURL,"addToWatchlist")},2922:(e,t,s)=>{"use strict";s.d(t,{K:()=>n});var a=s(1753),r=s(8526),l=s(3465);function n(e){let{symbol:t,holdings:s=null}=e;if(!s)return null;let n=s.find(e=>e.symbol.toUpperCase()===t.toUpperCase());return n?(0,a.jsx)(l.Bc,{children:(0,a.jsxs)(l.m_,{children:[(0,a.jsx)(l.k$,{children:(0,a.jsx)(r.A,{className:"h-4 w-4 fill-primary text-primary"})}),(0,a.jsx)(l.ZI,{children:(0,a.jsxs)("div",{className:"text-size-sm",children:[(0,a.jsx)("p",{children:"Current Position"}),(0,a.jsxs)("p",{className:"text-size-xs text-muted-foreground",children:["Shares: ",n.shares.toLocaleString()," | Value: $",n.value.toLocaleString()]})]})})]})}):null}},5419:(e,t,s)=>{"use strict";s.d(t,{default:()=>ey});var a=s(1753),r=s(2007);let l=(0,r.createServerReference)("40dd82ab3d6d3c22d3be9a14e44923ca6caa280ddc",r.callServer,void 0,r.findSourceMapURL,"default"),n=(0,r.createServerReference)("40fea060f6eb973a2674de96eff54bdb24dc2ec292",r.callServer,void 0,r.findSourceMapURL,"parseTrendChangeLink"),i=(0,r.createServerReference)("40c88ae3d31f771b8e364a36bfc04cab67dbc08815",r.callServer,void 0,r.findSourceMapURL,"extractEquityTableUrlRegEx"),d=(0,r.createServerReference)("00651d8bbf79c87fe037bd0f517ffb0e18dcb67520",r.callServer,void 0,r.findSourceMapURL,"getLastSyncedTrendChangeDate"),c=(0,r.createServerReference)("4082914884143dd2970140de0dd1f1be2975301463",r.callServer,void 0,r.findSourceMapURL,"getTrendChangesByDateOrMostRecent"),o=(0,r.createServerReference)("40e54e4c2e785198c3edb84e94b2846d18cc39479b",r.callServer,void 0,r.findSourceMapURL,"saveTrendChanges");var h=s(9890),m=s(9107),x=s(9338),u=s(8493),g=s(8355);let f=(0,r.createServerReference)("60dbaaa6dbaf6a54ad057cc9946eddbec128f5bd97",r.callServer,void 0,r.findSourceMapURL,"calculateStockPicks");var b=s(3183),p=s(9458);let j=(0,r.createServerReference)("0097dcbe8a183a3e3943497fe932f9b1b52a2680d7",r.callServer,void 0,r.findSourceMapURL,"getAllSymbols");var v=s(1896),k=s(5117),y=s.n(k),w=s(200),N=s(8623),S=s(653),T=s(5007),C=s(7930),E=s(8932);function R(e){let{symbol:t,isInWatchlist:s,onToggleWatchlist:r}=e,[l,n]=(0,u.useState)(!1),i=async()=>{try{n(!0),await r(t)}catch(e){(0,E.oR)({title:e instanceof Error?e.message:"Failed to update watchlist",variant:"destructive"})}finally{n(!1)}};return(0,a.jsx)(v.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:i,disabled:l,children:(0,a.jsx)(C.A,{className:"h-4 w-4 ".concat(s?"fill-yellow-400 text-yellow-400":"fill-none text-muted-foreground hover:text-foreground")})})}var L=s(2922);function I(e){let{resultTableTrendChange:t,showStockPicksOnly:s,stockPicks:r,entryWindow:l,session:n,watchlistItems:i,onToggleWatchlist:d,holdings:c}=e;return(0,a.jsx)("div",{className:"block md:hidden",children:(0,a.jsx)(w.F,{className:"h-[calc(100vh-300px)]",children:(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4",children:t.map(e=>(0,a.jsx)(x.Zp,{className:"\n                    ".concat(r.some(t=>t.index===e.index)?"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700":"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700","\n                    shadow-lg hover:shadow-xl transition-all duration-200\n                  "),children:(0,a.jsxs)("div",{className:"p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.E,{weight:"bold",size:"5",children:e.index}),(0,a.jsx)(L.K,{symbol:e.index,holdings:c}),(0,a.jsxs)(N.E,{variant:"soft",color:"BULLISH"===e.trend?"green":"BEARISH"===e.trend?"red":"blue",className:"flex items-center gap-1",children:["BULLISH"===e.trend?(0,a.jsx)(p.Kpk,{}):"BEARISH"===e.trend?(0,a.jsx)(p.ZLN,{}):(0,a.jsx)(p.YTx,{}),e.trend]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["admin"===n.user.role&&r.some(t=>t.index===e.index)&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(T.A,{userId:n.user.id,symbol:e.index,price:e.previousClose,action:"BULLISH"===e.trend?"BUY":"SELL"})}),(0,a.jsx)(R,{symbol:e.index,isInWatchlist:i.includes(e.index),onToggleWatchlist:d})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(m.E,{size:"1",color:"gray",children:"Buy"}),(0,a.jsx)(m.E,{weight:"medium",children:(0,S.T)(e.buyTrade)})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(m.E,{size:"1",color:"gray",children:"Sell"}),(0,a.jsx)(m.E,{weight:"medium",children:(0,S.T)(e.sellTrade)})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(m.E,{size:"1",color:"gray",children:"Previous"}),(0,a.jsx)(m.E,{weight:"medium",children:(0,S.T)(e.previousClose)})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(m.E,{size:"1",color:"gray",children:"Window"}),(0,a.jsx)(m.E,{weight:"medium",children:(0,S.T)((e.sellTrade-e.buyTrade)*(Number(l)/100))})]})]}),(0,a.jsx)("div",{className:"relative h-2 bg-slate-100 dark:bg-slate-800 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"absolute h-full bg-blue-500",style:{left:"".concat(e.buyTrade/e.sellTrade*100,"%"),right:"".concat(100-e.previousClose/e.sellTrade*100,"%")}})})]})},e.index))})})})})}var P=s(3823);function O(e){let{resultTableTrendChange:t,showStockPicksOnly:s,stockPicks:r,entryWindow:l,session:n,watchlistItems:i,onToggleWatchlist:d,holdings:c}=e;return console.log("DesktopTableView() called: resultTableTrendChange.length ->",t.length),console.log("DesktopTableView: showStockPicksOnly",s),(0,a.jsx)("div",{className:"hidden md:block h-[calc(100vh-300px)] overflow-auto",children:(0,a.jsxs)(P.bL,{variant:"surface",className:"relative bg-white dark:bg-slate-900",children:[(0,a.jsx)(P.Y9,{className:"bg-slate-50 dark:bg-slate-800/50",children:(0,a.jsxs)(P.fI,{className:"border-b border-slate-200 dark:border-slate-700",children:[(0,a.jsx)(P.Oh,{className:"sticky left-0 z-10 bg-slate-50 dark:bg-slate-800/50 after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:bg-slate-200 dark:after:bg-slate-700 font-semibold text-slate-900 dark:text-white",children:"Symbol"}),(0,a.jsx)(P.Oh,{className:"font-semibold text-slate-900 dark:text-white",children:"Trend"}),(0,a.jsx)(P.Oh,{align:"right",className:"font-semibold text-slate-900 dark:text-white",children:"Buy"}),(0,a.jsx)(P.Oh,{align:"right",className:"font-semibold text-slate-900 dark:text-white",children:"Sell"}),(0,a.jsx)(P.Oh,{align:"right",className:"font-semibold text-slate-900 dark:text-white",children:"Previous"}),(0,a.jsx)(P.Oh,{align:"right",className:"font-semibold text-slate-900 dark:text-white",children:"Window"}),(0,a.jsx)(P.Oh,{className:"font-semibold text-slate-900 dark:text-white",children:"Action"})]})}),(0,a.jsx)(P.nB,{children:t.map(e=>(0,a.jsxs)(P.fI,{className:"\n                ".concat(r.some(t=>t.index===e.index)?"dark:bg-green-900/30 bg-green-50/80 border-green-200 dark:border-green-800":"","\n                hover:dark:bg-slate-800/50 hover:bg-slate-50/80\n                transition-all duration-200 border-b border-slate-100 dark:border-slate-800\n              "),children:[(0,a.jsx)(P.kw,{className:"sticky left-0 z-10 bg-inherit after:absolute after:right-0 after:top-0 after:h-full after:w-[1px] after:bg-slate-200 dark:after:bg-slate-800",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(m.E,{weight:"bold",className:"text-foreground font-semibold",children:e.index}),r.some(t=>t.index===e.index)&&(0,a.jsx)("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"Stock Pick"})]}),(0,a.jsx)(L.K,{symbol:e.index,holdings:c})]})}),(0,a.jsx)(P.fh,{children:(0,a.jsxs)(N.E,{variant:"soft",color:"BULLISH"===e.trend?"green":"BEARISH"===e.trend?"red":"blue",className:"flex items-center gap-1",children:["BULLISH"===e.trend?(0,a.jsx)(p.Kpk,{}):"BEARISH"===e.trend?(0,a.jsx)(p.ZLN,{}):(0,a.jsx)(p.YTx,{}),e.trend]})}),(0,a.jsxs)(P.fh,{align:"right",className:"financial-data font-medium",children:["$",(0,S.T)(e.buyTrade)]}),(0,a.jsxs)(P.fh,{align:"right",className:"financial-data font-medium",children:["$",(0,S.T)(e.sellTrade)]}),(0,a.jsxs)(P.fh,{align:"right",className:"financial-data text-muted-foreground",children:["$",(0,S.T)(e.previousClose)]}),(0,a.jsx)(P.fh,{align:"right",className:"financial-data",children:(0,a.jsxs)("span",{className:"text-amber-600 dark:text-amber-400 font-semibold",children:["$",(0,S.T)((e.sellTrade-e.buyTrade)*(Number(l)/100))]})}),(0,a.jsx)(P.fh,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["admin"===n.user.role&&r.some(t=>t.index===e.index)&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(T.A,{userId:n.user.id,symbol:e.index,price:e.previousClose,action:"BULLISH"===e.trend?"BUY":"SELL"})}),(0,a.jsx)(R,{symbol:e.index,isInWatchlist:i.includes(e.index),onToggleWatchlist:d})]})})]},e.index))})]})})}var _=s(5444),A=s(9305),D=s(9501),U=s(5783);let F=e=>{let{shouldScaleBackground:t=!0,...s}=e;return(0,a.jsx)(D._s.Root,{shouldScaleBackground:t,...s})};F.displayName="Drawer";let M=D._s.Trigger,W=D._s.Portal;D._s.Close;let B=u.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(D._s.Overlay,{ref:t,className:(0,U.cn)("fixed inset-0 z-50 bg-black/80",s),...r})});B.displayName=D._s.Overlay.displayName;let z=u.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(W,{children:[(0,a.jsx)(B,{}),(0,a.jsxs)(D._s.Content,{ref:t,className:(0,U.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background",s),...l,children:[(0,a.jsx)("div",{className:"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted"}),r]})]})});z.displayName="DrawerContent";let H=u.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(D._s.Title,{ref:t,className:(0,U.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});H.displayName=D._s.Title.displayName,u.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(D._s.Description,{ref:t,className:(0,U.cn)("text-sm text-muted-foreground",s),...r})}).displayName=D._s.Description.displayName;var $=s(7568);function Z(e){let{onSettingsChange:t,temporarySettings:s}=e,r=(e,a)=>{t({...s,[e]:a})};return(0,a.jsx)("div",{className:"flex flex-col gap-4 w-full p-4",children:(0,a.jsxs)(x.Zp,{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Display Options"}),(0,a.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.d,{checked:s.showStockInstrumentsOnly,onCheckedChange:e=>r("showStockInstrumentsOnly",e),id:"stocks-only"}),(0,a.jsx)("label",{htmlFor:"stocks-only",className:"text-sm font-medium leading-none",children:"Show stock instruments only"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.d,{checked:s.sortStockPicksToTop,onCheckedChange:e=>r("sortStockPicksToTop",e),id:"sort-picks"}),(0,a.jsx)("label",{htmlFor:"sort-picks",className:"text-sm font-medium leading-none",children:"Sort stock picks to the top"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.d,{checked:s.showStockPicksOnly,onCheckedChange:e=>r("showStockPicksOnly",e),id:"picks-only"}),(0,a.jsx)("label",{htmlFor:"picks-only",className:"text-sm font-medium leading-none",children:"Show stock picks only"})]})]})]})})}function K(e){let{showStockPicksOnly:t,setShowStockPicksOnly:s,sortStockPicksToTheTop:r,setSortStockPicksToTheTop:l,showStocksOnly:n,setShowStocksOnly:i,userId:d,entryWindow:c,debouncedSaveSettings:o}=e,[h,m]=(0,u.useState)(!1),[x,g]=(0,u.useState)(()=>({defaultEntryWindow:Number(c),showStockPicksOnly:t,sortStockPicksToTop:r,showStockInstrumentsOnly:n})),f=async()=>{try{s(x.showStockPicksOnly),l(x.sortStockPicksToTop),i(x.showStockInstrumentsOnly),m(!1),o(x)}catch(e){console.error("Settings Update Error:",e),(0,E.oR)({title:"Failed to save settings",variant:"destructive"})}},b=e=>{g(e),o(e)};return(0,a.jsxs)(F,{open:h,onOpenChange:e=>{console.log("Drawer Open Change:",{open:e,currentSettings:{showStockPicksOnly:t,sortStockPicksToTheTop:r,showStocksOnly:n}}),e&&g({defaultEntryWindow:Number(c),showStockPicksOnly:t,sortStockPicksToTop:r,showStockInstrumentsOnly:n}),m(e)},children:[(0,a.jsx)(M,{asChild:!0,children:(0,a.jsxs)(v.$,{variant:"outline",size:"icon",className:"relative group",children:[(0,a.jsx)(p.L64,{className:"h-4 w-4 transition-transform group-hover:rotate-180 duration-300"}),(0,a.jsx)("span",{className:"sr-only",children:"More settings"}),(0,a.jsx)("div",{className:"absolute -bottom-8 scale-0 group-hover:scale-100 transition-transform bg-secondary text-secondary-foreground text-xs px-2 py-1 rounded whitespace-nowrap z-50",children:"More settings"})]})}),(0,a.jsxs)(z,{className:"h-[80vh]",children:[(0,a.jsx)(H,{className:"sr-only",children:"Risk Signal Settings"}),(0,a.jsxs)("div",{className:"mx-auto w-full max-w-sm h-full overflow-y-auto",children:[(0,a.jsx)(Z,{userId:d,temporarySettings:x,onSettingsChange:e=>b(e)}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 p-4 mt-4 border-t",children:[(0,a.jsx)(v.$,{variant:"outline",onClick:()=>m(!1),children:"Cancel"}),(0,a.jsx)(v.$,{onClick:f,children:"Apply Changes"})]})]})]})]})}var V=s(4083),J=s(2790),Y=s(6052),q=s(6961),G=s(5226),Q=s(1146);function X(e){let{className:t,classNames:s,showOutsideDays:r=!0,...l}=e;return(0,a.jsx)(Q.h,{showOutsideDays:r,className:(0,U.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,U.cn)((0,v.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,U.cn)((0,v.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{IconLeft:e=>{let{className:t,...s}=e;return(0,a.jsx)(q.A,{className:(0,U.cn)("h-4 w-4",t),...s})},IconRight:e=>{let{className:t,...s}=e;return(0,a.jsx)(G.A,{className:(0,U.cn)("h-4 w-4",t),...s})}},...l})}X.displayName="Calendar";var ee=s(4018);let et=ee.bL,es=ee.l9,ea=u.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:l=4,...n}=e;return(0,a.jsx)(ee.ZL,{children:(0,a.jsx)(ee.UC,{ref:t,align:r,sideOffset:l,className:(0,U.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n})})});function er(e){let{date:t,onSelect:s}=e;return(0,a.jsxs)(et,{children:[(0,a.jsx)(es,{asChild:!0,children:(0,a.jsxs)(v.$,{variant:"outline",size:"sm",className:"relative group h-11 px-4 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(Y.A,{className:"h-4 w-4 text-slate-500 dark:text-slate-400"}),(0,a.jsx)("span",{className:"font-medium text-slate-900 dark:text-white",children:(0,J.GP)(t,"MMM dd, yyyy")})]}),(0,a.jsx)("span",{className:"sr-only",children:"Open date picker"})]})}),(0,a.jsx)(ea,{className:"w-auto p-0 bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-xl",align:"end",children:(0,a.jsx)(X,{mode:"single",selected:t,onSelect:e=>{if(e){let t=e.getFullYear();s(new Date(Date.UTC(t,e.getMonth(),e.getDate(),12,0,0)))}else s(void 0)},disabled:e=>e>new Date||(0,V.c)(e),initialFocus:!0,className:"bg-white dark:bg-slate-900"})})]})}ea.displayName=ee.UC.displayName;var el=s(7960);let en=(0,r.createServerReference)("60afccde42b0dc1f25d6ac9ae7ee09877d448467b4",r.callServer,void 0,r.findSourceMapURL,"getTickerListWithChangesBetweenTwoDates");var ei=s(164),ed=s(3603),ec=s(5988),eo=s(4578),eh=s(4336),em=s(6370);let ex=(0,r.createServerReference)("4076ae6917a9197faf33016ff9b25083685fba3edd",r.callServer,void 0,r.findSourceMapURL,"getWatchlistItems");var eu=s(8513),eg=s(1432);let ef=e=>{let{selectedTrendChangeDate:t,initialWatchlistItems:s,userId:r,holdings:l}=e,[n,i]=(0,u.useState)([]),[d,c]=(0,u.useState)(!1),o=(0,em.U)("(max-width: 768px)"),[h,m]=(0,u.useState)(s),g=e=>h.includes(e);(0,u.useEffect)(()=>{(async()=>{if(t){c(!0);try{let e=await en(t);i(e)}catch(e){console.error("Error loading changed tickers:",e),i([])}finally{c(!1)}}})()},[t]);let f=e=>{switch(e){case"BULLISH":return"status-bullish border-0";case"BEARISH":return"status-bearish border-0";default:return"status-neutral border-0"}},b=async e=>{try{if(r)if(h.includes(e)){let t=(await ex(r)).find(t=>t.ticker===e);t&&(await (0,eu.Q)(t.id,r),m(t=>t.filter(t=>t!==e)),(0,E.oR)({title:"Removed from watchlist",description:"".concat(e," has been removed from your watchlist")}))}else await (0,eg.w)(e,r),m(t=>[...t,e]),(0,E.oR)({title:"Added to watchlist",description:"".concat(e," has been added to your watchlist")})}catch(e){(0,E.oR)({title:"Failed to update watchlist",description:e instanceof Error?e.message:"Please try again",variant:"destructive"}),r&&m((await ex(r)).map(e=>e.ticker))}},p=e=>r?(0,a.jsx)(R,{symbol:e,isInWatchlist:g(e),onToggleWatchlist:b}):null,j=e=>(0,J.GP)(e,"MMM dd, yyyy"),k=new Date(t);return k.setDate(k.getDate()-1),(0,a.jsxs)(el.lG,{children:[(0,a.jsx)(el.zM,{asChild:!0,children:n.length>0&&(0,a.jsxs)(v.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),n.length," ",1===n.length?"symbol":"symbols"," changed"]})}),(0,a.jsxs)(el.Cf,{className:"sm:max-w-[625px]",children:[(0,a.jsxs)(el.c7,{children:[(0,a.jsx)(el.L3,{children:"Trend Changes Comparison"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 mb-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1.5 rounded-md bg-muted",children:[(0,a.jsx)(Y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:j(k)})]}),(0,a.jsx)(ec.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1.5 rounded-md bg-primary/10 text-primary",children:[(0,a.jsx)(Y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:j(t)})]})]})]}),d?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(eh.A,{className:"h-8 w-8 animate-spin text-muted-foreground"})}):0===n.length?(0,a.jsx)("div",{className:"flex flex-col items-center justify-center py-8 text-muted-foreground",children:(0,a.jsx)("p",{children:"No trend changes found between these dates"})}):o?(0,a.jsx)(ed.F,{className:"h-[400px]",children:(0,a.jsx)("div",{className:"space-y-4 p-4",children:n.map(e=>{let{current:s,previous:r}=e;return(0,a.jsxs)(x.Zp,{className:(0,U.cn)("p-4 hover:bg-accent transition-colors",g(s.index)&&"dark:bg-blue-900/20 bg-blue-50"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-weight-semibold text-size-lg",children:s.index}),(0,a.jsx)(L.K,{symbol:s.index,holdings:l})]}),p(s.index)]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"space-y-2 p-3 rounded-lg bg-background border",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,a.jsx)("span",{className:"font-medium",children:"Previous"}),(0,a.jsx)("span",{className:"text-xs block",children:j(k)})]}),(0,a.jsx)(A.E,{className:(0,U.cn)("w-full justify-center py-2 text-base font-medium",f(r.trend)),children:r.trend})]}),(0,a.jsxs)("div",{className:"space-y-2 p-3 rounded-lg bg-background border",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,a.jsx)("span",{className:"font-medium",children:"Current"}),(0,a.jsx)("span",{className:"text-xs block",children:j(t)})]}),(0,a.jsx)(A.E,{className:(0,U.cn)("w-full justify-center py-2 text-base font-medium",f(s.trend)),children:s.trend})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 bg-muted/30 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Buy Price"}),(0,a.jsxs)("div",{className:"font-medium text-sm",children:["$",s.buyTrade.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Sell Price"}),(0,a.jsxs)("div",{className:"font-medium text-sm",children:["$",s.sellTrade.toLocaleString()]})]})]})]},s.index)})})}):(0,a.jsx)("div",{className:"h-[400px] overflow-auto rounded-md border",children:(0,a.jsxs)(ei.XI,{children:[(0,a.jsx)(ei.A0,{children:(0,a.jsxs)(ei.Hj,{children:[(0,a.jsx)(ei.nd,{children:"Symbol"}),(0,a.jsx)(ei.nd,{children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:"Previous Trend"}),(0,a.jsx)("span",{className:"text-xs font-normal text-muted-foreground",children:j(k)})]})}),(0,a.jsx)(ei.nd,{children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:"Current Trend"}),(0,a.jsx)("span",{className:"text-xs font-normal text-muted-foreground",children:j(t)})]})}),(0,a.jsx)(ei.nd,{className:"text-right",children:"Buy"}),(0,a.jsx)(ei.nd,{className:"text-right",children:"Sell"}),r&&(0,a.jsx)(ei.nd,{className:"w-[100px]",children:"Watchlist"})]})}),(0,a.jsx)(ei.BF,{children:n.map(e=>{let{current:t,previous:s}=e;return(0,a.jsxs)(ei.Hj,{className:(0,U.cn)(g(t.index)&&"dark:bg-blue-900/20 bg-blue-50"),children:[(0,a.jsx)(ei.nA,{className:"font-medium",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t.index,(0,a.jsx)(L.K,{symbol:t.index,holdings:l})]})}),(0,a.jsx)(ei.nA,{children:(0,a.jsx)(A.E,{className:(0,U.cn)(f(s.trend)),children:s.trend})}),(0,a.jsx)(ei.nA,{children:(0,a.jsx)(A.E,{className:(0,U.cn)(f(t.trend)),children:t.trend})}),(0,a.jsxs)(ei.nA,{className:"text-right",children:["$",t.buyTrade.toString()]}),(0,a.jsxs)(ei.nA,{className:"text-right",children:["$",t.sellTrade.toString()]}),r&&(0,a.jsx)(ei.nA,{children:p(t.index)})]},t.index)})})]})}),(0,a.jsx)(el.HM,{asChild:!0,children:(0,a.jsx)(v.$,{variant:"secondary",type:"button",children:"Close"})})]})]})};function eb(e){let{showStockPicksOnly:t,setShowStockPicksOnly:s,sortStockPicksToTheTop:r,setSortStockPicksToTheTop:l,showStocksOnly:n,setShowStocksOnly:i,userId:d,entryWindow:c,handleEntryWindowChange:o,resultTableTrendChange:h,stockPicks:m,selectedTrendChangeDate:f,onDateSelect:b,initialWatchlistItems:p,holdings:j,debouncedSaveSettings:v}=e,[k,y]=(0,u.useState)(!1);return(0,u.useEffect)(()=>{(async()=>{y(!0),y(!1)})()},[]),(0,a.jsx)(u.Suspense,{fallback:(0,a.jsx)(g.S,{}),children:k?(0,a.jsx)(g.S,{}):(0,a.jsxs)(x.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-lg",children:[(0,a.jsx)(x.aR,{className:"pb-6 border-b border-slate-100 dark:border-slate-700",children:(0,a.jsxs)(x.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white",children:"Entry Window"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Configure risk parameters"})]})]}),(0,a.jsx)("div",{className:"flex items-center pl-4",children:(0,a.jsx)(K,{showStockPicksOnly:t,setShowStockPicksOnly:s,sortStockPicksToTheTop:r,setSortStockPicksToTheTop:l,showStocksOnly:n,setShowStocksOnly:i,userId:d,entryWindow:c,debouncedSaveSettings:v})})]})}),(0,a.jsx)(x.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("label",{htmlFor:"entry-window",className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Risk Percentage"}),(0,a.jsxs)("div",{className:"flex items-center bg-white dark:bg-slate-800 rounded-lg border border-slate-300 dark:border-slate-600 shadow-sm hover:border-blue-400 dark:hover:border-blue-500 transition-colors",children:[(0,a.jsx)(_.p,{id:"entry-window",type:"number",value:c,onChange:o,className:"w-20 h-11 text-sm border-0 focus-visible:ring-0 focus-visible:ring-offset-0 financial-data font-semibold text-slate-900 dark:text-white",min:"0",max:"100"}),(0,a.jsx)("span",{className:"pr-3 text-sm text-slate-500 dark:text-slate-400 font-medium",children:"%"})]})]}),(0,a.jsxs)(A.E,{className:"h-9 px-4 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700 font-semibold",children:["Found"," ",(0,a.jsx)("span",{className:"ml-1 font-bold",children:(t?h.filter(e=>m.some(t=>t.index===e.index)):h).length})]})]}),(0,a.jsx)(er,{date:f,onSelect:b})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(ef,{selectedTrendChangeDate:f,initialWatchlistItems:p,userId:d,holdings:j})})]})})]})})}let ep=(0,r.createServerReference)("7f76ddcec25d3e0083a5eedf540fa563cbecbb815d",r.callServer,void 0,r.findSourceMapURL,"upsertUserProfileRiskSignalSettings"),ej=(0,r.createServerReference)("40130103a12210564b9cc2eb00ee5bfd15ed9e9469",r.callServer,void 0,r.findSourceMapURL,"fetchHoldingsData");var ev=s(9806);let ek=(0,r.createServerReference)("40396872244520ce764475f00a22baf0aa7a603940",r.callServer,void 0,r.findSourceMapURL,"getCachedInstrumentDetailBatch");function ey(e){var t,s,r,k,w,N,S;let{session:T,userProfile:C,initialWatchlistItems:R,initialHoldings:L}=e;console.log("TrendChangeComponent - Initial Props:",{hasUserProfile:!!C,settings:null==C||null==(t=C.settings)?void 0:t.riskSignalSettings});let[P,_]=(0,u.useTransition)(),[A,D]=(0,u.useState)(!1),[U,F]=(0,u.useState)(null),[M,W]=(0,u.useState)([]),[B,z]=(0,u.useState)([]),[H,$]=(0,u.useState)([]),[Z,K]=(0,u.useState)(()=>{var e,t,s;let a=null!=(s=null==C||null==(t=C.settings)||null==(e=t.riskSignalSettings)?void 0:e.showStockPicksOnly)&&s;return console.log("Initializing showStockPicksOnly:",a),a}),[V,J]=(0,u.useState)(null!=(N=null==C||null==(r=C.settings)||null==(s=r.riskSignalSettings)?void 0:s.showStockInstrumentsOnly)&&N),[Y,q]=(0,u.useState)(null!=(S=null==C||null==(w=C.settings)||null==(k=w.riskSignalSettings)?void 0:k.sortStockPicksToTop)&&S),[G,Q]=(0,u.useState)(!0),[X,ee]=(0,u.useState)(R),[et,es]=(0,u.useState)(new Date),[ea,er]=(0,u.useState)(L),[el,en]=(0,u.useState)(new Date),[ei,ed]=(0,u.useState)("10"),[ec,eo]=(0,u.useState)(-1),[eh,em]=(0,u.useState)(!1),[ef,ey]=(0,u.useState)(!1);async function ew(e){try{D(!0);let t=e.map((e,t)=>({...e,originalIndex:t}));z(t),W(t);let s=await f(t,Number(ei));$(s),eo(s.length)}catch(e){console.error("Failed to update trend change model:",e)}finally{D(!1)}}(0,u.useEffect)(()=>{var e;console.log("showStockPicksOnly Effect - Current Value:",Z),console.log("Current userProfile settings:",null==C||null==(e=C.settings)?void 0:e.riskSignalSettings)},[Z,C]),(0,u.useEffect)(()=>{var e,t;(null==C||null==(t=C.settings)||null==(e=t.riskSignalSettings)?void 0:e.defaultEntryWindow)&&ed(C.settings.riskSignalSettings.defaultEntryWindow.toString())},[C]),(0,u.useEffect)(()=>{console.log("showStockPicksOnly changed:",Z)},[Z]),(0,u.useEffect)(()=>{d().then(e=>{en(new Date(e)),es(new Date(e))})},[]),(0,u.useEffect)(()=>{let e=window.requestIdleCallback||(e=>setTimeout(e,1)),t=async()=>{if(0===ea.length){ey(!0);try{var e,t;if(await (0,ev.F)(null==T||null==(e=T.user)?void 0:e.id)){let e=await ej(null==T||null==(t=T.user)?void 0:t.id);console.log("Holdings data ->",e),er(e||[])}}catch(e){}finally{ey(!1)}}},s=e(()=>{setTimeout(()=>{t()},0)});return()=>{window.cancelIdleCallback&&window.cancelIdleCallback(s)}},[]),(0,u.useEffect)(()=>{"Notification"in window&&em("granted"===Notification.permission)},[]);let eN=(0,u.useCallback)(async()=>{if(console.log("applyFilters called with settings:",{showStocksOnly:V,showStockPicksOnly:Z,originalTableLength:B.length,stockPicksLength:H.length}),!B.length)return;let e="filteredList_".concat(V,"_").concat(Z,"_").concat(Y,"_").concat(B.length,"_").concat(H.length),t=sessionStorage.getItem(e);if(t)try{W(JSON.parse(t));return}catch(e){console.error("Failed to parse cached filtered list:",e)}let s=[...B];try{let t=H.map(e=>e.index);if(console.log("Current stock picks:",{count:t.length,picks:t}),eo(t.length),Z)console.log("Applying stock picks filter"),s=s.filter(e=>t.includes(e.index)),console.log("After stock picks filter:",{filteredLength:s.length});else if(V){console.log("Applying stocks only filter");let e=[],t=sessionStorage.getItem("cachedSymbols");if(t)try{e=JSON.parse(t)}catch(e){console.error("Failed to parse cached symbols:",e)}if(0===e.length){if(e=await j(),0===e.length){let t=await ek(B.map(e=>e.index));e=Array.from(t.values()).map(e=>({...e,isStock:"Common Stock"===e.securityType2||"Common Stock"===e.securityType}))}try{sessionStorage.setItem("cachedSymbols",JSON.stringify(e))}catch(e){console.error("Failed to cache symbols:",e)}}console.log("Filtering stocks:",{beforeFilter:s.length,totalSymbols:e.length}),e.length>0&&(s=s.filter(t=>!!e.find(e=>e.ticker===t.index&&e.isStock&&"GOLD"!==e.ticker))),console.log("After stock filter:",{afterFilter:s.length})}try{sessionStorage.setItem(e,JSON.stringify(s))}catch(e){console.error("Failed to cache filtered list:",e)}console.log("Setting final filtered list:",{length:s.length,showStockPicksOnly:Z,showStocksOnly:V,stockPicksCount:t.length,resultCount:t.length}),W(s)}catch(e){console.error("Failed to apply filters:",e)}},[B,V,Z,H,Y]);(0,u.useEffect)(()=>{eN()},[eN]),(0,u.useEffect)(()=>{B.length>0&&(async()=>{let e=await f(B,Number(ei));$(e),eo(e.length)})()},[ei,B]);let eS=(0,u.useMemo)(()=>[...M].sort((e,t)=>{var s,a;if(G){let s=X.includes(e.index);if(s!==X.includes(t.index))return s?-1:1}if(Y){let s=H.some(t=>t.index===e.index);if(s!==H.some(e=>e.index===t.index))return s?-1:1}return(null!=(s=e.originalIndex)?s:0)-(null!=(a=t.originalIndex)?a:0)}),[M,G,Y,X,H]),eT=async()=>{try{let e=new Date(et);return await c(e)}catch(e){return F(e instanceof Error?e:Error("Failed to fetch trend changes")),null}};(0,u.useEffect)(()=>{let e=!0,t=new AbortController,s=async()=>{try{D(!0),F(null);let t=await eT();if(!e)return;if(t){let e=JSON.parse(t);if(e&&e.length>0){await ew(e),D(!1);return}}await eE()}catch(e){console.error("Failed to fetch trend change:",e),F(e instanceof Error?e:Error("Failed to fetch trend changes"))}finally{e&&D(!1)}};return _(()=>{s()}),()=>{e=!1,t.abort()}},[et]);let eC=function(){let e=e=>new DOMParser().parseFromString(e,"text/html").querySelector('table[class*="dtr-table"]'),t=(e,t)=>{let s=[],a=0;return Array.from(e.querySelector("tbody").rows).forEach(e=>{let r={},l=e.cells[0];if(!l)return;let n=l.textContent.match(/(.+) \((.+)\)/);n&&n[1]&&n[2]&&(r.index=n[1],r.trend=n[2]);let i=l.querySelector("em");i&&(r.description=i.textContent);let d=e.cells[1],c=e.cells[2],o=e.cells[3];d&&c&&o&&(r.buyTrade=Number(d.textContent.replace(/,/g,"")),r.sellTrade=Number(c.textContent.replace(/,/g,"")),r.previousClose=Number(o.textContent.replace(/,/g,"")),r.originalIndex=a,r.date=t,s.push(r),a++)}),s};return async(s,a)=>t(e(s),a)}(),eE=async function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{D(!0);let e=await l(),t=[];if(await i(e)){let s=await n(e);es(null==s?void 0:s.date);let a=await l(null==s?void 0:s.href);t=await eC(a,null==s?void 0:s.date),await o(t),await ew(t)}else{let s=e.match(/<time class='article__time' datetime='(.*?)' itemprop='datePublished' pubdate>/);if(s&&s[1]){let a=s[1].toString(),r=new Date(Date.UTC(parseInt(a.slice(0,4)),parseInt(a.slice(5,7))-1,parseInt(a.slice(8,10)),parseInt(a.slice(11,13)),parseInt(a.slice(14,16)),parseInt(a.slice(17,19))));es(r),t=await eC(e,r),await o(t),await ew(t)}}}catch(e){console.error("Error in fetchRiskSignalPage:",e)}finally{D(!1)}},eR=async()=>{try{await fetch("/api/hedgeye/risk-range-signals",{method:"GET"})}catch(e){console.error("Error refreshing data:",e)}},eL=(0,u.useMemo)(()=>y()(async e=>{console.log("Debounced settings save triggered:",e);try{K(e.showStockPicksOnly),q(e.sortStockPicksToTop),J(e.showStockInstrumentsOnly),await ep({riskSignalSettings:e},T.user.id),console.log("Settings saved successfully:",e)}catch(e){var t,s,a,r,l,n,i,d,c;console.error("Failed to save settings:",e),K(null!=(i=null==C||null==(s=C.settings)||null==(t=s.riskSignalSettings)?void 0:t.showStockPicksOnly)&&i),q(null!=(d=null==C||null==(r=C.settings)||null==(a=r.riskSignalSettings)?void 0:a.sortStockPicksToTop)&&d),J(null!=(c=null==C||null==(n=C.settings)||null==(l=n.riskSignalSettings)?void 0:l.showStockInstrumentsOnly)&&c)}},1e3),[T.user.id]);(0,u.useEffect)(()=>{console.log("Settings changed, reapplying filters:",{showStocksOnly:V,showStockPicksOnly:Z}),B.length>0&&eN()},[V,Z,eN,B.length]),(0,u.useEffect)(()=>()=>{eL.cancel()},[eL]);let eI=async e=>{try{if(X.includes(e)){let t=(await ex(T.user.id)).find(t=>t.ticker===e);t&&(await (0,eu.Q)(t.id,T.user.id),ee(t=>t.filter(t=>t!==e)),(0,E.oR)({title:"Removed from watchlist",description:"".concat(e," has been removed from your watchlist")}))}else await (0,eg.w)(e,T.user.id),ee(t=>[...t,e]),(0,E.oR)({title:"Added to watchlist",description:"".concat(e," has been added to your watchlist")})}catch(e){(0,E.oR)({title:"Failed to update watchlist",description:e instanceof Error?e.message:"Please try again",variant:"destructive"}),ee((await ex(T.user.id)).map(e=>e.ticker))}},eP=e=>{let{count:t}=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-slate-100 dark:border-slate-700 bg-slate-50/50 dark:bg-slate-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-blue-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Market Analysis Results"})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-500 dark:text-slate-400",children:["Showing"," ",(0,a.jsx)("span",{className:"font-semibold text-slate-700 dark:text-slate-300",children:t})," ",1===t?"instrument":"instruments"]})]})};return A||P?(0,a.jsx)(g.A,{text:"Loading risk signals...Please wait"}):U?(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen text-destructive",children:["Error loading risk signals: ",U.message]}):(0,a.jsx)(h.m,{className:"py-6 px-4 md:px-6",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(x.Zp,{className:"bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-lg",children:[(0,a.jsx)(x.aR,{className:"pb-6 border-b border-slate-100 dark:border-slate-700",children:(0,a.jsx)(x.ZB,{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white",children:"Risk Range Signals"}),(0,a.jsx)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Real-time analysis and insights for market trends powered by Hedgeye Risk Management"})]})]})})}),(0,a.jsxs)(x.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(p._TA,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-900 dark:text-white mb-1",children:"Filter Controls Available"}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-300",children:"Use the settings panel to adjust view filters and customize your risk analysis display."}),M.length>0&&(!V||Z)&&0===M.filter(e=>H.some(t=>t.index===e.index)).length&&(0,a.jsx)("p",{className:"text-sm text-amber-600 dark:text-amber-400 mt-2 font-medium",children:V?"Stocks only filter is active. Click the gear icon ⚙️ to show all instruments.":Z?"Stock picks filter is active. Click the gear icon ⚙️ to show all instruments.":""})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wide mb-1",children:"Last Sync"}),(0,a.jsxs)("p",{className:"text-sm font-mono text-slate-700 dark:text-slate-300",children:[new Date(el).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})," ",new Date(el).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})]})]}),"admin"===T.user.role&&(0,a.jsxs)(v.$,{variant:"outline",size:"icon",className:"hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 dark:hover:border-blue-600",onClick:eR,children:[(0,a.jsx)(p.jfu,{className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"}),(0,a.jsx)("span",{className:"sr-only",children:"Refresh data"})]})]})]})]}),(0,a.jsx)(u.Suspense,{fallback:(0,a.jsx)(g.S,{}),children:(0,a.jsx)(eb,{showStockPicksOnly:Z,setShowStockPicksOnly:K,sortStockPicksToTheTop:Y,setSortStockPicksToTheTop:q,showStocksOnly:V,setShowStocksOnly:J,userId:(null==C?void 0:C.userId)||"",entryWindow:ei,handleEntryWindowChange:e=>{let t=e.target.value;console.log("Entry window changed to:",t),(""===t||Number(t)>=0&&100>=Number(t))&&(ed(t),""!==t&&eL({defaultEntryWindow:Number(t),showStockPicksOnly:Z,sortStockPicksToTop:Y,showStockInstrumentsOnly:V}))},debouncedSaveSettings:eL,resultTableTrendChange:M,stockPicks:H,selectedTrendChangeDate:et,onDateSelect:e=>{e&&(es(e),_(()=>{eT().then(async e=>{if(e)try{let t=JSON.parse(e);t&&t.length>0&&await ew(t)}catch(e){console.error("Error processing trend changes:",e),F(Error("Failed to process trend changes"))}})}))},initialWatchlistItems:R,holdings:ea})}),(0,a.jsxs)(x.Zp,{className:"flex-1 overflow-hidden bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-lg",children:[0===(Z?M.filter(e=>H.some(t=>t.index===e.index)):M).length&&(0,a.jsx)("div",{className:"m-6 p-6 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg",children:(0,a.jsxs)(b.s,{gap:"3",align:"center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(p._TA,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-900 dark:text-white mb-1",children:"No Data Available"}),(0,a.jsx)(m.E,{size:"2",className:"text-slate-600 dark:text-slate-300",children:Z?"No instruments found meeting the current entry criteria. Try adjusting the entry window in settings.":"No instruments found matching the current filters."})]})]})}),(0,a.jsxs)("div",{className:"hidden md:block",children:[(0,a.jsx)(O,{resultTableTrendChange:eS,showStockPicksOnly:Z,stockPicks:H,entryWindow:ei,session:T,watchlistItems:X,onToggleWatchlist:eI,holdings:ea}),(0,a.jsx)(eP,{count:eS.length})]}),(0,a.jsxs)("div",{className:"md:hidden",children:[(0,a.jsx)(I,{resultTableTrendChange:eS,showStockPicksOnly:Z,stockPicks:H,entryWindow:ei,session:T,watchlistItems:X,onToggleWatchlist:eI,holdings:ea}),(0,a.jsx)(eP,{count:eS.length})]})]})]})})}},6127:(e,t,s)=>{Promise.resolve().then(s.bind(s,5419)),Promise.resolve().then(s.bind(s,8906)),Promise.resolve().then(s.bind(s,8355))},7568:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var a=s(1753),r=s(8493),l=s(8975),n=s(5783);let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(l.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=l.bL.displayName},8513:(e,t,s)=>{"use strict";s.d(t,{Q:()=>r});var a=s(2007);let r=(0,a.createServerReference)("604b184f55fb3c880747ad4d6ee2ff556c780bbbca",a.callServer,void 0,a.findSourceMapURL,"removeFromWatchlist")},8906:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(1753),r=s(8493);class l extends r.Component{static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(e,t){console.error("Uncaught error:",e,t)}render(){return this.state.hasError?(0,a.jsx)("div",{children:"Something went wrong."}):this.props.children}constructor(...e){super(...e),this.state={hasError:!1}}}let n=l},9806:(e,t,s)=>{"use strict";s.d(t,{F:()=>r});var a=s(2007);let r=(0,a.createServerReference)("40acf74552384aecd67ae79d5c5d033c8a2f89ab6c",a.callServer,void 0,a.findSourceMapURL,"testIBKRConnection")}},e=>{var t=t=>e(e.s=t);e.O(0,[584,116,803,316,239,54,154,32,669,929,365,302,358],()=>t(6127)),_N_E=e.O()}]);