[{"/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/eruda-debug.tsx": "1", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/holdings-indicator.tsx": "2", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/market-status-indicator.tsx": "3", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/root-layout-client.tsx": "4", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/fetch-risk-signals.ts": "5", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/get-all-cached-instruments.ts": "6", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/get-equity-table.ts": "7", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/get-instrument-detail.ts": "8", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/ibapi/auto-connection.ts": "9", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/ibapi/connection.ts": "10", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/notifications-server.ts": "11", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/stock-picks.ts": "12", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/upsert-user-profile-settings.ts": "13", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/admin/actions/populate-cached-instrument-details.ts": "14", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/admin/instruments/_components/cached-instrument-table.tsx": "15", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/admin/instruments/page.tsx": "16", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/auth.config.ts": "17", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/auth.ts": "18", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/route.ts": "19", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/risk-range-signals/route.ts": "20", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/risk-signal-visualization/route.ts": "21", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/route.ts": "22", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/order/auto-order/route.ts": "23", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify/route.ts": "24", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify-trend-changes/route.ts": "25", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/subscribe/route.ts": "26", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/unsubscribe/route.ts": "27", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/vapid-key/route.ts": "28", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/sendemail/route.ts": "29", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/_components/dashboard-main.tsx": "30", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/actions/fetch-dashboard-analytics.ts": "31", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/page.tsx": "32", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx": "33", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/models/cache-symbols.ts": "34", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/models/symbols.ts": "35", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/orders/components/option-order-summary.tsx": "36", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/orders/page.tsx": "37", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/page.tsx": "38", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/auto-order-prefill-list.tsx": "39", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/create-order.tsx": "40", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/order-dialog.tsx": "41", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/orders-list.tsx": "42", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/portfolio-page.tsx": "43", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/portfolio-stats.tsx": "44", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/positions-list.tsx": "45", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/__tests__/get-holdings.test.ts": "46", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/cancel-order.ts": "47", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/create-auto-order-prefill.ts": "48", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/create-order.ts": "49", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/fetch-auto-order-prefill.ts": "50", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-all-accounts.ts": "51", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-all-orders.ts": "52", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-holdings.ts": "53", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-portfolio-all-data.ts": "54", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-portfolio-data.ts": "55", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/page.tsx": "56", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/IBAccountDetails.tsx": "57", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/IBKRConnectionForm.tsx": "58", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/NotificationPreferences.tsx": "59", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/_components/ibkr-account-settings.tsx": "60", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/_components/user-role-display.tsx": "61", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/ib-actions.ts": "62", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/ibkr-fetch-account.ts": "63", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/ibkr-test-connection.ts": "64", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/update-user-role.ts": "65", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/page.tsx": "66", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/profile-sheet.tsx": "67", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/send-email-button-test.tsx": "68", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/date-picker.tsx": "69", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/desktop-table-view.tsx": "70", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/entry-window-card.tsx": "71", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/info-circle-popover.tsx": "72", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/input-file.tsx": "73", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/mobile-card-view.tsx": "74", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/option-chain.tsx": "75", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/settings-panel-drawer.tsx": "76", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/settings-panel.tsx": "77", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/skeleton-loader.tsx": "78", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/stock-picks-settings.tsx": "79", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/trend-change-archive.tsx": "80", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/trend-change.tsx": "81", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/trend-changes-from-previous-date.tsx": "82", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/upside-downside-visualization.tsx": "83", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/watchlist-button.tsx": "84", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/page.tsx": "85", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/fetch-openfigi.ts": "86", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/get-option-chains.ts": "87", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/get-risk-signal-for-single-ticker.ts": "88", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/option-limit-order.ts": "89", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/trend-change.ts": "90", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/services/hedgeye.service.ts": "91", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/services/notification.service.ts": "92", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/services/order.service.ts": "93", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/[accountId]/page.tsx": "94", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/_components/account-details.tsx": "95", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/actions/__tests__/get-account-transactions.test.ts": "96", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/actions/get-account-transactions.ts": "97", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/utils/ibkr-errors.ts": "98", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/utils/ibkr-utils.ts": "99", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/utils/promise-utils.ts": "100", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/AddStockDialog.tsx": "101", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/EmptyWatchlist.tsx": "102", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/RemoveStockDialog.tsx": "103", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistGrid.tsx": "104", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistHeader.tsx": "105", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/actions.ts": "106", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/page.tsx": "107", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/error-boundary.tsx": "108", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/footer-nav.tsx": "109", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/header.tsx": "110", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/main-nav.tsx": "111", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/spinner-basic.tsx": "112", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/theme-toggle.tsx": "113", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/alert-dialog.tsx": "114", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/alert.tsx": "115", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/avatar.tsx": "116", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/badge.tsx": "117", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/button.tsx": "118", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/calendar.tsx": "119", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/card.tsx": "120", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/dialog.tsx": "121", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/drawer.tsx": "122", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/form.tsx": "123", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/input.tsx": "124", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/label.tsx": "125", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/popover.tsx": "126", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/scroll-area.tsx": "127", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/select.tsx": "128", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/separator.tsx": "129", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/sheet.tsx": "130", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/skeleton.tsx": "131", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/switch.tsx": "132", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/table.tsx": "133", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/tabs.tsx": "134", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/toast.tsx": "135", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/toaster.tsx": "136", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/tooltip.tsx": "137", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/logger.ts": "138", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/prisma.ts": "139", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/utils-client.ts": "140", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/utils-server.ts": "141", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/utils.ts": "142", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/date-hedgeye.ts": "143", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/notifications.ts": "144", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/pwa.ts": "145", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/yahoo-finance/fetch-quote.ts": "146", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/yahoo-finance/init.ts": "147", "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/yahoo-finance/is-market-open.ts": "148"}, {"size": 2158, "mtime": 1735619181884, "results": "149", "hashOfConfig": "150"}, {"size": 1417, "mtime": 1749870684800, "results": "151", "hashOfConfig": "150"}, {"size": 2190, "mtime": 1749870174482, "results": "152", "hashOfConfig": "150"}, {"size": 1771, "mtime": 1749872987026, "results": "153", "hashOfConfig": "150"}, {"size": 6405, "mtime": 1749900687667, "results": "154", "hashOfConfig": "150"}, {"size": 847, "mtime": 1736133515085, "results": "155", "hashOfConfig": "150"}, {"size": 1771, "mtime": 1717214673451, "results": "156", "hashOfConfig": "150"}, {"size": 3172, "mtime": 1736149550692, "results": "157", "hashOfConfig": "150"}, {"size": 55, "mtime": 1735529074395, "results": "158", "hashOfConfig": "150"}, {"size": 1566, "mtime": 1735553692988, "results": "159", "hashOfConfig": "150"}, {"size": 1691, "mtime": 1735133099761, "results": "160", "hashOfConfig": "150"}, {"size": 2348, "mtime": 1735306313844, "results": "161", "hashOfConfig": "150"}, {"size": 2705, "mtime": 1735722726670, "results": "162", "hashOfConfig": "150"}, {"size": 1408, "mtime": 1736135370624, "results": "163", "hashOfConfig": "150"}, {"size": 7699, "mtime": 1749871931267, "results": "164", "hashOfConfig": "150"}, {"size": 655, "mtime": 1736136134988, "results": "165", "hashOfConfig": "150"}, {"size": 357, "mtime": 1735446246489, "results": "166", "hashOfConfig": "150"}, {"size": 1598, "mtime": 1735483042212, "results": "167", "hashOfConfig": "150"}, {"size": 101, "mtime": 1710034848323, "results": "168", "hashOfConfig": "150"}, {"size": 3179, "mtime": 1736925243048, "results": "169", "hashOfConfig": "150"}, {"size": 1093, "mtime": 1717214673453, "results": "170", "hashOfConfig": "150"}, {"size": 2791, "mtime": 1749900925141, "results": "171", "hashOfConfig": "150"}, {"size": 2957, "mtime": 1735728289900, "results": "172", "hashOfConfig": "150"}, {"size": 4708, "mtime": 1749901289912, "results": "173", "hashOfConfig": "150"}, {"size": 8047, "mtime": 1749901213424, "results": "174", "hashOfConfig": "150"}, {"size": 2075, "mtime": 1749901378172, "results": "175", "hashOfConfig": "150"}, {"size": 1170, "mtime": 1735118812868, "results": "176", "hashOfConfig": "150"}, {"size": 392, "mtime": 1735130123257, "results": "177", "hashOfConfig": "150"}, {"size": 487, "mtime": 1717214673454, "results": "178", "hashOfConfig": "150"}, {"size": 25024, "mtime": 1749872326200, "results": "179", "hashOfConfig": "150"}, {"size": 10349, "mtime": 1736136046889, "results": "180", "hashOfConfig": "150"}, {"size": 564, "mtime": 1736056157631, "results": "181", "hashOfConfig": "150"}, {"size": 1405, "mtime": 1744364111341, "results": "182", "hashOfConfig": "150"}, {"size": 3049, "mtime": 1749901631124, "results": "183", "hashOfConfig": "150"}, {"size": 1972, "mtime": 1736137622016, "results": "184", "hashOfConfig": "150"}, {"size": 8050, "mtime": 1735564130804, "results": "185", "hashOfConfig": "150"}, {"size": 3109, "mtime": 1744533295109, "results": "186", "hashOfConfig": "150"}, {"size": 842, "mtime": 1749872326309, "results": "187", "hashOfConfig": "150"}, {"size": 5530, "mtime": 1749902239166, "results": "188", "hashOfConfig": "150"}, {"size": 9784, "mtime": 1749902388135, "results": "189", "hashOfConfig": "150"}, {"size": 2240, "mtime": 1749902562866, "results": "190", "hashOfConfig": "150"}, {"size": 16735, "mtime": 1736092875307, "results": "191", "hashOfConfig": "150"}, {"size": 4064, "mtime": 1749902615557, "results": "192", "hashOfConfig": "150"}, {"size": 5722, "mtime": 1749872326401, "results": "193", "hashOfConfig": "150"}, {"size": 8172, "mtime": 1749902729790, "results": "194", "hashOfConfig": "150"}, {"size": 4558, "mtime": 1735711613517, "results": "195", "hashOfConfig": "150"}, {"size": 2169, "mtime": 1735614305664, "results": "196", "hashOfConfig": "150"}, {"size": 2193, "mtime": 1735728290130, "results": "197", "hashOfConfig": "150"}, {"size": 2077, "mtime": 1735558420288, "results": "198", "hashOfConfig": "150"}, {"size": 1553, "mtime": 1749902333386, "results": "199", "hashOfConfig": "150"}, {"size": 1257, "mtime": 1735530340386, "results": "200", "hashOfConfig": "150"}, {"size": 2133, "mtime": 1735559715312, "results": "201", "hashOfConfig": "150"}, {"size": 4629, "mtime": 1735920407316, "results": "202", "hashOfConfig": "150"}, {"size": 9229, "mtime": 1735814464705, "results": "203", "hashOfConfig": "150"}, {"size": 2497, "mtime": 1735557403076, "results": "204", "hashOfConfig": "150"}, {"size": 1647, "mtime": 1735918913727, "results": "205", "hashOfConfig": "150"}, {"size": 7210, "mtime": 1749872326534, "results": "206", "hashOfConfig": "150"}, {"size": 4631, "mtime": 1744364213615, "results": "207", "hashOfConfig": "150"}, {"size": 8278, "mtime": 1749872326552, "results": "208", "hashOfConfig": "150"}, {"size": 5216, "mtime": 1735722776228, "results": "209", "hashOfConfig": "150"}, {"size": 2602, "mtime": 1735462308585, "results": "210", "hashOfConfig": "150"}, {"size": 9224, "mtime": 1735377660815, "results": "211", "hashOfConfig": "150"}, {"size": 3124, "mtime": 1735557300633, "results": "212", "hashOfConfig": "150"}, {"size": 1213, "mtime": 1735557760977, "results": "213", "hashOfConfig": "150"}, {"size": 1403, "mtime": 1735445654174, "results": "214", "hashOfConfig": "150"}, {"size": 3413, "mtime": 1735668876759, "results": "215", "hashOfConfig": "150"}, {"size": 1087, "mtime": 1710034848325, "results": "216", "hashOfConfig": "150"}, {"size": 2072, "mtime": 1735668459616, "results": "217", "hashOfConfig": "150"}, {"size": 2220, "mtime": 1749807544027, "results": "218", "hashOfConfig": "150"}, {"size": 7189, "mtime": 1749807544035, "results": "219", "hashOfConfig": "150"}, {"size": 7141, "mtime": 1749807709386, "results": "220", "hashOfConfig": "150"}, {"size": 1361, "mtime": 1735219702794, "results": "221", "hashOfConfig": "150"}, {"size": 251, "mtime": 1735219706128, "results": "222", "hashOfConfig": "150"}, {"size": 6257, "mtime": 1749807441726, "results": "223", "hashOfConfig": "150"}, {"size": 9949, "mtime": 1735564247160, "results": "224", "hashOfConfig": "150"}, {"size": 4239, "mtime": 1749877230724, "results": "225", "hashOfConfig": "150"}, {"size": 2454, "mtime": 1736140195035, "results": "226", "hashOfConfig": "150"}, {"size": 1024, "mtime": 1717214673457, "results": "227", "hashOfConfig": "150"}, {"size": 1594, "mtime": 1735306313844, "results": "228", "hashOfConfig": "150"}, {"size": 1281, "mtime": 1735823247858, "results": "229", "hashOfConfig": "150"}, {"size": 36085, "mtime": 1749881945346, "results": "230", "hashOfConfig": "150"}, {"size": 12475, "mtime": 1749872326679, "results": "231", "hashOfConfig": "150"}, {"size": 16197, "mtime": 1735306313844, "results": "232", "hashOfConfig": "150"}, {"size": 1225, "mtime": 1735806030732, "results": "233", "hashOfConfig": "150"}, {"size": 1280, "mtime": 1736140432663, "results": "234", "hashOfConfig": "150"}, {"size": 1147, "mtime": 1736069792446, "results": "235", "hashOfConfig": "150"}, {"size": 617, "mtime": 1722994436174, "results": "236", "hashOfConfig": "150"}, {"size": 787, "mtime": 1735715231265, "results": "237", "hashOfConfig": "150"}, {"size": 971, "mtime": 1735219731265, "results": "238", "hashOfConfig": "150"}, {"size": 1670, "mtime": 1749881945435, "results": "239", "hashOfConfig": "150"}, {"size": 676, "mtime": 1735639044899, "results": "240", "hashOfConfig": "150"}, {"size": 1344, "mtime": 1736684685344, "results": "241", "hashOfConfig": "150"}, {"size": 3031, "mtime": 1735728526227, "results": "242", "hashOfConfig": "150"}, {"size": 961, "mtime": 1744363936892, "results": "243", "hashOfConfig": "150"}, {"size": 5431, "mtime": 1736091429082, "results": "244", "hashOfConfig": "150"}, {"size": 7336, "mtime": 1736090987054, "results": "245", "hashOfConfig": "150"}, {"size": 4304, "mtime": 1736090987071, "results": "246", "hashOfConfig": "150"}, {"size": 143, "mtime": 1736089763475, "results": "247", "hashOfConfig": "150"}, {"size": 788, "mtime": 1736088350651, "results": "248", "hashOfConfig": "150"}, {"size": 848, "mtime": 1736088350655, "results": "249", "hashOfConfig": "150"}, {"size": 2222, "mtime": 1735619321920, "results": "250", "hashOfConfig": "150"}, {"size": 516, "mtime": 1735617787370, "results": "251", "hashOfConfig": "150"}, {"size": 2146, "mtime": 1735617886793, "results": "252", "hashOfConfig": "150"}, {"size": 4420, "mtime": 1749871915594, "results": "253", "hashOfConfig": "150"}, {"size": 933, "mtime": 1735617793693, "results": "254", "hashOfConfig": "150"}, {"size": 2947, "mtime": 1735714136936, "results": "255", "hashOfConfig": "150"}, {"size": 1468, "mtime": 1736041149415, "results": "256", "hashOfConfig": "150"}, {"size": 655, "mtime": 1735482535867, "results": "257", "hashOfConfig": "150"}, {"size": 1536, "mtime": 1749806311255, "results": "258", "hashOfConfig": "150"}, {"size": 4603, "mtime": 1749874196740, "results": "259", "hashOfConfig": "150"}, {"size": 1036, "mtime": 1749872624727, "results": "260", "hashOfConfig": "150"}, {"size": 926, "mtime": 1735823269600, "results": "261", "hashOfConfig": "150"}, {"size": 1843, "mtime": 1735219744936, "results": "262", "hashOfConfig": "150"}, {"size": 4464, "mtime": 1735469170981, "results": "263", "hashOfConfig": "150"}, {"size": 1596, "mtime": 1749873118721, "results": "264", "hashOfConfig": "150"}, {"size": 1432, "mtime": 1735379838173, "results": "265", "hashOfConfig": "150"}, {"size": 1237, "mtime": 1749873112893, "results": "266", "hashOfConfig": "150"}, {"size": 1914, "mtime": 1749873098020, "results": "267", "hashOfConfig": "150"}, {"size": 2721, "mtime": 1744364594233, "results": "268", "hashOfConfig": "150"}, {"size": 1888, "mtime": 1749806344887, "results": "269", "hashOfConfig": "150"}, {"size": 3876, "mtime": 1735092325135, "results": "270", "hashOfConfig": "150"}, {"size": 3045, "mtime": 1735044717747, "results": "271", "hashOfConfig": "150"}, {"size": 4154, "mtime": 1749873137279, "results": "272", "hashOfConfig": "150"}, {"size": 799, "mtime": 1749873083010, "results": "273", "hashOfConfig": "150"}, {"size": 734, "mtime": 1749873103090, "results": "274", "hashOfConfig": "150"}, {"size": 1254, "mtime": 1735308385357, "results": "275", "hashOfConfig": "150"}, {"size": 1666, "mtime": 1735467133173, "results": "276", "hashOfConfig": "150"}, {"size": 5658, "mtime": 1735445117874, "results": "277", "hashOfConfig": "150"}, {"size": 780, "mtime": 1735095159645, "results": "278", "hashOfConfig": "150"}, {"size": 4309, "mtime": 1735049195731, "results": "279", "hashOfConfig": "150"}, {"size": 264, "mtime": 1735095159661, "results": "280", "hashOfConfig": "150"}, {"size": 1162, "mtime": 1735044925140, "results": "281", "hashOfConfig": "150"}, {"size": 2787, "mtime": 1735467133237, "results": "282", "hashOfConfig": "150"}, {"size": 1912, "mtime": 1735100605419, "results": "283", "hashOfConfig": "150"}, {"size": 4887, "mtime": 1749873107900, "results": "284", "hashOfConfig": "150"}, {"size": 792, "mtime": 1735114609389, "results": "285", "hashOfConfig": "150"}, {"size": 1170, "mtime": 1735715194519, "results": "286", "hashOfConfig": "150"}, {"size": 1482, "mtime": 1709382339058, "results": "287", "hashOfConfig": "150"}, {"size": 99, "mtime": 1749897385019, "results": "288", "hashOfConfig": "150"}, {"size": 1445, "mtime": 1717214673462, "results": "289", "hashOfConfig": "150"}, {"size": 202, "mtime": 1709382339059, "results": "290", "hashOfConfig": "150"}, {"size": 169, "mtime": 1735033576480, "results": "291", "hashOfConfig": "150"}, {"size": 369, "mtime": 1735637702073, "results": "292", "hashOfConfig": "150"}, {"size": 4310, "mtime": 1735135249678, "results": "293", "hashOfConfig": "150"}, {"size": 209, "mtime": 1735135249681, "results": "294", "hashOfConfig": "150"}, {"size": 5204, "mtime": 1735830986882, "results": "295", "hashOfConfig": "150"}, {"size": 120, "mtime": 1735795191578, "results": "296", "hashOfConfig": "150"}, {"size": 2068, "mtime": 1735828188193, "results": "297", "hashOfConfig": "150"}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hhvykh", {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/eruda-debug.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/holdings-indicator.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/market-status-indicator.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/_components/root-layout-client.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/fetch-risk-signals.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/get-all-cached-instruments.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/get-equity-table.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/get-instrument-detail.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/ibapi/auto-connection.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/ibapi/connection.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/notifications-server.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/stock-picks.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/actions/upsert-user-profile-settings.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/admin/actions/populate-cached-instrument-details.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/admin/instruments/_components/cached-instrument-table.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/admin/instruments/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/auth.config.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/auth.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/risk-range-signals/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/risk-signal-visualization/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/hedgeye/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/order/auto-order/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/notify-trend-changes/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/subscribe/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/unsubscribe/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/push/vapid-key/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/api/sendemail/route.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/_components/dashboard-main.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/actions/fetch-dashboard-analytics.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/dashboard/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/layout.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/models/cache-symbols.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/models/symbols.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/orders/components/option-order-summary.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/orders/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/auto-order-prefill-list.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/create-order.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/order-dialog.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/orders-list.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/portfolio-page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/portfolio-stats.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/_components/positions-list.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/__tests__/get-holdings.test.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/cancel-order.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/create-auto-order-prefill.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/create-order.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/fetch-auto-order-prefill.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-all-accounts.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-all-orders.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-holdings.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-portfolio-all-data.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/actions/get-portfolio-data.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/portfolio/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/IBAccountDetails.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/IBKRConnectionForm.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/NotificationPreferences.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/_components/ibkr-account-settings.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/_components/user-role-display.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/ib-actions.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/ibkr-fetch-account.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/ibkr-test-connection.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/actions/update-user-role.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/profile-sheet.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/profile/send-email-button-test.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/date-picker.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/desktop-table-view.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/entry-window-card.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/info-circle-popover.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/input-file.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/mobile-card-view.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/option-chain.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/settings-panel-drawer.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/settings-panel.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/skeleton-loader.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/stock-picks-settings.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/trend-change-archive.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/trend-change.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/trend-changes-from-previous-date.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/upside-downside-visualization.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/_components/watchlist-button.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/fetch-openfigi.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/get-option-chains.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/get-risk-signal-for-single-ticker.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/option-limit-order.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/risk-signals/server-actions/trend-change.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/services/hedgeye.service.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/services/notification.service.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/services/order.service.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/[accountId]/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/_components/account-details.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/actions/__tests__/get-account-transactions.test.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/trading-account/actions/get-account-transactions.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/utils/ibkr-errors.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/utils/ibkr-utils.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/utils/promise-utils.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/AddStockDialog.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/EmptyWatchlist.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/RemoveStockDialog.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistGrid.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/_components/WatchlistHeader.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/actions.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/app/watchlist/page.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/error-boundary.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/footer-nav.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/header.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/main-nav.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/spinner-basic.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/theme-toggle.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/alert-dialog.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/alert.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/avatar.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/badge.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/button.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/calendar.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/card.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/dialog.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/drawer.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/form.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/input.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/label.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/popover.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/select.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/separator.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/sheet.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/switch.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/table.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/tabs.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/toast.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/toaster.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/logger.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/prisma.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/utils-client.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/utils-server.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/lib/utils.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/date-hedgeye.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/notifications.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/pwa.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/yahoo-finance/fetch-quote.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/yahoo-finance/init.ts", [], [], "/Users/<USER>/Dev-Workspaces/milosacademy/trading_platform/lunar-hedge/apps/web/utils/yahoo-finance/is-market-open.ts", [], []]