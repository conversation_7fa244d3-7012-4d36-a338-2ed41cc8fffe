import type { Config } from "jest";
import { pathsToModuleNameMapper } from "ts-jest";

const { compilerOptions } = require("./tsconfig.json");

const config: Config = {
  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: true,
  coverageDirectory: "coverage",
  coverageProvider: "v8",

  // The root directory that <PERSON><PERSON> should scan for tests and modules within
  rootDir: ".",

  // A list of paths to directories that <PERSON><PERSON> should use to search for files in
  roots: ["<rootDir>"],

  // Use pathsToModuleNameMapper to properly handle path aliases
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: "<rootDir>/",
  }) || {},

  // The test environment that will be used for testing
  testEnvironment: "node",

  // A map from regular expressions to paths to transformers
  transform: {
    "^.+\\.tsx?$": [
      "ts-jest",
      {
        tsconfig: "tsconfig.json",
      },
    ],
  },

  // The glob patterns Je<PERSON> uses to detect test files
  testMatch: ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[tj]s?(x)"],

  // An array of regexp pattern strings that are matched against all test paths
  testPathIgnorePatterns: ["/node_modules/", "/.next/"],

  // An array of regexp pattern strings that are matched against all source file paths
  watchPathIgnorePatterns: ["/node_modules/", "/.next/"],

  // Add moduleDirectories to help with module resolution
  moduleDirectories: ["node_modules", "<rootDir>"],

  // Files to run before tests
  setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"],

  // Automatically clear mock calls and instances between every test
  clearMocks: true,

  // Indicates whether each individual test should be reported during the run
  verbose: true,
};

export default config;
