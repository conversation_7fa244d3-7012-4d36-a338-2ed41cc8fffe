"use server";

import prismadb from "@/lib/prisma";

export const getTrendChangesForDate = async (
  targetDate: Date,
  useClosestDate: boolean = true,
) => {
  console.info("1. Input targetDate:", targetDate);

  // Validate if the targetDate is a valid Date object
  if (isNaN(targetDate.getTime())) {
    console.info("2. Invalid date detected:", targetDate);
    return [];
  }

  // Convert targetDate to a string with format 'YYYY-MM-DD'
  let dateString = targetDate.toISOString().split("T")[0];
  console.info("3. Converted dateString:", dateString);

  const archives = await getArchivesOfRiskSignals();
  console.info("4. Retrieved archives:", {
    archiveDates: archives.trendChanges.map(
      (d) => d.toISOString().split("T")[0],
    ),
  });

  // Make sure to compare only the date part of the targetDate
  const dateExists = archives.trendChanges.some(
    (date) => date.toISOString().split("T")[0] === dateString,
  );
  console.info("5. Date exists in archives?", dateExists);

  if (!dateExists && useClosestDate) {
    console.info("6. Target date not found, finding closest date...");
    // If the target date is not in the archives, find the closest date in the archives
    if (archives.trendChanges.length === 0) {
      throw new Error("No trend changes available in archives");
    }

    const closestDate = archives.trendChanges.reduce((closest, date) => {
      const currentDiff = Math.abs(date.getTime() - targetDate.getTime());
      const closestDiff = Math.abs(closest!.getTime() - targetDate.getTime());
      const result = currentDiff < closestDiff ? date : closest;
      console.info("6a. Comparing dates:", {
        date: date.toISOString(),
        diff: currentDiff,
        isCloser: currentDiff < closestDiff,
      });
      return result;
    }, archives.trendChanges[0]);

    dateString = closestDate!.toISOString().split("T")[0];
    console.info("7. Found closest date:", dateString);
  }

  console.info("8. Querying database with dateString:", dateString);
  const result =
    await prismadb.$queryRaw`SELECT id, original_index as "originalIndex", index, description, trend, buy_trade as "buyTrade", sell_trade as "sellTrade", previous_close as "previousClose", date FROM "trend_change" WHERE CAST(date AS DATE) = CAST(${dateString} AS DATE);`;

  // Convert Decimal values to regular numbers
  const resultTrendChanges = (result as any[]).map((item) => ({
    ...item,
    buyTrade:
      typeof item.buyTrade === "object" && item.buyTrade !== null
        ? Number(item.buyTrade.toString())
        : item.buyTrade,
    sellTrade:
      typeof item.sellTrade === "object" && item.sellTrade !== null
        ? Number(item.sellTrade.toString())
        : item.sellTrade,
    previousClose:
      typeof item.previousClose === "object" && item.previousClose !== null
        ? Number(item.previousClose.toString())
        : item.previousClose,
  })) as TrendChange[];

  console.info("9. Query results:", {
    count: resultTrendChanges.length,
    firstResult: resultTrendChanges[0],
  });

  return resultTrendChanges;
};

export async function getTrendChangesByDateOrMostRecent(
  date: Date | undefined,
) {
  const dateOnly = date ? new Date(date) : new Date();

  // Ensure we're working with the correct date by setting to noon UTC
  dateOnly.setUTCHours(12, 0, 0, 0);

  const trendChanges = await getTrendChangesForDate(dateOnly);

  if (trendChanges.length === 0) {
    const mostRecentTrendChange = await prismadb.trendChange.findFirst({
      orderBy: {
        date: "desc",
      },
    });
    const mostRecentTrendChanges = await getTrendChangesForDate(
      mostRecentTrendChange?.date
        ? new Date(mostRecentTrendChange.date.setUTCHours(12, 0, 0, 0))
        : new Date(),
    );
    return JSON.stringify(mostRecentTrendChanges);
  }

  // Ensure all values are serializable
  const serializableTrendChanges = trendChanges.map((tc) => ({
    ...tc,
    date: tc.date.toISOString(),
  }));

  return JSON.stringify(serializableTrendChanges);
}

export async function saveTrendChanges(
  trendChanges: TrendChange[],
): Promise<number> {
  // Check if array is empty
  if (trendChanges.length === 0) {
    return 0;
  }

  // Check for existing records first
  const existing = await getTrendChangesForDate(trendChanges[0]!.date, false);
  console.info("existing.length ->", existing.length);
  if (existing && existing.length > 0) {
    console.log(
      "Existing trend change already in the database for this date: ",
      trendChanges[0]!.date,
    );
    return 0;
  }

  // If no duplicates, proceed with saving
  const dataToSave = trendChanges.map((tc, index) => ({
    ...tc,
    originalIndex: tc.originalIndex ?? index, // Provide fallback value
    // Ensure numeric values are properly formatted
    buyTrade:
      typeof tc.buyTrade === "number" ? tc.buyTrade : Number(tc.buyTrade),
    sellTrade:
      typeof tc.sellTrade === "number" ? tc.sellTrade : Number(tc.sellTrade),
    previousClose:
      typeof tc.previousClose === "number"
        ? tc.previousClose
        : Number(tc.previousClose),
  }));

  const savedTrendChanges = await prismadb.trendChange.createMany({
    data: dataToSave,
  });
  return savedTrendChanges.count;
}

export async function getArchivesOfRiskSignals(): Promise<{
  trendChanges: Date[];
  visualization: Date[];
}> {
  try {
    const tc =
      (await prismadb.trendChange.groupBy({
        by: ["date"],
        _max: {
          date: true,
        },
        orderBy: {
          date: "desc",
        },
      })) || [];

    const v =
      (await prismadb.upsideDownsidePotential.groupBy({
        by: ["date"],
        _max: {
          date: true,
        },
        orderBy: {
          date: "desc",
        },
      })) || [];

    const ret = {
      trendChanges: tc
        .map((entry: any) => entry._max.date)
        .filter((date: any): date is Date => date !== null),
      visualization: v
        .map((entry: any) => entry._max.date)
        .filter((date: any): date is Date => date !== null),
    };

    return ret;
  } catch (error) {
    console.error("Error fetching archives of risk signals:", error);
    return { trendChanges: [], visualization: [] };
  }
}

export async function getLastSyncedTrendChangeDate(): Promise<string> {
  const trendChanges = await getArchivesOfRiskSignals();
  if (trendChanges.trendChanges.length === 0) {
    throw new Error("No trend changes found in archives");
  }
  return trendChanges.trendChanges[0]!.toISOString();
}

export type TrendChange = {
  index: string;
  trend: string;
  description?: string;
  buyTrade: number;
  sellTrade: number;
  previousClose: number;
  date: Date;
  originalIndex?: number;
  isStock?: boolean;
};
