"use server";

import { getUserProfile } from "@/db/user-profile";
import prismaDb from "@/lib/prisma";

interface AutoOrderPrefillRecord {
  id: string;
  user_id: string;
  action: string;
  ticker: string;
  quantity: number;
  price: number;
  order_type: string;
  account_id: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  metadata: any;
}

export interface AutoOrderPrefillWithDefaults extends AutoOrderPrefillRecord {
  isEnabled: boolean;
}

export async function fetchAutoOrderPrefill(
  userId: string,
): Promise<AutoOrderPrefillWithDefaults[]> {
  if (!userId) {
    throw new Error("User ID is required");
  }

  try {
    // First check if auto order prefill is enabled for the user
    const userProfile = await getUserProfile(userId);
    const isEnabled = userProfile?.settings?.enableAutoOrderPrefill ?? false;

    // If not enabled, return empty array with proper typing
    if (!isEnabled) {
      return [];
    }

    // Fetch prefill records if enabled
    const prefillRecords = await prismaDb.autoOrderPrefill.findMany({
      where: {
        user_id: userId,
        deleted_at: null, // Only get active records
      },
      orderBy: {
        created_at: "desc", // Get most recent first
      },
    });

    // Add isEnabled flag to each record
    return prefillRecords.map((record: AutoOrderPrefillRecord) => ({
      ...record,
      isEnabled,
    }));
  } catch (error) {
    console.error("[FETCH_AUTO_ORDER_PREFILL_ERROR]", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch auto order prefill records",
    );
  }
}
