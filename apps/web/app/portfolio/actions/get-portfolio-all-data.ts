"use server"; // Marks this as a Next.js server action

import { createIBApiConnection } from "@/app/actions/ibapi/connection";
import { testIBKRConnection } from "@/app/profile/actions/ibkr-test-connection";
import { fetchQuotes } from "@/utils/yahoo-finance/fetch-quote";
import { EventName, OrderType, SecType } from "@stoqey/ib";

// Type definition for a single portfolio position
export type PortfolioPosition = {
  symbol: string; // Stock/Asset symbol
  shares: number; // Number of shares held
  price: number; // Current price per share
  value: number; // Total value of position (shares * price)
  avgCost: number; // Average cost per share
  return: number; // Return on investment for this position
};

// Type definition for portfolio summary data
export type PortfolioSummary = {
  accountNumber: string; // IB account identifier
  totalValue: number; // Total portfolio value
  dayChange: number; // Change in value for the current day
  dayChangePercent: number; // Percentage change for the current day
  totalReturn: number; // Total return across all positions
  totalReturnPercent: number; // Total return as a percentage
  cashBalance: number; // Available cash in account
  cashBalancePercent: number; // Percentage of portfolio in cash
  positions: PortfolioPosition[]; // Array of all positions
};

// Extended interface that includes orders along with portfolio data
export interface PortfolioData extends PortfolioSummary {
  positions: PortfolioPosition[];
  orders: {
    // Array of order information
    orderId: number; // Unique order identifier
    symbol: string; // Stock symbol for the order
    action: "BUY" | "SELL"; // Order direction
    quantity: number; // Number of shares
    price: number; // Order price
    status: "Submitted" | "Filled" | "Cancelled" | "Error"; // Current order status
    timestamp: Date; // Time of order
    orderType: OrderType; // Type of order (market, limit, etc.)
  }[];
}

// Delayed/Snapshot market data
export async function fetchPortfolioAllData(
  userId: string,
): Promise<PortfolioData[] | null> {
  try {
    const isConnected = await testIBKRConnection(userId);
    if (!isConnected) {
      return null;
    }

    const ib = await createIBApiConnection(userId);
    const accountsData: { [key: string]: PortfolioData } = {};

    const dataPromise = new Promise<PortfolioData[]>((resolve, reject) => {
      // Increase timeout for slower connections
      const timeout = setTimeout(() => {
        ib.disconnect();
        reject(new Error("Timeout waiting for data"));
      }, 15000); // Increased to 15 seconds

      let accountSummaryReceived = false;
      let positionsReceived = false;
      let positionsProcessed = 0;
      let totalPositions = 0;

      // Add a map to track unique positions
      const positionMap = new Map<
        string,
        {
          account: string;
          symbol: string;
          shares: number;
          avgCost: number;
        }
      >();

      const checkComplete = () => {
        if (accountSummaryReceived && positionsReceived) {
          clearTimeout(timeout);
          ib.disconnect();
          resolve(Object.values(accountsData));
        }
      };

      // Error handler
      ib.on(EventName.error, (err, code, reqId) => {
        console.error(
          `Error: ${err.message} - code: ${code} - reqId: ${reqId}`,
        );
      });

      // Account summary handler
      ib.on(
        EventName.accountSummary,
        (reqId, account, tag, value, currency) => {
          if (!accountsData[account]) {
            accountsData[account] = {
              accountNumber: account,
              totalValue: 0,
              dayChange: 0,
              dayChangePercent: 0,
              totalReturn: 0,
              totalReturnPercent: 0,
              cashBalance: 0,
              cashBalancePercent: 0,
              positions: [],
              orders: [],
            };
          }
          const portfolioData = accountsData[account];
          switch (tag) {
            case "NetLiquidation":
              portfolioData.totalValue = parseFloat(value);
              break;
            case "AvailableFunds":
              portfolioData.cashBalance = parseFloat(value);
              break;
            case "UnrealizedPnL":
              portfolioData.totalReturn = parseFloat(value);
              break;
            case "DayPnL":
              portfolioData.dayChange = parseFloat(value);
              break;
          }
        },
      );

      // Function to process positions
      const processPositions = async (
        positions: Array<{
          account: string;
          symbol: string;
          shares: number;
          avgCost: number;
        }>,
      ) => {
        if (positions.length === 0) return;

        const symbols = positions.map((p) => p.symbol);
        const quotes = await fetchQuotes(symbols);

        positions.forEach((position) => {
          const quote = quotes[position.symbol];
          if (!quote) return; // Skip if quote not found

          const yfQuote = quote.regularMarketPrice;

          const positionValue = position.shares * yfQuote;
          const costBasis = position.shares * position.avgCost;
          const positionReturn =
            ((positionValue - costBasis) / costBasis) * 100;

          // Ensure account exists
          if (!accountsData[position.account]) {
            accountsData[position.account] = {
              accountNumber: position.account,
              totalValue: 0,
              dayChange: 0,
              dayChangePercent: 0,
              totalReturn: 0,
              totalReturnPercent: 0,
              cashBalance: 0,
              cashBalancePercent: 0,
              positions: [],
              orders: [],
            };
          }

          // Clear existing positions for this symbol if any
          accountsData[position.account]!.positions = accountsData[
            position.account
          ]!.positions.filter((p) => p.symbol !== position.symbol);

          // Add the aggregated position
          accountsData[position.account]!.positions.push({
            symbol: position.symbol,
            shares: position.shares,
            price: yfQuote,
            value: positionValue,
            avgCost: position.avgCost,
            return: positionReturn,
          });
        });
      };

      // Position handler
      ib.on(EventName.position, async (account, contract, pos, avgCost) => {
        if (!contract.symbol) return;

        // Create unique key for position (account + symbol)
        const positionKey = `${account}-${contract.symbol}`;

        // Update or add to position map
        const existingPosition = positionMap.get(positionKey);
        if (existingPosition) {
          existingPosition.shares += pos;
          existingPosition.avgCost =
            (existingPosition.avgCost * (existingPosition.shares - pos) +
              avgCost! * pos) /
            existingPosition.shares;
        } else {
          totalPositions++;
          positionMap.set(positionKey, {
            account,
            symbol: contract.symbol,
            shares: pos,
            avgCost: avgCost!,
          });
        }

        if (!accountsData[account]) {
          accountsData[account] = {
            accountNumber: account,
            totalValue: 0,
            dayChange: 0,
            dayChangePercent: 0,
            totalReturn: 0,
            totalReturnPercent: 0,
            cashBalance: 0,
            cashBalancePercent: 0,
            positions: [],
            orders: [],
          };
        }

        // Convert map to array for batch processing
        const pendingPositions = Array.from(positionMap.values());

        // Process in batches of 20 symbols or when all positions are received
        if (pendingPositions.length >= 20) {
          await processPositions(pendingPositions);
          positionsProcessed = positionMap.size;
        }
      });

      // Position end handler
      ib.on(EventName.positionEnd, async () => {
        // Process any remaining positions
        const remainingPositions = Array.from(positionMap.values());
        await processPositions(remainingPositions);

        positionsProcessed = positionMap.size;
        positionsReceived = true;

        if (accountSummaryReceived) {
          checkComplete();
        }
      });

      // Account summary end handler
      ib.on(EventName.accountSummaryEnd, () => {
        for (const account of Object.values(accountsData)) {
          // Calculate day change percentage
          account.dayChangePercent =
            account.totalValue !== 0
              ? (account.dayChange / (account.totalValue - account.dayChange)) *
                100
              : 0;

          // Calculate total return percentage
          account.totalReturnPercent =
            account.totalValue !== 0
              ? (account.totalReturn /
                  (account.totalValue - account.totalReturn)) *
                100
              : 0;

          // Calculate cash balance percentage
          account.cashBalancePercent =
            account.totalValue !== 0
              ? (account.cashBalance / account.totalValue) * 100
              : 0;
        }
        accountSummaryReceived = true;
        checkComplete();
      });

      // Start the data collection process
      ib.connect();

      // Request account summary
      ib.reqAccountSummary(
        1,
        "All",
        "NetLiquidation,AvailableFunds,UnrealizedPnL,DayPnL",
      );

      // Request positions
      ib.reqPositions();
    });

    return await dataPromise;
  } catch (error) {
    console.error("Error fetching portfolio data:", error);
    return null;
  }
}
