import { fetchHoldingsData, PositionData } from "../get-holdings";
import { fetchQuote } from "@/utils/yahoo-finance/fetch-quote";
import { EventEmitter } from "events";
import { EventName } from "@stoqey/ib";
import { createIBApiConnection } from "@/app/actions/ibapi/connection";

// Mock dependencies
jest.mock("@/app/actions/ibapi/connection");
jest.mock("@/utils/yahoo-finance/fetch-quote");

describe("fetchHoldingsData", () => {
  let mockIB: EventEmitter & {
    connect: jest.Mock;
    disconnect: jest.Mock;
    reqPositions: jest.Mock;
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();

    // Create a new EventEmitter instance for each test
    mockIB = new EventEmitter() as any;
    mockIB.connect = jest.fn();
    mockIB.disconnect = jest.fn();
    mockIB.reqPositions = jest.fn();
    mockIB.setMaxListeners(20); // Increase max listeners to prevent warnings

    (createIBApiConnection as jest.Mock).mockResolvedValue(mockIB);
    (fetchQuote as jest.Mock).mockResolvedValue({ regularMarketPrice: 150.0 });
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  it("should successfully fetch positions data", async () => {
    const dataPromise = fetchHoldingsData("testUserId");

    // Use setImmediate instead of process.nextTick for better test stability
    setImmediate(() => {
      mockIB.emit("position", "testAccount", { symbol: "AAPL" }, 10, 140.0);
      mockIB.emit("position", "testAccount", { symbol: "GOOGL" }, 5, 2500.0);
      mockIB.emit("positionEnd");
    });

    const result = await dataPromise;

    expect(result).toEqual([
      {
        symbol: "AAPL",
        shares: 10,
        price: 150.0,
        value: 1500.0,
        avgCost: 140.0,
        return: 7.***************,
      },
      {
        symbol: "GOOGL",
        shares: 5,
        price: 150.0,
        value: 750.0,
        avgCost: 2500.0,
        return: -94,
      },
    ]);

    expect(createIBApiConnection).toHaveBeenCalledWith("testUserId");
    expect(mockIB.connect).toHaveBeenCalled();
    expect(mockIB.reqPositions).toHaveBeenCalled();
    expect(mockIB.disconnect).toHaveBeenCalled();
  });

  it("should handle positions with undefined symbols", async () => {
    const dataPromise = fetchHoldingsData("testUserId");

    setImmediate(() => {
      mockIB.emit("position", "testAccount", { symbol: undefined }, 10, 100.0);
      mockIB.emit("positionEnd");
    });

    const result = await dataPromise;
    expect(result).toEqual([]);
  });

  it("should handle Yahoo Finance API errors", async () => {
    (fetchQuote as jest.Mock).mockRejectedValue(new Error("API Error"));

    const dataPromise = fetchHoldingsData("testUserId");

    setImmediate(() => {
      mockIB.emit("position", "testAccount", { symbol: "AAPL" }, 10, 140.0);
      mockIB.emit("positionEnd");
    });

    const result = await dataPromise;
    expect(result).toEqual([
      {
        symbol: "AAPL",
        shares: 10,
        price: 0,
        value: 0,
        avgCost: 140.0,
        return: -100,
      },
    ]);
  });

  it("should handle IB connection errors", async () => {
    (createIBApiConnection as jest.Mock).mockRejectedValue(
      new Error("Connection failed"),
    );

    const result = await fetchHoldingsData("testUserId");
    expect(result).toBeNull();
  });

  it("should handle timeout", async () => {
    jest.useFakeTimers();

    const dataPromise = fetchHoldingsData("testUserId");

    // Run all pending timers
    await jest.runAllTimersAsync();

    await expect(dataPromise).rejects.toThrow(
      "Timeout waiting for positions data",
    );

    jest.useRealTimers();
  });

  it("should handle IB API errors during position fetching", async () => {
    const errorHandler = jest.fn();
    mockIB.on("error", errorHandler);

    const dataPromise = fetchHoldingsData("testUserId");

    setImmediate(() => {
      mockIB.emit("error", new Error("IB API Error"), 500, 1);
      mockIB.emit("positionEnd");
    });

    const result = await dataPromise;
    expect(result).toEqual([]);
    expect(errorHandler).toHaveBeenCalled();
  });

  it("should calculate position returns correctly", async () => {
    (fetchQuote as jest.Mock).mockResolvedValue({ regularMarketPrice: 200.0 });

    const dataPromise = fetchHoldingsData("testUserId");

    setImmediate(() => {
      mockIB.emit("position", "testAccount", { symbol: "TSLA" }, 10, 100.0);
      mockIB.emit("positionEnd");
    });

    const result = await dataPromise;
    if (!result) {
      throw new Error("No result");
    }
    expect(result[0]!.return.toFixed(2)).toBe("100.00");
  });
});
