"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { placeOrder } from "../actions/create-order";
import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { Account } from "../actions/get-all-accounts";

const formSchema = z.object({
  accountId: z.string().min(1, "Account is required"),
  symbol: z
    .string()
    .min(1, "Symbol is required")
    .max(5, "Symbol cannot exceed 5 characters"),
  action: z.enum(["BUY", "SELL"]),
  quantity: z.coerce
    .number()
    .min(1, "Quantity must be at least 1")
    .max(1000000, "Quantity cannot exceed 1,000,000"),
  price: z.coerce
    .number()
    .min(0.01, "Price must be at least 0.01")
    .max(1000000, "Price cannot exceed 1,000,000"),
});

interface CreateOrderProps {
  userId: string;
  accounts: Account[];
  defaultSymbol?: string;
  defaultAction?: "BUY" | "SELL";
  defaultPrice?: number;
  defaultQuantity?: number;
  defaultAccountId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function CreateOrder({
  userId,
  accounts,
  defaultSymbol = "",
  defaultAction = "BUY",
  defaultPrice = 0,
  defaultQuantity = 1,
  defaultAccountId = "",
  onSuccess,
  onCancel,
}: CreateOrderProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      accountId: defaultAccountId,
      symbol: defaultSymbol,
      action: defaultAction,
      quantity: defaultQuantity,
      price: defaultPrice,
    },
  });

  useEffect(() => {
    if (accounts.length === 1) {
      form.setValue("accountId", defaultAccountId);
    }
  }, [accounts, form]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);
      const result = await placeOrder(
        userId,
        values.symbol,
        values.action,
        values.quantity,
        values.price,
        values.accountId,
      );

      if (result) {
        toast({
          title: "Order placed successfully",
          description: `${values.action} ${values.quantity} ${values.symbol} @ $${values.price}`,
        });
        form.reset();
        onSuccess?.();
        router.refresh();
      } else {
        toast({
          variant: "destructive",
          title: "Failed to place order",
          description: "Please check your connection details and try again.",
        });
      }
    } catch (error) {
      console.error("Error placing order:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Place Order</CardTitle>
        <CardDescription>
          Enter the details below to place a stock order
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {accounts.length === 1 ? (
              <FormField
                control={form.control}
                name="accountId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account</FormLabel>
                    <FormControl>
                      <div className="p-2 bg-muted rounded-md text-sm">
                        {accounts[0]?.accountId}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="accountId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {accounts.map((account) => (
                          <SelectItem
                            key={account.accountId}
                            value={account.accountId}
                          >
                            {account.accountId}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="symbol"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Symbol</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="AAPL"
                      {...field}
                      className="uppercase"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the stock symbol (e.g., AAPL for Apple Inc.)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="action"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Action</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="BUY">Buy</SelectItem>
                      <SelectItem value="SELL">Sell</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="100"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="150.00"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex flex-row gap-4">
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
                variant={
                  form.getValues("action") === "BUY" ? "default" : "destructive"
                }
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isSubmitting
                  ? "Placing Order..."
                  : `Place ${form.getValues("action")} Order`}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="text-xs text-muted-foreground">
        <p>
          Make sure your IBKR connection details are properly configured in your
          profile settings.
        </p>
      </CardFooter>
    </Card>
  );
}
