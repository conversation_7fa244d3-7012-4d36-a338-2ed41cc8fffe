"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Close,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { getTickerListWithChangesBetweenTwoDates } from "@/db/get-ticker-list-with-changes-between-two-dates";
import { useEffect, useState } from "react";
import { TrendChange } from "@/db/trend-change";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ArrowUpDown,
  Loader2,
  StarIcon,
  CalendarIcon,
  ArrowRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Card } from "@/components/ui/card";
import { useMediaQuery } from "@/hooks/use-media-query";
import Watchlist<PERSON><PERSON>on from "./watchlist-button";
import { HoldingsIndicator } from "@/app/_components/holdings-indicator";
import { PortfolioPosition } from "@/app/portfolio/actions/get-portfolio-all-data";
import { format } from "date-fns";
import {
  addToWatchlist,
  removeFromWatchlist,
  getWatchlistItems,
} from "@/app/watchlist/actions";
import { toast } from "@/hooks/use-toast";

interface TrendChangesFromPreviousDateProps {
  selectedTrendChangeDate: Date;
  initialWatchlistItems: string[];
  userId: string | null;
  holdings: PortfolioPosition[];
}

const TrendChangesFromPreviousDate = ({
  selectedTrendChangeDate,
  initialWatchlistItems,
  userId,
  holdings,
}: TrendChangesFromPreviousDateProps) => {
  const [changedTickers, setChangedTickers] = useState<
    { current: TrendChange; previous: TrendChange }[]
  >([]);
  const [loading, setLoading] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [watchlistItems, setWatchlistItems] = useState<string[]>(
    initialWatchlistItems,
  );
  // console.log("TrendChangesFromPreviousDate():session ->", userId);
  // useEffect(() => {
  //   const fetchWatchlistItems = async () => {
  //     if (userId) {
  //       const items = await getWatchlistItems(userId);
  //       setWatchlistItems(items.map((item) => item.ticker));
  //     }
  //   };

  //   fetchWatchlistItems();
  // }, [userId]);

  const isInWatchlist = (symbol: string) => watchlistItems.includes(symbol);

  useEffect(() => {
    const loadChangedTickers = async () => {
      if (!selectedTrendChangeDate) return;

      setLoading(true);
      try {
        const changes = await getTickerListWithChangesBetweenTwoDates(
          selectedTrendChangeDate,
        );
        setChangedTickers(changes);
      } catch (error) {
        console.error("Error loading changed tickers:", error);
        setChangedTickers([]);
      } finally {
        setLoading(false);
      }
    };

    loadChangedTickers();
  }, [selectedTrendChangeDate]);

  const getTrendBadgeColor = (trend: string) => {
    switch (trend) {
      case "BULLISH":
        return "status-bullish border-0";
      case "BEARISH":
        return "status-bearish border-0";
      default:
        return "status-neutral border-0";
    }
  };

  const handleToggleWatchlist = async (symbol: string) => {
    try {
      if (userId) {
        if (watchlistItems.includes(symbol)) {
          // Get current watchlist items to find the ID
          const currentItems = await getWatchlistItems(userId);
          const itemToRemove = currentItems.find(
            (item: { ticker: string; id: string }) => item.ticker === symbol,
          );

          if (itemToRemove) {
            await removeFromWatchlist(itemToRemove.id, userId);
            setWatchlistItems((prev) => prev.filter((item) => item !== symbol));
            toast({
              title: "Removed from watchlist",
              description: `${symbol} has been removed from your watchlist`,
            });
          }
        } else {
          await addToWatchlist(symbol, userId);
          setWatchlistItems((prev) => [...prev, symbol]);
          toast({
            title: "Added to watchlist",
            description: `${symbol} has been added to your watchlist`,
          });
        }
      }
    } catch (error) {
      toast({
        title: "Failed to update watchlist",
        description:
          error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
      // Refresh watchlist items to ensure UI is in sync
      if (userId) {
        const items = await getWatchlistItems(userId);
        setWatchlistItems(items.map((item: { ticker: string }) => item.ticker));
      }
    }
  };

  const renderWatchlistButton = (symbol: string) => {
    if (userId) {
      return (
        <WatchlistButton
          symbol={symbol}
          isInWatchlist={isInWatchlist(symbol)}
          onToggleWatchlist={handleToggleWatchlist}
        />
      );
    }
    return null;
  };

  const formatDate = (date: Date) => {
    return format(date, "MMM dd, yyyy");
  };

  const previousDate = new Date(selectedTrendChangeDate);
  previousDate.setDate(previousDate.getDate() - 1);

  const renderDateComparison = () => (
    <div className="flex items-center justify-center gap-4 mb-4 text-sm">
      <div className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-muted">
        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
        <span>{formatDate(previousDate)}</span>
      </div>
      <ArrowRight className="h-4 w-4 text-muted-foreground" />
      <div className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-primary/10 text-primary">
        <CalendarIcon className="h-4 w-4" />
        <span>{formatDate(selectedTrendChangeDate)}</span>
      </div>
    </div>
  );

  const renderMobileContent = () => (
    <ScrollArea className="h-[400px]">
      <div className="space-y-4 p-4">
        {changedTickers.map(({ current, previous }) => (
          <Card
            key={current.index}
            className={cn(
              "p-4 hover:bg-accent transition-colors",
              isInWatchlist(current.index) && "dark:bg-blue-900/20 bg-blue-50",
            )}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <span className="font-weight-semibold text-size-lg">
                  {current.index}
                </span>
                <HoldingsIndicator symbol={current.index} holdings={holdings} />
              </div>
              {renderWatchlistButton(current.index)}
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="space-y-2 p-3 rounded-lg bg-background border">
                <div className="text-sm text-muted-foreground">
                  <span className="font-medium">Previous</span>
                  <span className="text-xs block">
                    {formatDate(previousDate)}
                  </span>
                </div>
                <Badge
                  className={cn(
                    "w-full justify-center py-2 text-base font-medium",
                    getTrendBadgeColor(previous.trend),
                  )}
                >
                  {previous.trend}
                </Badge>
              </div>

              <div className="space-y-2 p-3 rounded-lg bg-background border">
                <div className="text-sm text-muted-foreground">
                  <span className="font-medium">Current</span>
                  <span className="text-xs block">
                    {formatDate(selectedTrendChangeDate)}
                  </span>
                </div>
                <Badge
                  className={cn(
                    "w-full justify-center py-2 text-base font-medium",
                    getTrendBadgeColor(current.trend),
                  )}
                >
                  {current.trend}
                </Badge>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 bg-muted/30 rounded-lg p-3">
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">Buy Price</div>
                <div className="font-medium text-sm">
                  ${current.buyTrade.toLocaleString()}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">Sell Price</div>
                <div className="font-medium text-sm">
                  ${current.sellTrade.toLocaleString()}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </ScrollArea>
  );

  const renderTableContent = () => (
    <div className="h-[400px] overflow-auto rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Symbol</TableHead>
            <TableHead>
              <div className="flex flex-col">
                <span>Previous Trend</span>
                <span className="text-xs font-normal text-muted-foreground">
                  {formatDate(previousDate)}
                </span>
              </div>
            </TableHead>
            <TableHead>
              <div className="flex flex-col">
                <span>Current Trend</span>
                <span className="text-xs font-normal text-muted-foreground">
                  {formatDate(selectedTrendChangeDate)}
                </span>
              </div>
            </TableHead>
            <TableHead className="text-right">Buy</TableHead>
            <TableHead className="text-right">Sell</TableHead>
            {userId && <TableHead className="w-[100px]">Watchlist</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {changedTickers.map(({ current, previous }) => (
            <TableRow
              key={current.index}
              className={cn(
                isInWatchlist(current.index) &&
                  "dark:bg-blue-900/20 bg-blue-50",
              )}
            >
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  {current.index}
                  <HoldingsIndicator
                    symbol={current.index}
                    holdings={holdings}
                  />
                </div>
              </TableCell>
              <TableCell>
                <Badge className={cn(getTrendBadgeColor(previous.trend))}>
                  {previous.trend}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge className={cn(getTrendBadgeColor(current.trend))}>
                  {current.trend}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                ${current.buyTrade.toString()}
              </TableCell>
              <TableCell className="text-right">
                ${current.sellTrade.toString()}
              </TableCell>
              {userId && (
                <TableCell>{renderWatchlistButton(current.index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        {changedTickers.length > 0 && (
          <Button variant="outline" className="gap-2">
            <ArrowUpDown className="h-4 w-4" />
            {changedTickers.length}{" "}
            {changedTickers.length === 1 ? "symbol" : "symbols"} changed
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Trend Changes Comparison</DialogTitle>
          {renderDateComparison()}
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : changedTickers.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
            <p>No trend changes found between these dates</p>
          </div>
        ) : isMobile ? (
          renderMobileContent()
        ) : (
          renderTableContent()
        )}

        <DialogClose asChild>
          <Button variant="secondary" type="button">
            Close
          </Button>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default TrendChangesFromPreviousDate;
