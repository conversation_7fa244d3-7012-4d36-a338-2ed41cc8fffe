"use server";

import {
  IBApi,
  EventName,
  ErrorCode,
  Contract,
  IBApiNext,
  IBApiNextError,
  Stock,
  ContractDetails,
  SecType,
  OptionType,
  IBApiNextTickType,
  IBApiTickType,
  AccountSummariesUpdate,
  AccountSummaryTagValues,
  MarketDataUpdate,
  MarketDataTick,
  TickType,
} from "@stoqey/ib";
import { Subscription } from "rxjs";

// Sleep function to introduce a delay
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * @internal
 *
 * JSON replace function to convert ES6 Maps to tuple arrays.
 */
function jsonReplacer(key: any, value: any) {
  if (value instanceof Map) {
    const tuples: [unknown, unknown][] = [];
    value.forEach((v, k) => {
      tuples.push([k, v]);
    });
    return tuples;
  } else {
    return value;
  }
}

function printObject(obj: unknown): void {
  console.log(`${JSON.stringify(obj, jsonReplacer, 2)}`);
}

const stop = async (subscription$: Subscription, apiNext: IBApiNext) => {
  if (subscription$) subscription$.unsubscribe();
  if (apiNext) {
    apiNext.disconnect();
    console.log("stop(): unsubscribed and disconnected!");
  }
};

export async function getAccountSummary(): Promise<string[]> {
  console.log("getAccountSummary() started");
  // const clientId = Math.floor(Math.random() * 32766) + 1; // ensure unique client
  const clientId = 10;
  console.log(`client id: ${clientId}`);
  const apiNext = new IBApiNext({
    host: process.env.IB_HOST,
    port: process.env.IB_PORT,
  });
  // console.log("getAccountSummary():apiNext ->", apiNext);
  /** The [[Subscription]] on the account summary. */
  let subscription: Subscription;
  const accounts: string[] = [];
  try {
    const connectionPromise = new Promise((resolve, reject) => {
      // const timeout = setTimeout(() => {
      //   reject(new Error("Connection timeout"));
      // }, 10000); // 10 second timeout
      subscription = apiNext
        .connect(clientId)
        .getAccountSummary("All", "NetLiquidation")
        .subscribe({
          next: async (summaries: AccountSummariesUpdate) => {
            summaries.all.forEach(
              (value: AccountSummaryTagValues, key: string) => {
                accounts.push(key);
                resolve(accounts);
              },
            );
            console.log("getAccountSummary():accounts ->", accounts);
            await stop(subscription, apiNext);
          },
          error: (error: IBApiNextError) => {
            console.log(error.error.message);
          },
        });
    });
    await connectionPromise;
    return accounts;
  } catch (error) {
    console.error(error);
    return accounts;
  } finally {
    apiNext.disconnect();
  }
}

export async function getMarketData(): Promise<boolean> {
  const contractParams = {
    conid: 3691937,
    symbol: "AMZN",
    sectype: SecType.STK,
    currency: "USD",
    exchange: "SMART",
  };
  const contract = await createBasicContract(contractParams);
  const contractDetails = await getContractDetails(contract);
  const clientId = Math.floor(Math.random() * 32766) + 1;
  const apiNext = new IBApiNext();

  // Initialize subscription$ before use
  let subscription$: Subscription;

  try {
    subscription$ = apiNext
      .connect(clientId)
      .getMarketData(
        JSON.parse(contract) as Contract,
        "221" as string,
        false,
        false,
      )
      .subscribe({
        next: (marketData: MarketDataUpdate) => {
          const changedOrAddedDataWithTickNames = new Map<
            string,
            number | undefined
          >();

          marketData.added?.forEach((tick: MarketDataTick, type: TickType) => {
            if (type > IBApiNextTickType.API_NEXT_FIRST_TICK_ID) {
              const tickName = IBApiNextTickType[type];
              if (tickName) {
                changedOrAddedDataWithTickNames.set(tickName, tick.value);
              }
            } else {
              const tickName = IBApiTickType[type];
              if (tickName) {
                changedOrAddedDataWithTickNames.set(tickName, tick.value);
              }
            }
          });

          marketData.changed?.forEach(
            (tick: MarketDataTick, type: TickType) => {
              if (type > IBApiNextTickType.API_NEXT_FIRST_TICK_ID) {
                const tickName = IBApiNextTickType[type];
                if (tickName) {
                  changedOrAddedDataWithTickNames.set(tickName, tick.value);
                }
              } else {
                const tickName = IBApiTickType[type];
                if (tickName) {
                  changedOrAddedDataWithTickNames.set(tickName, tick.value);
                }
              }
            },
          );

          printObject(changedOrAddedDataWithTickNames);
        },
        error: async (err: IBApiNextError) => {
          await stop(subscription$, apiNext);
          console.error(
            `getMarketData failed with '${err.error.message}' (${err.code})`,
          );
        },
      });

    return true;
  } catch (error) {
    console.error("Failed to get market data:", error);
    return false;
  }
}

export async function getMarketDataSnapshot(): Promise<boolean> {
  const contractParams = {
    conid: 3691937,
    symbol: "AMZN",
    sectype: SecType.STK,
    currency: "USD",
    exchange: "SMART",
  };
  const contract = await createBasicContract(contractParams);
  const clientId = Math.floor(Math.random() * 32766) + 1; // ensure unique client
  console.log(clientId);
  const apiNext = new IBApiNext();
  apiNext
    .connect(clientId)
    .getMarketDataSnapshot(JSON.parse(contract) as Contract, "", false)
    .then((marketData: any) => {
      const dataWithTickNames = new Map<string, number | undefined>();
      marketData.forEach((tick: MarketDataTick, type: number) => {
        if (type > IBApiNextTickType.API_NEXT_FIRST_TICK_ID) {
          const tickName = IBApiNextTickType[type];
          if (tickName) {
            dataWithTickNames.set(tickName, tick.value);
          }
        } else {
          const tickName = IBApiTickType[type];
          if (tickName) {
            dataWithTickNames.set(tickName, tick.value);
          }
        }
      });
      printObject(dataWithTickNames);
    })
    .catch((err: IBApiNextError) => {
      console.error(`getMarketDataSingle failed with '${err.error.message}'`);
    });
  return true;
}

export async function getContractDetails(
  contractString: string,
): Promise<string> {
  const contract: Contract = JSON.parse(contractString);
  // const clientId = Math.floor(Math.random() * 32766) + 1;
  const clientId = 11;
  const apiNext = new IBApiNext();
  let contractDetails: ContractDetails[] = [];
  try {
    contractDetails = await apiNext
      .connect(clientId)
      .getContractDetails(contract);
  } catch (error) {
    console.error(error);
  } finally {
    if (apiNext) {
      apiNext.disconnect();
    }
    return JSON.stringify(contractDetails);
  }
}

// Define a type for the return value
type IBConnectResult = {
  accountNumber: string;
  positionsCount: number;
};

export async function ibStandardConnect(): Promise<IBConnectResult> {
  return new Promise((resolve, reject) => {
    const clientId = Math.floor(Math.random() * 32766) + 1; // ensure unique client
    console.log(
      `clientId: ${clientId}, host: ${process.env.IB_HOST}, port: ${process.env.IB_PORT}`,
    );
    const ib = new IBApi({
      clientId: clientId,
      host: process.env.IB_HOST,
      port: process.env.IB_PORT,
    });

    let positionsCount = 0;
    let accountNumber = "";

    ib.on(EventName.error, (err: Error, code: ErrorCode, reqId: number) => {
      console.error(`${err.message} - code: ${code} - reqId: ${reqId}`);
      if (ib && ib.isConnected) ib.disconnect();
      reject(err);
    })
      .on(
        EventName.position,
        (
          account: string,
          contract: Contract,
          pos: number,
          avgCost?: number,
        ) => {
          console.log(`${account}: ${pos} x ${contract.symbol} @ ${avgCost}`);
          if (!accountNumber) {
            accountNumber = account;
          }
          positionsCount++;
        },
      )
      .once(EventName.positionEnd, () => {
        console.log(`Total: ${positionsCount} positions.`);
        if (ib && ib.isConnected) ib.disconnect();
        resolve({ accountNumber, positionsCount });
      });

    ib.connect().reqPositions();
  });
}

type ContractParams = {
  conid: number;
  symbol: string;
  sectype: SecType | undefined;
  exchange: string | undefined;
  currency: string | undefined;
  localSymbol?: string | undefined;
  expiry?: string | undefined;
  strike?: number | undefined;
  right?: OptionType | undefined;
  multiplier?: number | undefined;
};

export async function createBasicContract({
  conid,
  symbol,
  sectype = SecType.STK,
  currency = "USD",
  exchange = "SMART",
  localSymbol = undefined,
  expiry = undefined,
  strike = undefined,
  right = undefined,
  multiplier = undefined,
}: ContractParams): Promise<string> {
  return JSON.stringify({
    conId: (conid as number) ?? undefined,
    secType: sectype as SecType,
    symbol: symbol as string,
    localSymbol: localSymbol as string,
    currency: currency,
    exchange: exchange,
    lastTradeDateOrContractMonth: (expiry as string) ?? undefined,
    strike: (strike as number) ?? undefined,
    right: (right as OptionType) ?? undefined,
    multiplier: (multiplier as number) ?? undefined,
  } as Contract);
}
